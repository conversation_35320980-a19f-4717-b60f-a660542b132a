<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bonc.ioc.parent</groupId>
        <artifactId>mcp-project-parent-nacos-mysql</artifactId>
        <version>1.1.11-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.bonc.ioc</groupId>
    <artifactId>bzf-business-payment</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <name>bzf-business-payment</name>
    <description>保障房-业务中台-缴费中心</description>

    <properties>
        <!-- 权限框架 分别有mcp-security-shiro 和 mcp-security-yh 和 mcp-security-none -->
        <!-- mcp-security-shiro 老框架中使用的 shiro -->
        <!-- mcp-security-yh 与炎黄对接 -->
        <!-- mcp-security-none 与不使用权限 -->
        <mcp-security.artifactId>mcp-security-yh</mcp-security.artifactId>
        <mcp-security.version>1.1.1-SNAPSHOT</mcp-security.version>
        <!-- 日志框架 分别有mcp-log-logback 和 mcp-log-log4j2 -->
        <mcp-log.artifactId>mcp-log-log4j2</mcp-log.artifactId>
        <javax.mail.version>1.5.0-b01</javax.mail.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <!--EasyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.4.3</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinovatech.rd.bms</groupId>
            <artifactId>bms-saas-api</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.11.4</version>
        </dependency>

        <dependency>
            <groupId>com.sinovatech.saas</groupId>
            <artifactId>saas-api-base</artifactId>
            <version>1.4.1.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.sinovatech</groupId>
            <artifactId>sino-um-timTask-springboot</artifactId>
            <version>2.3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version> <!-- 请根据需要选择合适的版本 -->
        </dependency>
    </dependencies>

    <repositories>
<!--        <repository>-->
<!--            <id>api</id>-->
<!--            <name>api Maven Repository</name>-->
<!--            <url>http://maven.sinovatech.com/nexus/repository/maven-public/</url>-->
<!--        </repository>-->
        <repository>
            <id>Public Repositories</id>
            <name>Maven Repository</name>
            <url>http://10.0.9.180:7083/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <!-- updataPolicy:配置maven从远程仓库检查更新的频率 -->
                <!-- 默认daily-maven每天检查一次 -->
                <!-- never-从不检查；-->
                <!-- always-每次构件都要检查更新；-->
                <!-- interval:X -每隔X分钟检查一次更新（X为整数） -->
                <updatePolicy>always</updatePolicy>
                <!-- checksumPolicy用来配置Maven检查校验和文件失败后的策略。 -->
                <!-- (默认值)warn-maven会执行构建时输出警告信息；-->
                <!-- fail-maven遇到校验和错处就让构建失败；-->
                <!-- ignore-使maven完全忽略校验和错误。 -->
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <!-- updataPolicy:配置maven从远程仓库检查更新的频率 -->
                <!-- 默认daily-maven每天检查一次 -->
                <!-- never-从不检查；-->
                <!-- always-每次构件都要检查更新；-->
                <!-- interval:X -每隔X分钟检查一次更新（X为整数） -->
                <updatePolicy>always</updatePolicy>
                <!-- checksumPolicy用来配置Maven检查校验和文件失败后的策略。 -->
                <!-- (默认值)warn-maven会执行构建时输出警告信息；-->
                <!-- fail-maven遇到校验和错处就让构建失败；-->
                <!-- ignore-使maven完全忽略校验和错误。 -->
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>license</id>
            <dependencies>
                <dependency>
                    <groupId>com.bonc.ioc</groupId>
                    <artifactId>mcp-license-client</artifactId>
                    <version>1.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <!-- 炎黄打包的参数 -->
        <profile>
            <id>yh</id>
            <properties>
                <mcp-security.artifactId>mcp-security-yh</mcp-security.artifactId>
                <mcp-log.artifactId>mcp-log-log4j2</mcp-log.artifactId>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>bzf-business-payment</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
