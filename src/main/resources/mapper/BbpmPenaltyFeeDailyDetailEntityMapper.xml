<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeDailyDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeDailyDetailEntity">
                            <id column="daily_detail_id" property="dailyDetailId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="calculation_date" property="calculationDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
                            <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                            <result column="daily_penalty_amount" property="dailyPenaltyAmount" javaType="BigDecimal"/>
                            <result column="entry_type" property="entryType" javaType="String"/>
                            <result column="adjustment_reason" property="adjustmentReason" javaType="String"/>
                            <result column="bill_status" property="billStatus" javaType="String"/>
                            <result column="account_status" property="accountStatus" javaType="String"/>
                            <result column="charge_time" property="chargeTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="original_entry_id" property="originalEntryId" javaType="String"/>
                            <result column="adjustment_seq" property="adjustmentSeq" javaType="Integer"/>
                            <result column="del_flag" property="delFlag" javaType="String"/>
                            <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeDailyDetailPageResultVo">
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="calculation_date" property="calculationDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="penalty_base_amount" property="penaltyBaseAmount" javaType="BigDecimal"/>
                        <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
                        <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                        <result column="daily_penalty_amount" property="dailyPenaltyAmount" javaType="BigDecimal"/>
                        <result column="entry_type" property="entryType" javaType="String"/>
                        <result column="adjustment_reason" property="adjustmentReason" javaType="String"/>
                        <result column="bill_status" property="billStatus" javaType="String"/>
                        <result column="account_status" property="accountStatus" javaType="String"/>
                        <result column="charge_time" property="chargeTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="original_entry_id" property="originalEntryId" javaType="String"/>
                        <result column="adjustment_seq" property="adjustmentSeq" javaType="Integer"/>
                        <result column="del_flag" property="delFlag" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 普通查询结果映射 -->
    <resultMap id="VoResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeDailyDetailVo">
                            <result column="daily_detail_id" property="dailyDetailId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="calculation_date" property="calculationDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="penalty_base_amount" property="penaltyBaseAmount" javaType="BigDecimal"/>
                        <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
                        <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                        <result column="daily_penalty_amount" property="dailyPenaltyAmount" javaType="BigDecimal"/>
                        <result column="entry_type" property="entryType" javaType="String"/>
                        <result column="adjustment_reason" property="adjustmentReason" javaType="String"/>
                        <result column="bill_status" property="billStatus" javaType="String"/>
                        <result column="account_status" property="accountStatus" javaType="String"/>
                        <result column="charge_time" property="chargeTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="original_entry_id" property="originalEntryId" javaType="String"/>
                        <result column="adjustment_seq" property="adjustmentSeq" javaType="Integer"/>
                        <result column="del_flag" property="delFlag" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.daily_detail_id
        ,base.bill_id
        ,base.calculation_date
        ,base.replace_pay_amount
        ,base.daily_overdue_rate
        ,base.daily_penalty_amount
        ,base.entry_type
        ,base.adjustment_reason
        ,base.bill_status
        ,base.account_status
        ,base.charge_time
        ,base.original_entry_id
        ,base.adjustment_seq
        ,base.del_flag
        ,base.project_id
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_fee_daily_detail base
        <where>
            <if test="'' != vo.dailyDetailId and vo.dailyDetailId != null">
                and base.daily_detail_id = #{vo.dailyDetailId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="vo.calculationDate != null">
                and base.calculation_date = #{vo.calculationDate}
            </if>
            <if test="vo.replacePayAmount != null">
                and base.replace_pay_amount = #{vo.replacePayAmount}
            </if>
            <if test="vo.dailyOverdueRate != null">
                and base.daily_overdue_rate = #{vo.dailyOverdueRate}
            </if>
            <if test="vo.dailyPenaltyAmount != null">
                and base.daily_penalty_amount = #{vo.dailyPenaltyAmount}
            </if>
            <if test="'' != vo.entryType and vo.entryType != null">
                and base.entry_type = #{vo.entryType}
            </if>
            <if test="'' != vo.adjustmentReason and vo.adjustmentReason != null">
                and base.adjustment_reason = #{vo.adjustmentReason}
            </if>
            <if test="'' != vo.billStatus and vo.billStatus != null">
                and base.bill_status = #{vo.billStatus}
            </if>
            <if test="'' != vo.accountStatus and vo.accountStatus != null">
                and base.account_status = #{vo.accountStatus}
            </if>
            <if test="vo.chargeTime != null">
                and base.charge_time = #{vo.chargeTime}
            </if>
            <if test="'' != vo.originalEntryId and vo.originalEntryId != null">
                and base.original_entry_id = #{vo.originalEntryId}
            </if>
            <if test="vo.adjustmentSeq != null">
                and base.adjustment_seq = #{vo.adjustmentSeq}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
    </select>

    <!-- 直接查询方法，不使用分页框架 -->
    <select id="selectListByCondition" resultMap="VoResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_fee_daily_detail base
        <where>
            <if test="'' != vo.dailyDetailId and vo.dailyDetailId != null">
                and base.daily_detail_id = #{vo.dailyDetailId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="vo.calculationDate != null">
                and base.calculation_date = #{vo.calculationDate}
            </if>
            <if test="vo.penaltyBaseAmount != null">
                and base.penalty_base_amount = #{vo.penaltyBaseAmount}
            </if>
            <if test="vo.replacePayAmount != null">
                and base.replace_pay_amount = #{vo.replacePayAmount}
            </if>
            <if test="vo.dailyOverdueRate != null">
                and base.daily_overdue_rate = #{vo.dailyOverdueRate}
            </if>
            <if test="vo.dailyPenaltyAmount != null">
                and base.daily_penalty_amount = #{vo.dailyPenaltyAmount}
            </if>
            <if test="'' != vo.entryType and vo.entryType != null">
                and base.entry_type = #{vo.entryType}
            </if>
            <if test="'' != vo.adjustmentReason and vo.adjustmentReason != null">
                and base.adjustment_reason = #{vo.adjustmentReason}
            </if>
            <if test="'' != vo.billStatus and vo.billStatus != null">
                and base.bill_status = #{vo.billStatus}
            </if>
            <if test="'' != vo.accountStatus and vo.accountStatus != null">
                and base.account_status = #{vo.accountStatus}
            </if>
            <if test="vo.chargeTime != null">
                and base.charge_time = #{vo.chargeTime}
            </if>
            <if test="'' != vo.originalEntryId and vo.originalEntryId != null">
                and base.original_entry_id = #{vo.originalEntryId}
            </if>
            <if test="vo.adjustmentSeq != null">
                and base.adjustment_seq = #{vo.adjustmentSeq}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
        ORDER BY base.calculation_date DESC
    </select>
</mapper>
