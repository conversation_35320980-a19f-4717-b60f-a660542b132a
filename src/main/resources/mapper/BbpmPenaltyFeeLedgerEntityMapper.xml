<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeLedgerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeLedgerEntity">
                            <id column="ledger_id" property="ledgerId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="penalty_disposal_no" property="penaltyDisposalNo" javaType="String"/>
                            <result column="status" property="status" javaType="Integer"/>
                            <result column="project_id" property="projectId" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="tenant_code" property="tenantCode" javaType="String"/>
                            <result column="tenant_name" property="tenantName" javaType="String"/>
                            <result column="house_name" property="houseName" javaType="String"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="disposal_type" property="disposalType" javaType="String"/>
                            <result column="reduction_amount" property="reductionAmount" javaType="BigDecimal"/>
                            <result column="bill_generation_amount" property="billGenerationAmount" javaType="BigDecimal"/>
                            <result column="disposal_basis_file_id" property="disposalBasisFileId" javaType="String"/>
                            <result column="disposal_basis_description" property="disposalBasisDescription" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeLedgerPageResultVo">
                        <result column="penalty_disposal_no" property="penaltyDisposalNo" javaType="String"/>
                        <result column="status" property="status" javaType="Integer"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="tenant_code" property="tenantCode" javaType="String"/>
                        <result column="tenant_name" property="tenantName" javaType="String"/>
                        <result column="house_name" property="houseName" javaType="String"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="disposal_type" property="disposalType" javaType="String"/>
                        <result column="reduction_amount" property="reductionAmount" javaType="BigDecimal"/>
                        <result column="bill_generation_amount" property="billGenerationAmount" javaType="BigDecimal"/>
                        <result column="disposal_basis_file_id" property="disposalBasisFileId" javaType="String"/>
                        <result column="disposal_basis_description" property="disposalBasisDescription" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.ledger_id
        ,base.penalty_disposal_no
        ,base.status
        ,base.project_id
        ,base.project_name
        ,base.tenant_code
        ,base.tenant_name
        ,base.house_name
        ,base.contract_code
        ,base.disposal_type
        ,base.reduction_amount
        ,base.bill_generation_amount
        ,base.disposal_basis_file_id
        ,base.disposal_basis_description
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_fee_ledger base
        <where>
            <if test="'' != vo.ledgerId and vo.ledgerId != null">
                and base.ledger_id = #{vo.ledgerId}
            </if>
            <if test="'' != vo.penaltyDisposalNo and vo.penaltyDisposalNo != null">
                and base.penalty_disposal_no = #{vo.penaltyDisposalNo}
            </if>
            <if test="vo.status != null">
                and base.status = #{vo.status}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.tenantCode and vo.tenantCode != null">
                and base.tenant_code = #{vo.tenantCode}
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name = #{vo.tenantName}
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name = #{vo.houseName}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.disposalType and vo.disposalType != null">
                and base.disposal_type = #{vo.disposalType}
            </if>
            <if test="vo.reductionAmount != null">
                and base.reduction_amount = #{vo.reductionAmount}
            </if>
            <if test="vo.billGenerationAmount != null">
                and base.bill_generation_amount = #{vo.billGenerationAmount}
            </if>
            <if test="'' != vo.disposalBasisFileId and vo.disposalBasisFileId != null">
                and base.disposal_basis_file_id = #{vo.disposalBasisFileId}
            </if>
            <if test="'' != vo.disposalBasisDescription and vo.disposalBasisDescription != null">
                and base.disposal_basis_description = #{vo.disposalBasisDescription}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
