<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyApiCallLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyApiCallLogEntity">
                            <id column="api_call_log_id" property="apiCallLogId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="task_status_id" property="taskStatusId" javaType="String"/>
                            <result column="task_date" property="taskDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="api_type" property="apiType" javaType="String"/>
                            <result column="api_url" property="apiUrl" javaType="String"/>
                            <result column="request_params" property="requestParams" javaType="String"/>
                            <result column="response_data" property="responseData" javaType="String"/>
                            <result column="call_duration" property="callDuration" javaType="Integer"/>
                            <result column="call_status" property="callStatus" javaType="String"/>
                            <result column="error_message" property="errorMessage" javaType="String"/>
                            <result column="call_time" property="callTime" javaType="Date"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyApiCallLogPageResultVo">
                        <result column="task_status_id" property="taskStatusId" javaType="String"/>
                        <result column="task_date" property="taskDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="api_type" property="apiType" javaType="String"/>
                        <result column="api_url" property="apiUrl" javaType="String"/>
                        <result column="request_params" property="requestParams" javaType="String"/>
                        <result column="response_data" property="responseData" javaType="String"/>
                        <result column="call_duration" property="callDuration" javaType="Integer"/>
                        <result column="call_status" property="callStatus" javaType="String"/>
                        <result column="error_message" property="errorMessage" javaType="String"/>
                        <result column="call_time" property="callTime" javaType="Date"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.api_call_log_id
        ,base.task_status_id
        ,base.task_date
        ,base.contract_code
        ,base.bill_id
        ,base.api_type
        ,base.api_url
        ,base.request_params
        ,base.response_data
        ,base.call_duration
        ,base.call_status
        ,base.error_message
        ,base.call_time
        ,base.del_flag
        ,base.project_id
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_api_call_log base
        <where>
            <if test="'' != vo.apiCallLogId and vo.apiCallLogId != null">
                and base.api_call_log_id = #{vo.apiCallLogId}
            </if>
            <if test="'' != vo.taskStatusId and vo.taskStatusId != null">
                and base.task_status_id = #{vo.taskStatusId}
            </if>
            <if test="vo.taskDate != null">
                and base.task_date = #{vo.taskDate}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.apiType and vo.apiType != null">
                and base.api_type = #{vo.apiType}
            </if>
            <if test="'' != vo.apiUrl and vo.apiUrl != null">
                and base.api_url = #{vo.apiUrl}
            </if>
            <if test="'' != vo.requestParams and vo.requestParams != null">
                and base.request_params = #{vo.requestParams}
            </if>
            <if test="'' != vo.responseData and vo.responseData != null">
                and base.response_data = #{vo.responseData}
            </if>
            <if test="vo.callDuration != null">
                and base.call_duration = #{vo.callDuration}
            </if>
            <if test="'' != vo.callStatus and vo.callStatus != null">
                and base.call_status = #{vo.callStatus}
            </if>
            <if test="'' != vo.errorMessage and vo.errorMessage != null">
                and base.error_message = #{vo.errorMessage}
            </if>
            <if test="vo.callTime != null">
                and base.call_time = #{vo.callTime}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
    </select>
</mapper>
