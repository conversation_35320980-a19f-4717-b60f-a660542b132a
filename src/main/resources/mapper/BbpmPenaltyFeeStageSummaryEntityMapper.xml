<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeStageSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeStageSummaryEntity">
                            <id column="stage_summary_id" property="stageSummaryId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="trial_bill_id" property="trialBillId" javaType="String"/>
                            <result column="penalty_start_date" property="penaltyStartDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="penalty_end_date" property="penaltyEndDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="overdue_days" property="overdueDays" javaType="Integer"/>
                            <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                            <result column="unpaid_amount" property="unpaidAmount" javaType="BigDecimal"/>
                            <result column="stage_penalty_amount" property="stagePenaltyAmount" javaType="BigDecimal"/>
                            <result column="del_flag" property="delFlag" javaType="String"/>
                            <result column="project_id" property="projectId" javaType="String"/>
        <result column="current_stage" property="currentStage" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeStageSummaryPageResultVo">
                        <result column="trial_bill_id" property="trialBillId" javaType="String"/>
                        <result column="penalty_start_date" property="penaltyStartDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="penalty_end_date" property="penaltyEndDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="overdue_days" property="overdueDays" javaType="Integer"/>
                        <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                        <result column="unpaid_amount" property="unpaidAmount" javaType="BigDecimal"/>
                        <result column="stage_penalty_amount" property="stagePenaltyAmount" javaType="BigDecimal"/>
                        <result column="del_flag" property="delFlag" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
        <result column="current_stage" property="currentStage" javaType="String"/>
    </resultMap>

    <!-- 普通查询结果映射 -->
    <resultMap id="VoResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeStageSummaryVo">
                            <result column="stage_summary_id" property="stageSummaryId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                        <result column="trial_bill_id" property="trialBillId" javaType="String"/>
                        <result column="penalty_start_date" property="penaltyStartDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="penalty_end_date" property="penaltyEndDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="overdue_days" property="overdueDays" javaType="Integer"/>
                        <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                        <result column="unpaid_amount" property="unpaidAmount" javaType="BigDecimal"/>
                        <result column="stage_penalty_amount" property="stagePenaltyAmount" javaType="BigDecimal"/>
                        <result column="del_flag" property="delFlag" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="current_stage" property="currentStage" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.stage_summary_id
        ,base.trial_bill_id
        ,base.penalty_start_date
        ,base.penalty_end_date
        ,base.overdue_days
        ,base.daily_overdue_rate
        ,base.unpaid_amount
        ,base.stage_penalty_amount
        ,base.del_flag
        ,base.project_id
        ,base.current_stage
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_fee_stage_summary base
        <where>
            <if test="'' != vo.stageSummaryId and vo.stageSummaryId != null">
                and base.stage_summary_id = #{vo.stageSummaryId}
            </if>
            <if test="'' != vo.trialBillId and vo.trialBillId != null">
                and base.trial_bill_id = #{vo.trialBillId}
            </if>
            <if test="vo.penaltyStartDate != null">
                and base.penalty_start_date = #{vo.penaltyStartDate}
            </if>
            <if test="vo.penaltyEndDate != null">
                and base.penalty_end_date = #{vo.penaltyEndDate}
            </if>
            <if test="vo.overdueDays != null">
                and base.overdue_days = #{vo.overdueDays}
            </if>
            <if test="vo.dailyOverdueRate != null">
                and base.daily_overdue_rate = #{vo.dailyOverdueRate}
            </if>
            <if test="vo.unpaidAmount != null">
                and base.unpaid_amount = #{vo.unpaidAmount}
            </if>
            <if test="vo.stagePenaltyAmount != null">
                and base.stage_penalty_amount = #{vo.stagePenaltyAmount}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
    </select>

    <!-- 直接查询方法，不使用分页框架 -->
    <select id="selectListByCondition" resultMap="VoResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_fee_stage_summary base
        <where>
            <if test="'' != vo.stageSummaryId and vo.stageSummaryId != null">
                and base.stage_summary_id = #{vo.stageSummaryId}
            </if>
            <if test="'' != vo.trialBillId and vo.trialBillId != null">
                and base.trial_bill_id = #{vo.trialBillId}
            </if>
            <if test="vo.penaltyStartDate != null">
                and base.penalty_start_date = #{vo.penaltyStartDate}
            </if>
            <if test="vo.penaltyEndDate != null">
                and base.penalty_end_date = #{vo.penaltyEndDate}
            </if>
            <if test="vo.overdueDays != null">
                and base.overdue_days = #{vo.overdueDays}
            </if>
            <if test="vo.dailyOverdueRate != null">
                and base.daily_overdue_rate = #{vo.dailyOverdueRate}
            </if>
            <if test="vo.unpaidAmount != null">
                and base.unpaid_amount = #{vo.unpaidAmount}
            </if>
            <if test="vo.stagePenaltyAmount != null">
                and base.stage_penalty_amount = #{vo.stagePenaltyAmount}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.currentStage and vo.currentStage != null">
                and base.current_stage = #{vo.currentStage}
            </if>
        </where>
        ORDER BY base.penalty_start_date DESC
    </select>
</mapper>
