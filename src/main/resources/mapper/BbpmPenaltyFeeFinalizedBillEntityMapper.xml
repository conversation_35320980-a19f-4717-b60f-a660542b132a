<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeFinalizedBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeFinalizedBillEntity">
                            <id column="finalized_bill_id" property="finalizedBillId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="trial_bill_id" property="trialBillId" javaType="String"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="bill_cycle" property="billCycle" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="house_name" property="houseName" javaType="String"/>
                            <result column="tenant_code" property="tenantCode" javaType="String"/>
                            <result column="tenant_name" property="tenantName" javaType="String"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>
                            <result column="payable_date" property="payableDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="should_pay_amount" property="shouldPayAmount" javaType="BigDecimal"/>
                            <result column="payed_amount" property="payedAmount" javaType="BigDecimal"/>
                            <result column="actual_pay_date" property="actualPayDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
                            <result column="overdue_days" property="overdueDays" javaType="Integer"/>
                            <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                            <result column="total_penalty_amount" property="totalPenaltyAmount" javaType="BigDecimal"/>
                            <result column="reduction_amount" property="reductionAmount" javaType="BigDecimal"/>
                            <result column="bill_generation_amount" property="billGenerationAmount" javaType="BigDecimal"/>
                            <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
                            <result column="finalized_reason" property="finalizedReason" javaType="String"/>
                            <result column="finalized_date" property="finalizedDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="penalty_disposal_no" property="penaltyDisposalNo" javaType="String"/>
                            <result column="disposal_status" property="disposalStatus" javaType="String"/>
                            <result column="disposal_type" property="disposalType" javaType="String"/>
                            <result column="process_time" property="processTime" javaType="Date"/>
                            <result column="process_remark" property="processRemark" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="String"/>
                            <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeFinalizedBillPageResultVo">
                        <result column="trial_bill_id" property="trialBillId" javaType="String"/>
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="bill_cycle" property="billCycle" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="house_name" property="houseName" javaType="String"/>
                        <result column="tenant_code" property="tenantCode" javaType="String"/>
                        <result column="tenant_name" property="tenantName" javaType="String"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>
                        <result column="payable_date" property="payableDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="should_pay_amount" property="shouldPayAmount" javaType="BigDecimal"/>
                        <result column="payed_amount" property="payedAmount" javaType="BigDecimal"/>
                        <result column="actual_pay_date" property="actualPayDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
                        <result column="overdue_days" property="overdueDays" javaType="Integer"/>
                        <result column="daily_overdue_rate" property="dailyOverdueRate" javaType="BigDecimal"/>
                        <result column="total_penalty_amount" property="totalPenaltyAmount" javaType="BigDecimal"/>
                        <result column="reduction_amount" property="reductionAmount" javaType="BigDecimal"/>
                        <result column="bill_generation_amount" property="billGenerationAmount" javaType="BigDecimal"/>
                        <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
                        <result column="finalized_reason" property="finalizedReason" javaType="String"/>
                        <result column="finalized_date" property="finalizedDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="penalty_disposal_no" property="penaltyDisposalNo" javaType="String"/>
                        <result column="disposal_status" property="disposalStatus" javaType="String"/>
                        <result column="disposal_type" property="disposalType" javaType="String"/>
                        <result column="process_time" property="processTime" javaType="Date"/>
                        <result column="process_remark" property="processRemark" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.finalized_bill_id
        ,base.trial_bill_id
        ,base.bill_id
        ,base.bill_cycle
        ,base.project_name
        ,base.house_name
        ,base.tenant_code
        ,base.tenant_name
        ,base.contract_code
        ,base.charge_subject_begin_date
        ,base.charge_subject_end_date
        ,base.charge_subject_period
        ,base.payable_date
        ,base.should_pay_amount
        ,base.payed_amount
        ,base.actual_pay_date
        ,base.replace_pay_amount
        ,base.overdue_days
        ,base.daily_overdue_rate
        ,base.total_penalty_amount
        ,base.reduction_amount
        ,base.bill_generation_amount
        ,base.bill_charge_subject
        ,base.finalized_reason
        ,base.finalized_date
        ,base.penalty_disposal_no
        ,base.disposal_status
        ,base.disposal_type
        ,base.process_time
        ,base.process_remark
        ,base.del_flag
        ,base.project_id
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_fee_finalized_bill base
        <where>
            <if test="'' != vo.finalizedBillId and vo.finalizedBillId != null">
                and base.finalized_bill_id = #{vo.finalizedBillId}
            </if>
            <if test="'' != vo.trialBillId and vo.trialBillId != null">
                and base.trial_bill_id = #{vo.trialBillId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.billCycle and vo.billCycle != null">
                and base.bill_cycle = #{vo.billCycle}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name = #{vo.houseName}
            </if>
            <if test="'' != vo.tenantCode and vo.tenantCode != null">
                and base.tenant_code = #{vo.tenantCode}
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name = #{vo.tenantName}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="vo.chargeSubjectBeginDate != null">
                and base.charge_subject_begin_date = #{vo.chargeSubjectBeginDate}
            </if>
            <if test="vo.chargeSubjectEndDate != null">
                and base.charge_subject_end_date = #{vo.chargeSubjectEndDate}
            </if>
            <if test="vo.chargeSubjectPeriod != null">
                and base.charge_subject_period = #{vo.chargeSubjectPeriod}
            </if>
            <if test="vo.payableDate != null">
                and base.payable_date = #{vo.payableDate}
            </if>
            <if test="vo.shouldPayAmount != null">
                and base.should_pay_amount = #{vo.shouldPayAmount}
            </if>
            <if test="vo.payedAmount != null">
                and base.payed_amount = #{vo.payedAmount}
            </if>
            <if test="vo.actualPayDate != null">
                and base.actual_pay_date = #{vo.actualPayDate}
            </if>
            <if test="vo.replacePayAmount != null">
                and base.replace_pay_amount = #{vo.replacePayAmount}
            </if>
            <if test="vo.overdueDays != null">
                and base.overdue_days = #{vo.overdueDays}
            </if>
            <if test="vo.dailyOverdueRate != null">
                and base.daily_overdue_rate = #{vo.dailyOverdueRate}
            </if>
            <if test="vo.totalPenaltyAmount != null">
                and base.total_penalty_amount = #{vo.totalPenaltyAmount}
            </if>
            <if test="vo.reductionAmount != null">
                and base.reduction_amount = #{vo.reductionAmount}
            </if>
            <if test="vo.billGenerationAmount != null">
                and base.bill_generation_amount = #{vo.billGenerationAmount}
            </if>
            <if test="'' != vo.billChargeSubject and vo.billChargeSubject != null">
                and base.bill_charge_subject = #{vo.billChargeSubject}
            </if>
            <if test="'' != vo.finalizedReason and vo.finalizedReason != null">
                and base.finalized_reason = #{vo.finalizedReason}
            </if>
            <if test="vo.finalizedDate != null">
                and base.finalized_date = #{vo.finalizedDate}
            </if>
            <if test="'' != vo.penaltyDisposalNo and vo.penaltyDisposalNo != null">
                and base.penalty_disposal_no = #{vo.penaltyDisposalNo}
            </if>
            <if test="'' != vo.disposalStatus and vo.disposalStatus != null">
                and base.disposal_status = #{vo.disposalStatus}
            </if>
            <if test="'' != vo.disposalType and vo.disposalType != null">
                and base.disposal_type = #{vo.disposalType}
            </if>
            <if test="vo.processTime != null">
                and base.process_time = #{vo.processTime}
            </if>
            <if test="'' != vo.processRemark and vo.processRemark != null">
                and base.process_remark = #{vo.processRemark}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
    </select>
</mapper>
