<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyTaskStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyTaskStatusEntity">
                            <id column="task_status_id" property="taskStatusId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="task_date" property="taskDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="process_status" property="processStatus" javaType="String"/>
                            <result column="bill_count" property="billCount" javaType="Integer"/>
                            <result column="start_time" property="startTime" javaType="Date"/>
                            <result column="end_time" property="endTime" javaType="Date"/>
                            <result column="error_message" property="errorMessage" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyTaskStatusPageResultVo">
                        <result column="task_date" property="taskDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="process_status" property="processStatus" javaType="String"/>
                        <result column="bill_count" property="billCount" javaType="Integer"/>
                        <result column="start_time" property="startTime" javaType="Date"/>
                        <result column="end_time" property="endTime" javaType="Date"/>
                        <result column="error_message" property="errorMessage" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 普通查询结果映射 -->
    <resultMap id="VoResultMap" type="com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyTaskStatusVo">
                            <result column="task_status_id" property="taskStatusId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="task_date" property="taskDate" javaType="java.time.LocalDate" jdbcType="DATE"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="process_status" property="processStatus" javaType="String"/>
                        <result column="bill_count" property="billCount" javaType="Integer"/>
                        <result column="start_time" property="startTime" javaType="Date"/>
                        <result column="end_time" property="endTime" javaType="Date"/>
                        <result column="error_message" property="errorMessage" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.task_status_id
        ,base.task_date
        ,base.contract_code
        ,base.process_status
        ,base.bill_count
        ,base.start_time
        ,base.end_time
        ,base.error_message
        ,base.del_flag
        ,base.project_id
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_task_status base
        <where>
            <if test="'' != vo.taskStatusId and vo.taskStatusId != null">
                and base.task_status_id = #{vo.taskStatusId}
            </if>
            <if test="vo.taskDate != null">
                and base.task_date = #{vo.taskDate}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.processStatus and vo.processStatus != null">
                and base.process_status = #{vo.processStatus}
            </if>
            <if test="vo.billCount != null">
                and base.bill_count = #{vo.billCount}
            </if>
            <if test="vo.startTime != null">
                and base.start_time = #{vo.startTime}
            </if>
            <if test="vo.endTime != null">
                and base.end_time = #{vo.endTime}
            </if>
            <if test="'' != vo.errorMessage and vo.errorMessage != null">
                and base.error_message = #{vo.errorMessage}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
    </select>

    <!-- 直接查询方法，不使用分页框架 -->
    <select id="selectListByCondition" resultMap="VoResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_penalty_task_status base
        <where>
            <if test="'' != vo.taskStatusId and vo.taskStatusId != null">
                and base.task_status_id = #{vo.taskStatusId}
            </if>
            <if test="vo.taskDate != null">
                and base.task_date = #{vo.taskDate}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.processStatus and vo.processStatus != null">
                and base.process_status = #{vo.processStatus}
            </if>
            <if test="vo.billCount != null">
                and base.bill_count = #{vo.billCount}
            </if>
            <if test="vo.startTime != null">
                and base.start_time = #{vo.startTime}
            </if>
            <if test="vo.endTime != null">
                and base.end_time = #{vo.endTime}
            </if>
            <if test="'' != vo.errorMessage and vo.errorMessage != null">
                and base.error_message = #{vo.errorMessage}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
        ORDER BY base.create_time DESC
    </select>
</mapper>
