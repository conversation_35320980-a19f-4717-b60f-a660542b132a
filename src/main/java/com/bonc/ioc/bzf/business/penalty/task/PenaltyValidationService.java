package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeDailyDetailService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeFinalizedBillService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeTrialBillService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeDailyDetailVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeFinalizedBillVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillComparisonResultVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo;
import com.bonc.ioc.common.base.page.PageResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金验证和调整逻辑服务类
 * 负责账单状态验证、违约金调整逻辑、数据完整性检查、自动完结等功能
 */
@Slf4j
@Service
public class PenaltyValidationService {

    @Resource
    private IBbpmPenaltyFeeTrialBillService penaltyFeeTrialBillService;
    
    @Resource
    private IBbpmPenaltyFeeDailyDetailService penaltyFeeDailyDetailService;
    
    @Resource
    private IBbpmPenaltyFeeFinalizedBillService penaltyFeeFinalizedBillService;
    
    @Resource
    private PenaltyDataService penaltyDataService;
    
    @Resource
    @Lazy
    private PenaltyCalculationService penaltyCalculationService;

    @Resource
    @Lazy
    private PenaltySummaryService penaltySummaryService;

    /**
     * 对比工银账单与库中试算账单，找出新增、变化、无变化的账单
     */
    public BillComparisonResultVo compareBillsWithDatabase(
            List<BbpmBillManagementPageResultVo> allBills, 
            Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap,
            Map<String, List<BbpmCollectionPageResultVo>> billPaymentsMap,
            LocalDate calculationDate,
            BigDecimal dailyOverdueRate,
            String projectId) {
        
        List<BbpmBillManagementPageResultVo> newBills = new ArrayList<>();
        List<BillStatusComparisonVo> statusChangedBills = new ArrayList<>();
        List<BbpmBillManagementPageResultVo> unchangedBills = new ArrayList<>();
        
        for (BbpmBillManagementPageResultVo bill : allBills) {
            String billId = bill.getBillId();
            BbpmPenaltyFeeTrialBillVo existingTrialBill = existingTrialBillMap.get(billId);
            
            if (existingTrialBill == null) {
                // 新增账单（库中完全没有记录）
                // 优化：首次执行时，已缴足额支付的账单无需统计
                if ("01".equals(bill.getBillStatus())) {
                    log.debug("新增账单 {} 已缴足额支付（billStatus=01），跳过违约金计算", billId);
                } else {
                    newBills.add(bill);
                    log.debug("发现新增账单：{}", billId);
                }
            } else {
                // 库中已存在记录，根据试算账单状态判断处理方式
                String trialBillStatus = existingTrialBill.getStatus();
                
                if (!"1".equals(trialBillStatus)) {
                    // 试算账单已完结（状态2）或已取消（状态3），但账单重新出现
                    if ("2".equals(trialBillStatus)) {
                        // 优化：已完结的账单重新出现，但如果已缴足额支付，则无需重新处理
                        if ("01".equals(bill.getBillStatus())) {
                            log.debug("已完结账单 {} 重新出现但已缴足额支付（billStatus=01），跳过处理", billId);
                        } else {
                            log.info("账单 {} 对应的试算账单已完结，但账单重新出现，当作新账单处理", billId);
                            newBills.add(bill);
                        }
                    } else {
                        // 处理未定义的试算账单状态（如状态3等）
                        log.warn("账单 {} 对应的试算账单状态异常：{}，当作无变化账单处理", billId, trialBillStatus);
                        // 状态异常的账单，如果已缴足额支付，也跳过处理
                        if (!"01".equals(bill.getBillStatus())) {
                            unchangedBills.add(bill);
                        }
                    }
                } else {
                    // 试算账单正在试算中（状态1），检查对账状态是否变化
                    try {
                        // 使用预先查询的收款单信息（优化：避免重复查询）
                        List<BbpmCollectionPageResultVo> currentPayments = 
                            billPaymentsMap.getOrDefault(billId, new ArrayList<>());
                        
                        String currentStatus = penaltyDataService.analyzeReconciliationStatus(currentPayments);
                        String savedStatus = existingTrialBill.getAccountStatus();
                        
                        // 检测账单缴费状态变化
                        String currentBillStatus = bill.getBillStatus();
                        String savedBillStatus = existingTrialBill.getBillStatus();
                        
                        boolean accountStatusChanged = !Objects.equals(currentStatus, savedStatus);
                        boolean billStatusChanged = !Objects.equals(currentBillStatus, savedBillStatus);
                        
                        if (accountStatusChanged || billStatusChanged) {
                            // 对账状态或账单缴费状态发生变化
                            statusChangedBills.add(new BillStatusComparisonVo(
                                bill, existingTrialBill, savedStatus, currentStatus, currentPayments
                            ));
                            if (accountStatusChanged && billStatusChanged) {
                                log.debug("发现状态变化账单：{} 对账状态({}->{}), 缴费状态({}->{})", 
                                    billId, savedStatus, currentStatus, savedBillStatus, currentBillStatus);
                            } else if (accountStatusChanged) {
                                log.debug("发现状态变化账单：{} 对账状态({}->{})", billId, savedStatus, currentStatus);
                            } else {
                                log.debug("发现状态变化账单：{} 缴费状态({}->{})", billId, savedBillStatus, currentBillStatus);
                            }
                        } else {
                            // 对账状态无变化
                            // 优化：如果账单已缴足额支付，无需进行每日计算
                            if ("01".equals(bill.getBillStatus())) {
                                log.debug("无变化账单 {} 已缴足额支付（billStatus=01），跳过每日计算", billId);
                            } else {
                                unchangedBills.add(bill);
                                log.debug("账单状态无变化：{} ({})", billId, currentStatus);
                            }
                        }
                    } catch (Exception e) {
                        log.error("检查账单 {} 状态失败：{}", billId, e.getMessage(), e);
                        // 出错时当作无变化账单处理
                        unchangedBills.add(bill);
                    }
                }
            }
        }
        
        return new BillComparisonResultVo(newBills, statusChangedBills, unchangedBills);
    }

    /**
     * 确保试算账单记录存在（增强版本）
     */
    public void ensureTrialBillExists(BbpmBillManagementPageResultVo bill, 
                                     List<BbpmCollectionPageResultVo> payments, 
                                     String reconciliationStatus, 
                                     LocalDate earliestReceiptDate,
                                     BigDecimal dailyOverdueRate) {
        try {
            // 1. 检查试算账单是否已存在
            BbpmPenaltyFeeTrialBillVo existingBill = null;
            try {
                existingBill = penaltyDataService.selectTrialBillByBillId(bill.getBillId());
            } catch (Exception e) {
                log.debug("查询试算账单时出错：{}", e.getMessage());
            }
            
            if (existingBill == null) {
                // 2. 创建新的试算账单记录
                BbpmPenaltyFeeTrialBillVo trialBill = new BbpmPenaltyFeeTrialBillVo();
                
                // 设置试算账单ID
                trialBill.setTrialBillId(UUID.randomUUID().toString().replace("-", ""));
                
                // 设置工银账单基本信息
                trialBill.setBillId(bill.getBillId());
                trialBill.setBillCycle(bill.getBillCycle());
                trialBill.setProjectName(bill.getProjectName());
                trialBill.setHouseName(bill.getHouseName());
                trialBill.setTenantCode(bill.getTenantCode());
                trialBill.setTenantName(bill.getTenantName());
                trialBill.setContractCode(bill.getContractNo());
                trialBill.setChargeSubjectBeginDate(penaltyCalculationService.parseDate(bill.getChargeSubjectBeginDate()));
                trialBill.setChargeSubjectEndDate(penaltyCalculationService.parseDate(bill.getChargeSubjectEndDate()));
                trialBill.setChargeSubjectPeriod(bill.getChargeSubjectPeriod());
                trialBill.setPayableDate(penaltyCalculationService.parseDate(bill.getPayableDate()));
                trialBill.setShouldPayAmount(penaltyCalculationService.parseBigDecimal(bill.getShouldPayAmount()));
                trialBill.setPayedAmount(penaltyCalculationService.parseBigDecimal(bill.getPayedAmount()));
                trialBill.setReplacePayAmount(penaltyCalculationService.parseBigDecimal(bill.getReplacePayAmount()));
                trialBill.setBillChargeSubject(bill.getBillChargeSubject());
                trialBill.setProjectId(bill.getProjectId());
                
                // 设置账单状态和对账状态
                trialBill.setBillStatus(bill.getBillStatus());
                trialBill.setAccountStatus(reconciliationStatus);
                
                // 计算逾期天数
                LocalDate payableDate = penaltyCalculationService.parseDate(bill.getPayableDate());
                if (payableDate != null) {
                    int overdueDays = (int) java.time.temporal.ChronoUnit.DAYS.between(payableDate, LocalDate.now());
                    trialBill.setOverdueDays(Math.max(0, overdueDays));
                }
                
                // 设置每日逾期率
                trialBill.setDailyOverdueRate(dailyOverdueRate);
                
                // 设置试算账单状态
                trialBill.setStatus("1"); // 1-试算中
                trialBill.setDelFlag("1");
                
                // 3. 保存试算账单记录
                penaltyFeeTrialBillService.insertRecord(trialBill);
                
                log.info("创建试算账单记录：账单ID={}, 试算账单ID={}, 合同号={}, 应缴金额={}", 
                        bill.getBillId(), trialBill.getTrialBillId(), bill.getContractNo(), 
                        trialBill.getShouldPayAmount());
                        
            } else {
                // 4. 更新已存在的试算账单信息（如对账状态等可能变化的字段）
                boolean needUpdate = false;
                
                if (!Objects.equals(existingBill.getAccountStatus(), reconciliationStatus)) {
                    existingBill.setAccountStatus(reconciliationStatus);
                    needUpdate = true;
                }
                
                if (!Objects.equals(existingBill.getBillStatus(), bill.getBillStatus())) {
                    existingBill.setBillStatus(bill.getBillStatus());
                    needUpdate = true;
                }
                
                BigDecimal currentPayedAmount = penaltyCalculationService.parseBigDecimal(bill.getPayedAmount());
                if (existingBill.getPayedAmount() != null && 
                    existingBill.getPayedAmount().compareTo(currentPayedAmount) != 0) {
                    existingBill.setPayedAmount(currentPayedAmount);
                    existingBill.setReplacePayAmount(penaltyCalculationService.parseBigDecimal(bill.getReplacePayAmount()));
                    needUpdate = true;
                }
                
                if (needUpdate) {
                    // 更新试算账单
                    penaltyFeeTrialBillService.updateByIdRecord(existingBill);
                    log.debug("更新试算账单记录：{}", bill.getBillId());
                }
            }
            
        } catch (Exception e) {
            log.error("确保试算账单存在时发生异常：{}", e.getMessage(), e);
        }
    }

    /**
     * 检查是否需要自动完结账单
     */
    public boolean checkAndAutoFinalize(String billId) {
        try {
            // 1. 查询试算账单信息
            BbpmPenaltyFeeTrialBillVo trialBill = penaltyDataService.selectTrialBillByBillId(billId);
            if (trialBill == null || !"1".equals(trialBill.getStatus())) {
                log.debug("试算账单不存在或已完结：{}", billId);
                return false;
            }
            
            // 2. 检查自动完结条件
            boolean shouldAutoFinalize = false;
            String finalizeReason = null;
            
            // 条件1：账单状态为已缴足额支付（01）且已对平（01）
            if ("01".equals(trialBill.getBillStatus()) && "01".equals(trialBill.getAccountStatus())) {
                shouldAutoFinalize = true;
                finalizeReason = "1"; // 已缴足额支付
                log.debug("账单 {} 满足自动完结条件：已缴足额且已对平", billId);
            }
            
            // 3. 执行自动完结
            if (shouldAutoFinalize) {
                autoFinalizeTrialBill(trialBill, finalizeReason);
                return true;
            } else {
                log.debug("账单 {} 不满足自动完结条件", billId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("检查自动完结失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行自动完结试算账单
     */
    public void autoFinalizeTrialBill(BbpmPenaltyFeeTrialBillVo trialBill, String finalizeReason) {
        try {
            // 1. 创建完结账单记录
            BbpmPenaltyFeeFinalizedBillVo finalizedBill = new BbpmPenaltyFeeFinalizedBillVo();
            
            // 复制试算账单的基本信息
            finalizedBill.setTrialBillId(trialBill.getTrialBillId());
            finalizedBill.setBillId(trialBill.getBillId());
            finalizedBill.setBillCycle(trialBill.getBillCycle());
            finalizedBill.setProjectName(trialBill.getProjectName());
            finalizedBill.setHouseName(trialBill.getHouseName());
            finalizedBill.setTenantCode(trialBill.getTenantCode());
            finalizedBill.setTenantName(trialBill.getTenantName());
            finalizedBill.setContractCode(trialBill.getContractCode());
            finalizedBill.setChargeSubjectBeginDate(trialBill.getChargeSubjectBeginDate());
            finalizedBill.setChargeSubjectEndDate(trialBill.getChargeSubjectEndDate());
            finalizedBill.setChargeSubjectPeriod(trialBill.getChargeSubjectPeriod());
            finalizedBill.setPayableDate(trialBill.getPayableDate());
            finalizedBill.setShouldPayAmount(trialBill.getShouldPayAmount());
            finalizedBill.setPayedAmount(trialBill.getPayedAmount());
            finalizedBill.setReplacePayAmount(trialBill.getReplacePayAmount());
            finalizedBill.setOverdueDays(trialBill.getOverdueDays());
            finalizedBill.setDailyOverdueRate(trialBill.getDailyOverdueRate());
            finalizedBill.setTotalPenaltyAmount(trialBill.getTotalPenaltyAmount());
            finalizedBill.setBillChargeSubject(trialBill.getBillChargeSubject());
            
            // 设置完结相关信息
            finalizedBill.setFinalizedReason(finalizeReason);
            finalizedBill.setFinalizedDate(LocalDate.now());
            finalizedBill.setDisposalStatus("1"); // 未处置
            finalizedBill.setProjectId(trialBill.getProjectId());
            finalizedBill.setDelFlag("1");
            
            // 2. 保存完结账单
            penaltyFeeFinalizedBillService.insertRecord(finalizedBill);
            
            // 3. 更新试算账单状态为已完结
            trialBill.setStatus("2"); // 已完结
            penaltyFeeTrialBillService.updateByIdRecord(trialBill);
            
            log.info("自动完结试算账单成功：账单ID={}, 完结原因={}", trialBill.getBillId(), finalizeReason);
            
        } catch (Exception e) {
            log.error("自动完结试算账单失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 调整违约金（红冲+重新计算）
     */
    public void adjustPenalty(BbpmBillManagementPageResultVo bill, LocalDate discoveryDate, String projectId) {
        try {
            // 1. 查询当前账单的收款单信息（带日志记录）
            PageResult<List<BbpmCollectionPageResultVo>> paymentsResult = penaltyDataService.queryPaymentsByBillId(
                null, bill.getBillId(), discoveryDate, bill.getContractNo(), projectId);
            List<BbpmCollectionPageResultVo> currentPayments = 
                (paymentsResult != null && paymentsResult.getRows() != null) ? paymentsResult.getRows() : new ArrayList<>();
                
            LocalDate bankReceiptDate = penaltyCalculationService.getEarliestBankReceiptDate(currentPayments, discoveryDate);
            
            if (bankReceiptDate == null) {
                log.warn("账单 {} 没有银行回单日期，跳过调整", bill.getBillId());
                return;
            }
            
            // 2. 查询需要调整的记录（从银行回单日期开始）
            List<BbpmPenaltyFeeDailyDetailVo> recordsToAdjust = penaltyDataService.selectDailyDetailsFromDate(bill.getBillId(), bankReceiptDate);
            
            if (recordsToAdjust.isEmpty()) {
                log.info("账单 {} 从日期 {} 开始没有需要调整的违约金记录", bill.getBillId(), bankReceiptDate);
                return;
            }
            
            log.info("找到账单 {} 从日期 {} 开始需要调整的 {} 条违约金记录", 
                    bill.getBillId(), bankReceiptDate, recordsToAdjust.size());
            
            // 3. 对每条记录进行精确红冲
            for (BbpmPenaltyFeeDailyDetailVo record : recordsToAdjust) {
                try {
                    insertPreciseAdjustmentRecord(record, "对账状态变化调整");
                } catch (Exception e) {
                    log.error("红冲记录失败：记录ID={}，错误：{}", record.getDailyDetailId(), e.getMessage(), e);
                }
            }
            
            // 4. 更新试算账单汇总信息
            penaltySummaryService.updateTrialBillSummary(bill.getBillId(), discoveryDate);
            
        } catch (Exception e) {
            log.error("调整违约金失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 插入精确红冲记录
     */
    public void insertPreciseAdjustmentRecord(BbpmPenaltyFeeDailyDetailVo originalRecord, String adjustmentReason) {
        try {
            // 创建红冲记录
            BbpmPenaltyFeeDailyDetailVo adjustmentRecord = new BbpmPenaltyFeeDailyDetailVo();
            
            // 复制原记录的基本信息
            adjustmentRecord.setBillId(originalRecord.getBillId());
            adjustmentRecord.setCalculationDate(originalRecord.getCalculationDate());
            adjustmentRecord.setReplacePayAmount(originalRecord.getReplacePayAmount());
            adjustmentRecord.setDailyOverdueRate(originalRecord.getDailyOverdueRate());
            adjustmentRecord.setBillStatus(originalRecord.getBillStatus());
            adjustmentRecord.setAccountStatus(originalRecord.getAccountStatus());
            adjustmentRecord.setChargeTime(originalRecord.getChargeTime());
            adjustmentRecord.setProjectId(originalRecord.getProjectId());
            
            // 设置红冲特有字段
            adjustmentRecord.setEntryType("2"); // 2-调整红冲
            adjustmentRecord.setAdjustmentReason(adjustmentReason);
            adjustmentRecord.setOriginalEntryId(originalRecord.getDailyDetailId()); // 关联原记录
            
            // 红冲金额为负数（与原记录相反）
            BigDecimal originalAmount = originalRecord.getDailyPenaltyAmount();
            if (originalAmount != null) {
                adjustmentRecord.setDailyPenaltyAmount(originalAmount.negate());
            } else {
                adjustmentRecord.setDailyPenaltyAmount(BigDecimal.ZERO);
            }
            
            // 获取当前调整序号（同一天同一原记录的调整次数）
            int adjustmentSeq = getNextAdjustmentSeq(originalRecord.getBillId(), 
                                                    originalRecord.getCalculationDate(), 
                                                    originalRecord.getDailyDetailId());
            adjustmentRecord.setAdjustmentSeq(adjustmentSeq);
            
            // 设置删除标识
            adjustmentRecord.setDelFlag("1");
            
            // 保存红冲记录
            penaltyFeeDailyDetailService.insertRecord(adjustmentRecord);
            
            log.debug("插入红冲记录成功：原记录ID={}, 红冲金额={}, 调整序号={}", 
                     originalRecord.getDailyDetailId(), adjustmentRecord.getDailyPenaltyAmount(), adjustmentSeq);
            
        } catch (Exception e) {
            log.error("插入红冲记录失败：原记录ID={}, 错误：{}", originalRecord.getDailyDetailId(), e.getMessage(), e);
            throw new RuntimeException("插入红冲记录失败", e);
        }
    }

    /**
     * 获取下一个调整序号
     */
    public int getNextAdjustmentSeq(String billId, LocalDate calculationDate, String originalEntryId) {
        try {
            // 查询该账单、该日期、该原记录的所有调整记录
            List<BbpmPenaltyFeeDailyDetailVo> allDetails = penaltyDataService.selectDailyDetailsByBillId(billId);

            // 筛选相关的调整记录
            int maxSeq = allDetails.stream()
                .filter(detail -> "2".equals(detail.getEntryType())) // 调整红冲记录
                .filter(detail -> calculationDate.equals(detail.getCalculationDate()))
                .filter(detail -> originalEntryId.equals(detail.getOriginalEntryId()))
                .mapToInt(detail -> detail.getAdjustmentSeq() != null ? detail.getAdjustmentSeq() : 0)
                .max()
                .orElse(1); // 如果没有调整记录，从2开始（1是正常计算记录）

            return maxSeq + 1;

        } catch (Exception e) {
            log.error("获取调整序号失败：billId={}, calculationDate={}, originalEntryId={}",
                     billId, calculationDate, originalEntryId, e);
            return 2; // 默认返回2
        }
    }
}
