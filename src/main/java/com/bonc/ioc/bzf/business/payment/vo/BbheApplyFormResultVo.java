package com.bonc.ioc.bzf.business.payment.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 根据合同获取申请单对象
 */
@Data
@ApiModel(value="BbheApplyFormResultVo", description="根据合同获取申请单对象")
public class BbheApplyFormResultVo implements Serializable {

    /**
     * 申请单id
     */
    @ApiModelProperty(value = "申请单编号")
    private String exchangeApplyId;


    /**
     * 申请单编号
     */
    @ApiModelProperty(value = "申请单编号")
    private String applyNum;


    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private String applyDate;


    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractCode;


    /**
     * 申请阶段
     */
    @ApiModelProperty(value = "申请阶段")
    private String applyStage;

    /**
     * 申请阶段名称
     */
    @ApiModelProperty(value = "申请阶段名称")
    private String applyStageName;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态")
    private String applyStatus;

    /**
     * 申请状态名称
     */
    @ApiModelProperty(value = "申请状态名称")
    private String applyStatusName;
}
