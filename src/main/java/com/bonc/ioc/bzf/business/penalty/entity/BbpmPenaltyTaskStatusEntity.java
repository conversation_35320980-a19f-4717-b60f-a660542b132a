package com.bonc.ioc.bzf.business.penalty.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 任务执行状态 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@TableName("bbpm_penalty_task_status")
@ApiModel(value="BbpmPenaltyTaskStatusEntity对象", description="任务执行状态")
public class BbpmPenaltyTaskStatusEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_TASK_STATUS_ID = "task_status_id";
    public static final String FIELD_TASK_DATE = "task_date";
    public static final String FIELD_CONTRACT_CODE = "contract_code";
    public static final String FIELD_PROCESS_STATUS = "process_status";
    public static final String FIELD_BILL_COUNT = "bill_count";
    public static final String FIELD_START_TIME = "start_time";
    public static final String FIELD_END_TIME = "end_time";
    public static final String FIELD_ERROR_MESSAGE = "error_message";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_PROJECT_ID = "project_id";

    /**
     * 任务状态ID
     */
    @ApiModelProperty(value = "任务状态ID")
                                @TableId(value = "task_status_id", type = IdType.ASSIGN_UUID)
                                  private String taskStatusId;

    /**
     * 任务日期
     */
    @ApiModelProperty(value = "任务日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate taskDate;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 处理状态：1-处理中，2-已完成，3-失败
     */
    @ApiModelProperty(value = "处理状态：1-处理中，2-已完成，3-失败")
                            private String processStatus;

    /**
     * 处理的账单数量
     */
    @ApiModelProperty(value = "处理的账单数量")
                            private Integer billCount;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date endTime;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
                            private String errorMessage;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private Integer delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * @return 任务状态ID
     */
    public String getTaskStatusId() {
        return taskStatusId;
    }

    public void setTaskStatusId(String taskStatusId) {
        this.taskStatusId = taskStatusId;
    }

    /**
     * @return 任务日期
     */
    public LocalDate getTaskDate() {
        return taskDate;
    }

    public void setTaskDate(LocalDate taskDate) {
        this.taskDate = taskDate;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 处理状态：1-处理中，2-已完成，3-失败
     */
    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    /**
     * @return 处理的账单数量
     */
    public Integer getBillCount() {
        return billCount;
    }

    public void setBillCount(Integer billCount) {
        this.billCount = billCount;
    }

    /**
     * @return 开始时间
     */
    public Date getStartTime(){
        if(startTime!=null){
            return (Date)startTime.clone();
        }else{
            return null;
        }
    }

    public void setStartTime(Date startTime) {
        if(startTime==null){
            this.startTime = null;
        }else{
            this.startTime = (Date)startTime.clone();
        }
    }

    /**
     * @return 结束时间
     */
    public Date getEndTime(){
        if(endTime!=null){
            return (Date)endTime.clone();
        }else{
            return null;
        }
    }

    public void setEndTime(Date endTime) {
        if(endTime==null){
            this.endTime = null;
        }else{
            this.endTime = (Date)endTime.clone();
        }
    }

    /**
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * @return 有效标识
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyTaskStatusEntity{" +
            "taskStatusId=" + taskStatusId +
            ", taskDate=" + taskDate +
            ", contractCode=" + contractCode +
            ", processStatus=" + processStatus +
            ", billCount=" + billCount +
            ", startTime=" + startTime +
            ", endTime=" + endTime +
            ", errorMessage=" + errorMessage +
            ", delFlag=" + delFlag +
            ", projectId=" + projectId +
        "}";
    }
}