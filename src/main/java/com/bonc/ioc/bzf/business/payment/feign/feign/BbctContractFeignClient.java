package com.bonc.ioc.bzf.business.payment.feign.feign;

import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import com.bonc.ioc.bzf.business.payment.utils.SystemConstant;
import com.bonc.ioc.bzf.business.payment.feign.fallback.BbctContractFeignClientFallback;

import java.util.Date;
import java.util.List;

/**
 * @description: 合同管理
 * @author: zhaoweiming
 * @date: 2022-12-12 8:55
 * @param:
 * @return:
 * @since 1.0.0
 **/
@FeignClient(contextId = "contract", value = SystemConstant.APPLICATION_NAME_CONTRACT_V2, fallback = BbctContractFeignClientFallback.class,
        configuration = FeignExceptionConfiguration.class)
public interface BbctContractFeignClient {

    /**
     * 通过合同id或编号查询合同基础信息
     *
     * @param contractNo 合同编号
     * @param contractId 合同id
     * @return
     */
    @GetMapping(value = "/bzf-business-contract/v2/business/bbctContractManagementEntity/selectByIdNo", produces = "application/json;charset=UTF-8")
    @LogPoint(system = SystemConstant.APPLICATION_NAME_CONTRACT_V2)
    AppReply<BbctContractManagementVo> selectByIdNo(@RequestParam(value = "contractNo", required = false) String contractNo, @RequestParam(value = "contractId", required = false) String contractId);

    @GetMapping(value = "/bzf-business-contract/v2/business/bbctContractManagementEntity/selectByPageCommon", produces = "application/json;charset=UTF-8")
    @LogPoint(system = SystemConstant.APPLICATION_NAME_CONTRACT_V2)
    AppReply<PageResult<List<BbctContractManagementPageResultVo>>> selectByPageCommon(@SpringQueryMap BbctContractManagementPageVo vo);

}
