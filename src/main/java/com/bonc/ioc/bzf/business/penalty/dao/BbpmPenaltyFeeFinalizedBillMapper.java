package com.bonc.ioc.bzf.business.penalty.dao;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeFinalizedBillEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import java.util.List;

/**
 * 违约金计费完结账单 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Mapper
public interface BbpmPenaltyFeeFinalizedBillMapper extends McpBaseMapper<BbpmPenaltyFeeFinalizedBillEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change 2025-08-08 by tbh for init
     */
    List<BbpmPenaltyFeeFinalizedBillPageResultVo> selectByPageCustom(@Param("vo") BbpmPenaltyFeeFinalizedBillPageVo vo );
}
