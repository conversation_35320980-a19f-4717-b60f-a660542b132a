package com.bonc.ioc.bzf.business.penalty.service;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyTaskStatusEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 任务执行状态 服务类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
public interface IBbpmPenaltyTaskStatusService extends IMcpBaseService<BbpmPenaltyTaskStatusEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    String insertRecord(BbpmPenaltyTaskStatusVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    List<String> insertBatchRecord(List<BbpmPenaltyTaskStatusVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param taskStatusId 需要删除的任务状态ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdRecord(String taskStatusId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param taskStatusIdList 需要删除的任务状态ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdsRecord(List<String> taskStatusIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateByIdRecord(BbpmPenaltyTaskStatusVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateBatchByIdRecord(List<BbpmPenaltyTaskStatusVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveByIdRecord(BbpmPenaltyTaskStatusVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveBatchByIdRecord(List<BbpmPenaltyTaskStatusVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param taskStatusId 需要查询的任务状态ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    BbpmPenaltyTaskStatusVo selectByIdRecord(String taskStatusId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    PageResult<List<BbpmPenaltyTaskStatusPageResultVo>> selectByPageRecord(BbpmPenaltyTaskStatusPageVo vo);

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    List<BbpmPenaltyTaskStatusVo> selectListByCondition(BbpmPenaltyTaskStatusVo vo);
}
