package com.bonc.ioc.bzf.business.penalty.vo.contractExtend;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustomerExtendVo {

    /**
     * 公司注册地址
     */
    @ApiModelProperty(value = "公司注册地址")
    private String registeredAddress;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String proCode;

    /**
     * 省名
     */
    @ApiModelProperty(value = "省名")
    private String proName;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(value = "市名")
    private String cityName;



}
