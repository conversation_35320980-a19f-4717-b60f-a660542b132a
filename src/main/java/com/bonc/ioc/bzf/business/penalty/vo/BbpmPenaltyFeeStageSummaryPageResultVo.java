package com.bonc.ioc.bzf.business.penalty.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.bzf.business.payment.vo.BasePageResultVo;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 违约金分阶段汇总表 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@ApiModel(value="BbpmPenaltyFeeStageSummaryPageResultVo对象", description="违约金分阶段汇总表")
public class BbpmPenaltyFeeStageSummaryPageResultVo extends BasePageResultVo implements Serializable{


    /**
     * 分阶段汇总ID
     */
    @ApiModelProperty(value = "分阶段汇总ID")
    @NotBlank(message = "分阶段汇总ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String stageSummaryId;

    /**
     * 试算账单ID
     */
    @ApiModelProperty(value = "试算账单ID")
                            private String trialBillId;

    /**
     * 违约金计算开始日期
     */
    @ApiModelProperty(value = "违约金计算开始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate penaltyStartDate;

    /**
     * 违约金计算截止日期（银行回单日期）
     */
    @ApiModelProperty(value = "违约金计算截止日期（银行回单日期）")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate penaltyEndDate;

    /**
     * 逾期天数
     */
    @ApiModelProperty(value = "逾期天数")
                            private Integer overdueDays;

    /**
     * 每日逾期率
     */
    @ApiModelProperty(value = "每日逾期率")
                            private BigDecimal dailyOverdueRate;

    /**
     * 欠缴金额
     */
    @ApiModelProperty(value = "欠缴金额")
                            private BigDecimal unpaidAmount;

    /**
     * 本阶段违约金金额
     */
    @ApiModelProperty(value = "本阶段违约金金额")
                            private BigDecimal stagePenaltyAmount;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private String delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    @ApiModelProperty(value = "当前阶段：1-当前日期前一天，2-银行回单日")
    @McpDictPoint(dictCode = "PENALTY_CURRENTSTAGE", overTransCopyTo = "currentStageName")
    private String currentStage;

    @ApiModelProperty(value = "当前阶段名称")
    private String currentStageName;

    public String getCurrentStageName() {
        return currentStageName;
    }

    public void setCurrentStageName(String currentStageName) {
        this.currentStageName = currentStageName;
    }

    public String getCurrentStage() {
        return currentStage;
    }

    public void setCurrentStage(String currentStage) {
        this.currentStage = currentStage;
    }

    /**
     * @return 分阶段汇总ID
     */
    public String getStageSummaryId() {
        return stageSummaryId;
    }

    public void setStageSummaryId(String stageSummaryId) {
        this.stageSummaryId = stageSummaryId;
    }

    /**
     * @return 试算账单ID
     */
    public String getTrialBillId() {
        return trialBillId;
    }

    public void setTrialBillId(String trialBillId) {
        this.trialBillId = trialBillId;
    }

    /**
     * @return 违约金计算开始日期
     */
    public LocalDate getPenaltyStartDate() {
        return penaltyStartDate;
    }

    public void setPenaltyStartDate(LocalDate penaltyStartDate) {
        this.penaltyStartDate = penaltyStartDate;
    }

    /**
     * @return 违约金计算截止日期（银行回单日期）
     */
    public LocalDate getPenaltyEndDate() {
        return penaltyEndDate;
    }

    public void setPenaltyEndDate(LocalDate penaltyEndDate) {
        this.penaltyEndDate = penaltyEndDate;
    }

    /**
     * @return 逾期天数
     */
    public Integer getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(Integer overdueDays) {
        this.overdueDays = overdueDays;
    }

    /**
     * @return 每日逾期率
     */
    public BigDecimal getDailyOverdueRate() {
        return dailyOverdueRate;
    }

    public void setDailyOverdueRate(BigDecimal dailyOverdueRate) {
        this.dailyOverdueRate = dailyOverdueRate;
    }

    /**
     * @return 欠缴金额
     */
    public BigDecimal getUnpaidAmount() {
        return unpaidAmount;
    }

    public void setUnpaidAmount(BigDecimal unpaidAmount) {
        this.unpaidAmount = unpaidAmount;
    }

    /**
     * @return 本阶段违约金金额
     */
    public BigDecimal getStagePenaltyAmount() {
        return stagePenaltyAmount;
    }

    public void setStagePenaltyAmount(BigDecimal stagePenaltyAmount) {
        this.stagePenaltyAmount = stagePenaltyAmount;
    }

    /**
     * @return 有效标识
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyFeeStageSummaryPageResultVo{" +
            "stageSummaryId=" + stageSummaryId +
            ", trialBillId=" + trialBillId +
            ", penaltyStartDate=" + penaltyStartDate +
            ", penaltyEndDate=" + penaltyEndDate +
            ", overdueDays=" + overdueDays +
            ", dailyOverdueRate=" + dailyOverdueRate +
            ", unpaidAmount=" + unpaidAmount +
            ", stagePenaltyAmount=" + stagePenaltyAmount +
            ", delFlag=" + delFlag +
            ", projectId=" + projectId +
        "}";
    }
}
