package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 追加账单推送报文 vo实体类
 *
 * <AUTHOR>
 * @since 2025/4/2
 */
@Data
@ApiModel(value = "追加账单推送报文", description = "追加账单推送报文")
public class BbpmAppendBillVo extends McpBaseVo implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractId;

    /**
     * 房源列表
     */
    @ApiModelProperty(value = "房源列表")
    private List<BbpmAppendBillRoomVo> roomList;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目业态 (03商业  01 公祖 02 保租)
     */
    @ApiModelProperty(value = "项目业态 (03商业  01 公祖 02 保租)")
    private String projectFormat;
}
