package com.bonc.ioc.bzf.business.penalty.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 违约金每日计算明细 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@ApiModel(value="BbpmPenaltyFeeDailyDetailPageResultVo对象", description="违约金每日计算明细")
public class BbpmPenaltyFeeDailyDetailPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 每日明细ID
     */
    @ApiModelProperty(value = "每日明细ID")
    @NotBlank(message = "每日明细ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String dailyDetailId;

    /**
     * 账单ID（工银账单ID）
     */
    @ApiModelProperty(value = "账单ID（工银账单ID）")
                            private String billId;

    /**
     * 计算日期
     */
    @ApiModelProperty(value = "计算日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate calculationDate;

    /**
     * 当日待缴金额（未缴金额）
     */
    @ApiModelProperty(value = "当日待缴金额（未缴金额）")
                            private BigDecimal replacePayAmount;

    /**
     * 每日逾期率
     */
    @ApiModelProperty(value = "每日逾期率")
                            private BigDecimal dailyOverdueRate;

    /**
     * 当日违约金金额（可为负数）
     */
    @ApiModelProperty(value = "当日违约金金额（可为负数）")
                            private BigDecimal dailyPenaltyAmount;

    /**
     * 记录类型：1-正常计算，2-调整红冲
     */
    @ApiModelProperty(value = "记录类型：1-正常计算，2-调整红冲")
                            private String entryType;

    /**
     * 调整原因
     */
    @ApiModelProperty(value = "调整原因")
                            private String adjustmentReason;

    /**
     * 账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴
     */
    @ApiModelProperty(value = "账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴")
                            private String billStatus;

    /**
     * 对账状态：01-对齐，02-未对齐
     */
    @ApiModelProperty(value = "对账状态：01-对齐，02-未对齐")
                            private String accountStatus;

    /**
     * 收款时间（银行回单日期，记录影响当日计算的收款时间）
     */
    @ApiModelProperty(value = "收款时间（银行回单日期，记录影响当日计算的收款时间）")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate chargeTime;

    /**
     * 原始记录ID（红冲时关联到daily_detail_id）
     */
    @ApiModelProperty(value = "原始记录ID（红冲时关联到daily_detail_id）")
                            private String originalEntryId;

    /**
     * 调整序号（同一天多次调整时递增，正常计算记录固定为1）
     */
    @ApiModelProperty(value = "调整序号（同一天多次调整时递增，正常计算记录固定为1）")
                            private Integer adjustmentSeq;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private String delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * @return 每日明细ID
     */
    public String getDailyDetailId() {
        return dailyDetailId;
    }

    public void setDailyDetailId(String dailyDetailId) {
        this.dailyDetailId = dailyDetailId;
    }

    /**
     * @return 账单ID（工银账单ID）
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 计算日期
     */
    public LocalDate getCalculationDate() {
        return calculationDate;
    }

    public void setCalculationDate(LocalDate calculationDate) {
        this.calculationDate = calculationDate;
    }

    /**
     * @return 当日待缴金额（未缴金额）
     */
    public BigDecimal getReplacePayAmount() {
        return replacePayAmount;
    }

    public void setReplacePayAmount(BigDecimal replacePayAmount) {
        this.replacePayAmount = replacePayAmount;
    }

    /**
     * @return 每日逾期率
     */
    public BigDecimal getDailyOverdueRate() {
        return dailyOverdueRate;
    }

    public void setDailyOverdueRate(BigDecimal dailyOverdueRate) {
        this.dailyOverdueRate = dailyOverdueRate;
    }

    /**
     * @return 当日违约金金额（可为负数）
     */
    public BigDecimal getDailyPenaltyAmount() {
        return dailyPenaltyAmount;
    }

    public void setDailyPenaltyAmount(BigDecimal dailyPenaltyAmount) {
        this.dailyPenaltyAmount = dailyPenaltyAmount;
    }

    /**
     * @return 记录类型：1-正常计算，2-调整红冲
     */
    public String getEntryType() {
        return entryType;
    }

    public void setEntryType(String entryType) {
        this.entryType = entryType;
    }

    /**
     * @return 调整原因
     */
    public String getAdjustmentReason() {
        return adjustmentReason;
    }

    public void setAdjustmentReason(String adjustmentReason) {
        this.adjustmentReason = adjustmentReason;
    }

    /**
     * @return 账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴
     */
    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     * @return 对账状态：01-对齐，02-未对齐
     */
    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    /**
     * @return 收款时间（银行回单日期，记录影响当日计算的收款时间）
     */
    public LocalDate getChargeTime() {
        return chargeTime;
    }

    public void setChargeTime(LocalDate chargeTime) {
        this.chargeTime = chargeTime;
    }

    /**
     * @return 原始记录ID（红冲时关联到daily_detail_id）
     */
    public String getOriginalEntryId() {
        return originalEntryId;
    }

    public void setOriginalEntryId(String originalEntryId) {
        this.originalEntryId = originalEntryId;
    }

    /**
     * @return 调整序号（同一天多次调整时递增，正常计算记录固定为1）
     */
    public Integer getAdjustmentSeq() {
        return adjustmentSeq;
    }

    public void setAdjustmentSeq(Integer adjustmentSeq) {
        this.adjustmentSeq = adjustmentSeq;
    }

    /**
     * @return 有效标识
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyFeeDailyDetailPageResultVo{" +
            "dailyDetailId=" + dailyDetailId +
            ", billId=" + billId +
            ", calculationDate=" + calculationDate +
            ", replacePayAmount=" + replacePayAmount +
            ", dailyOverdueRate=" + dailyOverdueRate +
            ", dailyPenaltyAmount=" + dailyPenaltyAmount +
            ", entryType=" + entryType +
            ", adjustmentReason=" + adjustmentReason +
            ", billStatus=" + billStatus +
            ", accountStatus=" + accountStatus +
            ", chargeTime=" + chargeTime +
            ", originalEntryId=" + originalEntryId +
            ", adjustmentSeq=" + adjustmentSeq +
            ", delFlag=" + delFlag +
            ", projectId=" + projectId +
        "}";
    }
}
