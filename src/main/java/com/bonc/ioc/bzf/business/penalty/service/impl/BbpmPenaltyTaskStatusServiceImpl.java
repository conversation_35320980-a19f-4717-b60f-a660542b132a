package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyTaskStatusEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyTaskStatusMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyTaskStatusService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 任务执行状态 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyTaskStatusServiceImpl extends McpBaseServiceImpl<BbpmPenaltyTaskStatusEntity> implements IBbpmPenaltyTaskStatusService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyTaskStatusMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyTaskStatusService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyTaskStatusVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyTaskStatusEntity entity = new BbpmPenaltyTaskStatusEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setTaskStatusId(null);
        if(!baseService.insert(entity)) {
            log.error("任务执行状态新增失败:" + entity.toString());
            throw new McpException("任务执行状态新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getTaskStatusId(),1)) {
                log.error("任务执行状态新增后保存历史失败:" + entity.toString());
                throw new McpException("任务执行状态新增后保存历史失败");
            }

            log.debug("任务执行状态新增成功:"+entity.getTaskStatusId());
            return entity.getTaskStatusId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyTaskStatusVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyTaskStatusEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyTaskStatusVo item:voList) {
            BbpmPenaltyTaskStatusEntity entity = new BbpmPenaltyTaskStatusEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyTaskStatusEntity item:entityList){
            item.setTaskStatusId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("任务执行状态新增失败");
            throw new McpException("任务执行状态新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyTaskStatusEntity::getTaskStatusId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("任务执行状态批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("任务执行状态批量新增后保存历史失败");
            }

            log.debug("任务执行状态新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param taskStatusId 需要删除的任务状态ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String taskStatusId) {
        if(!StringUtils.isEmpty(taskStatusId)) {
            if(!baseService.saveOperationHisById(taskStatusId,3)) {
                log.error("任务执行状态删除后保存历史失败:" + taskStatusId);
                throw new McpException("任务执行状态删除后保存历史失败");
            }

            if(!baseService.removeById(taskStatusId)) {
                log.error("任务执行状态删除失败");
                throw new McpException("任务执行状态删除失败"+taskStatusId);
            }
        } else {
            throw new McpException("任务执行状态删除失败任务状态ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param taskStatusIdList 需要删除的任务状态ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> taskStatusIdList) {
        if(!CollectionUtils.isEmpty(taskStatusIdList)) {
            int oldSize = taskStatusIdList.size();
            taskStatusIdList = taskStatusIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(taskStatusIdList) || oldSize != taskStatusIdList.size()) {
                throw new McpException("任务执行状态批量删除失败 存在主键id为空的记录"+StringUtils.join(taskStatusIdList));
            }

            if(!baseService.saveOperationHisByIds(taskStatusIdList,3)) {
                log.error("任务执行状态批量删除后保存历史失败:" + StringUtils.join(taskStatusIdList));
                throw new McpException("任务执行状态批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(taskStatusIdList)) {
                log.error("任务执行状态批量删除失败");
                throw new McpException("任务执行状态批量删除失败"+StringUtils.join(taskStatusIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyTaskStatusVo vo) {
        if(vo != null) {
            BbpmPenaltyTaskStatusEntity entity = new BbpmPenaltyTaskStatusEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getTaskStatusId())) {
                throw new McpException("任务执行状态更新失败传入任务状态ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("任务执行状态更新失败");
                throw new McpException("任务执行状态更新失败"+entity.getTaskStatusId());
            } else {
                if(!baseService.saveOperationHisById(entity.getTaskStatusId(),2)) {
                    log.error("任务执行状态更新后保存历史失败:" + entity.getTaskStatusId());
                    throw new McpException("任务执行状态更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("任务执行状态更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyTaskStatusVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyTaskStatusEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyTaskStatusVo item:voList){
                BbpmPenaltyTaskStatusEntity entity = new BbpmPenaltyTaskStatusEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getTaskStatusId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("任务执行状态批量更新失败 存在任务状态ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("任务执行状态批量更新失败");
                throw new McpException("任务执行状态批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getTaskStatusId())).map(BbpmPenaltyTaskStatusEntity::getTaskStatusId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("任务执行状态批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("任务执行状态批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyTaskStatusVo vo) {
        if(vo != null) {
            BbpmPenaltyTaskStatusEntity entity = new BbpmPenaltyTaskStatusEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("任务执行状态保存失败");
                throw new McpException("任务执行状态保存失败"+entity.getTaskStatusId());
            } else {
                if(!baseService.saveOperationHisById(entity.getTaskStatusId(),4)) {
                    log.error("任务执行状态保存后保存历史失败:" + entity.getTaskStatusId());
                    throw new McpException("任务执行状态保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("任务执行状态保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的任务执行状态
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyTaskStatusVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyTaskStatusEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyTaskStatusVo item:voList){
                BbpmPenaltyTaskStatusEntity entity = new BbpmPenaltyTaskStatusEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("任务执行状态批量保存失败");
                throw new McpException("任务执行状态批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getTaskStatusId())).map(BbpmPenaltyTaskStatusEntity::getTaskStatusId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("任务执行状态批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("任务执行状态批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param taskStatusId 需要查询的任务状态ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyTaskStatusVo selectByIdRecord(String taskStatusId) {
        BbpmPenaltyTaskStatusVo vo = new BbpmPenaltyTaskStatusVo();

        if(!StringUtils.isEmpty(taskStatusId)) {
            BbpmPenaltyTaskStatusEntity entity = baseService.selectById(taskStatusId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyTaskStatusPageResultVo>> selectByPageRecord(BbpmPenaltyTaskStatusPageVo vo) {
        List<BbpmPenaltyTaskStatusPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmPenaltyTaskStatusVo> selectListByCondition(BbpmPenaltyTaskStatusVo vo) {
        return baseMapper.selectListByCondition(vo);
    }
}
