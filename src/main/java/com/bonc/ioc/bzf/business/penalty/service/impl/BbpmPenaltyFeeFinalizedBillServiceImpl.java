package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeFinalizedBillEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeFinalizedBillMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeFinalizedBillService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金计费完结账单 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyFeeFinalizedBillServiceImpl extends McpBaseServiceImpl<BbpmPenaltyFeeFinalizedBillEntity> implements IBbpmPenaltyFeeFinalizedBillService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyFeeFinalizedBillMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyFeeFinalizedBillService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyFeeFinalizedBillVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyFeeFinalizedBillEntity entity = new BbpmPenaltyFeeFinalizedBillEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setFinalizedBillId(null);
        if(!baseService.insert(entity)) {
            log.error("违约金计费完结账单新增失败:" + entity.toString());
            throw new McpException("违约金计费完结账单新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getFinalizedBillId(),1)) {
                log.error("违约金计费完结账单新增后保存历史失败:" + entity.toString());
                throw new McpException("违约金计费完结账单新增后保存历史失败");
            }

            log.debug("违约金计费完结账单新增成功:"+entity.getFinalizedBillId());
            return entity.getFinalizedBillId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyFeeFinalizedBillVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyFeeFinalizedBillEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyFeeFinalizedBillVo item:voList) {
            BbpmPenaltyFeeFinalizedBillEntity entity = new BbpmPenaltyFeeFinalizedBillEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyFeeFinalizedBillEntity item:entityList){
            item.setFinalizedBillId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("违约金计费完结账单新增失败");
            throw new McpException("违约金计费完结账单新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyFeeFinalizedBillEntity::getFinalizedBillId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("违约金计费完结账单批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("违约金计费完结账单批量新增后保存历史失败");
            }

            log.debug("违约金计费完结账单新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param finalizedBillId 需要删除的完结账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String finalizedBillId) {
        if(!StringUtils.isEmpty(finalizedBillId)) {
            if(!baseService.saveOperationHisById(finalizedBillId,3)) {
                log.error("违约金计费完结账单删除后保存历史失败:" + finalizedBillId);
                throw new McpException("违约金计费完结账单删除后保存历史失败");
            }

            if(!baseService.removeById(finalizedBillId)) {
                log.error("违约金计费完结账单删除失败");
                throw new McpException("违约金计费完结账单删除失败"+finalizedBillId);
            }
        } else {
            throw new McpException("违约金计费完结账单删除失败完结账单ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param finalizedBillIdList 需要删除的完结账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> finalizedBillIdList) {
        if(!CollectionUtils.isEmpty(finalizedBillIdList)) {
            int oldSize = finalizedBillIdList.size();
            finalizedBillIdList = finalizedBillIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(finalizedBillIdList) || oldSize != finalizedBillIdList.size()) {
                throw new McpException("违约金计费完结账单批量删除失败 存在主键id为空的记录"+StringUtils.join(finalizedBillIdList));
            }

            if(!baseService.saveOperationHisByIds(finalizedBillIdList,3)) {
                log.error("违约金计费完结账单批量删除后保存历史失败:" + StringUtils.join(finalizedBillIdList));
                throw new McpException("违约金计费完结账单批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(finalizedBillIdList)) {
                log.error("违约金计费完结账单批量删除失败");
                throw new McpException("违约金计费完结账单批量删除失败"+StringUtils.join(finalizedBillIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyFeeFinalizedBillVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeFinalizedBillEntity entity = new BbpmPenaltyFeeFinalizedBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getFinalizedBillId())) {
                throw new McpException("违约金计费完结账单更新失败传入完结账单ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("违约金计费完结账单更新失败");
                throw new McpException("违约金计费完结账单更新失败"+entity.getFinalizedBillId());
            } else {
                if(!baseService.saveOperationHisById(entity.getFinalizedBillId(),2)) {
                    log.error("违约金计费完结账单更新后保存历史失败:" + entity.getFinalizedBillId());
                    throw new McpException("违约金计费完结账单更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金计费完结账单更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyFeeFinalizedBillVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeFinalizedBillEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeFinalizedBillVo item:voList){
                BbpmPenaltyFeeFinalizedBillEntity entity = new BbpmPenaltyFeeFinalizedBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getFinalizedBillId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("违约金计费完结账单批量更新失败 存在完结账单ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("违约金计费完结账单批量更新失败");
                throw new McpException("违约金计费完结账单批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getFinalizedBillId())).map(BbpmPenaltyFeeFinalizedBillEntity::getFinalizedBillId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("违约金计费完结账单批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金计费完结账单批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyFeeFinalizedBillVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeFinalizedBillEntity entity = new BbpmPenaltyFeeFinalizedBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("违约金计费完结账单保存失败");
                throw new McpException("违约金计费完结账单保存失败"+entity.getFinalizedBillId());
            } else {
                if(!baseService.saveOperationHisById(entity.getFinalizedBillId(),4)) {
                    log.error("违约金计费完结账单保存后保存历史失败:" + entity.getFinalizedBillId());
                    throw new McpException("违约金计费完结账单保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金计费完结账单保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyFeeFinalizedBillVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeFinalizedBillEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeFinalizedBillVo item:voList){
                BbpmPenaltyFeeFinalizedBillEntity entity = new BbpmPenaltyFeeFinalizedBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("违约金计费完结账单批量保存失败");
                throw new McpException("违约金计费完结账单批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getFinalizedBillId())).map(BbpmPenaltyFeeFinalizedBillEntity::getFinalizedBillId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("违约金计费完结账单批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金计费完结账单批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param finalizedBillId 需要查询的完结账单ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyFeeFinalizedBillVo selectByIdRecord(String finalizedBillId) {
        BbpmPenaltyFeeFinalizedBillVo vo = new BbpmPenaltyFeeFinalizedBillVo();

        if(!StringUtils.isEmpty(finalizedBillId)) {
            BbpmPenaltyFeeFinalizedBillEntity entity = baseService.selectById(finalizedBillId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyFeeFinalizedBillPageResultVo>> selectByPageRecord(BbpmPenaltyFeeFinalizedBillPageVo vo) {
        List<BbpmPenaltyFeeFinalizedBillPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
