package com.bonc.ioc.bzf.business.penalty.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 违约金计算任务API调用类型枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum PenaltyApiType {

    /**
     * 工银账单接口调用
     */
    BILL_QUERY("1", "账单接口", "调用工银账单查询接口"),

    /**
     * 工银收款单接口调用  
     */
    COLLECTION_QUERY("2", "收款单接口", "调用工银收款单查询接口"),

    /**
     * 对账状态变化记录
     */
    STATUS_CHANGE("3", "状态变化记录", "记录对账状态变化情况"),

    /**
     * 合同中心接口调用
     */
    CONTRACT_QUERY("4", "合同中心接口", "调用合同中心查询有效合同");

    /**
     * API类型编码
     */
    private final String code;

    /**
     * API类型名称
     */
    private final String name;

    /**
     * API类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     * 
     * @param code 编码
     * @return 对应的枚举，如果没有找到则返回null
     */
    public static PenaltyApiType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PenaltyApiType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     * 
     * @param code 编码
     * @return 对应的名称，如果没有找到则返回编码本身
     */
    public static String getNameByCode(String code) {
        PenaltyApiType type = getByCode(code);
        return type != null ? type.getName() : code;
    }

    /**
     * 根据编码获取描述
     * 
     * @param code 编码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDescriptionByCode(String code) {
        PenaltyApiType type = getByCode(code);
        return type != null ? type.getDescription() : "";
    }
}
