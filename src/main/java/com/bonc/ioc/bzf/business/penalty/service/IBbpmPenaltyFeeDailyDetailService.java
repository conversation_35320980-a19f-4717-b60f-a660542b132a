package com.bonc.ioc.bzf.business.penalty.service;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeDailyDetailEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金每日计算明细 服务类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
public interface IBbpmPenaltyFeeDailyDetailService extends IMcpBaseService<BbpmPenaltyFeeDailyDetailEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    String insertRecord(BbpmPenaltyFeeDailyDetailVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    List<String> insertBatchRecord(List<BbpmPenaltyFeeDailyDetailVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param dailyDetailId 需要删除的每日明细ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdRecord(String dailyDetailId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param dailyDetailIdList 需要删除的每日明细ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdsRecord(List<String> dailyDetailIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateByIdRecord(BbpmPenaltyFeeDailyDetailVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateBatchByIdRecord(List<BbpmPenaltyFeeDailyDetailVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveByIdRecord(BbpmPenaltyFeeDailyDetailVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveBatchByIdRecord(List<BbpmPenaltyFeeDailyDetailVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param dailyDetailId 需要查询的每日明细ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    BbpmPenaltyFeeDailyDetailVo selectByIdRecord(String dailyDetailId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    PageResult<List<BbpmPenaltyFeeDailyDetailPageResultVo>> selectByPageRecord(BbpmPenaltyFeeDailyDetailPageVo vo);

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    List<BbpmPenaltyFeeDailyDetailVo> selectListByCondition(BbpmPenaltyFeeDailyDetailVo vo);
}
