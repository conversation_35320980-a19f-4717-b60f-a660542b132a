package com.bonc.ioc.bzf.business.penalty.task;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyTaskStatusService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyTaskStatusVo;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金任务状态管理服务类
 * 负责任务状态的初始化、查询、更新等操作
 */
@Slf4j
@Service
public class PenaltyTaskStatusService {

    @Resource
    private IBbpmPenaltyTaskStatusService penaltyTaskStatusService;
    
    @Resource
    private PenaltyDataService penaltyDataService;

    /**
     * 初始化任务状态表（首次执行时创建所有待处理任务记录）- 使用幂等性安全初始化
     */
    public void initializeTaskStatus(List<String> allContracts, LocalDate taskDate) {
        try {
            log.info("开始检查并初始化任务状态表，任务日期：{}，合同数量：{}", taskDate, allContracts.size());
            
            // 查询已存在的任务状态记录
            List<BbpmPenaltyTaskStatusVo> existingTaskStatus = penaltyDataService.queryTaskStatusList(taskDate, null, null);
            Set<String> existingContracts = existingTaskStatus.stream()
                .map(BbpmPenaltyTaskStatusVo::getContractCode)
                .collect(Collectors.toSet());
            
            // 找出缺失的合同记录
            List<String> missingContracts = allContracts.stream()
                .filter(contractNo -> !existingContracts.contains(contractNo))
                .collect(Collectors.toList());
            
            if (!missingContracts.isEmpty()) {
                log.info("发现 {} 个合同缺少任务状态记录，开始补充创建", missingContracts.size());
                
                int successCount = 0;
                int failCount = 0;
                
                for (String contractNo : missingContracts) {
                    try {
                        // 使用幂等性插入：先查询再插入
                        List<BbpmPenaltyTaskStatusVo> existingRecord = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
                        if (existingRecord.isEmpty()) {
                            BbpmPenaltyTaskStatusVo taskStatus = new BbpmPenaltyTaskStatusVo();
                            taskStatus.setTaskDate(taskDate);
                            taskStatus.setContractCode(contractNo);
                            taskStatus.setProcessStatus("0"); // 0-待处理
                            taskStatus.setBillCount(0);
                            taskStatus.setDelFlag(1);
                            
                            penaltyTaskStatusService.insertRecord(taskStatus);
                            successCount++;
                            log.debug("成功创建合同 {} 的任务状态记录", contractNo);
                        } else {
                            log.debug("合同 {} 的任务状态记录已存在，跳过创建", contractNo);
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("创建合同 {} 的任务状态记录失败：{}", contractNo, e.getMessage(), e);
                        
                        // 如果是唯一键冲突，尝试查询确认记录是否真实存在
                        if (e.getMessage() != null && e.getMessage().contains("Duplicate")) {
                            try {
                                List<BbpmPenaltyTaskStatusVo> checkRecord = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
                                if (!checkRecord.isEmpty()) {
                                    log.info("合同 {} 的任务状态记录实际已存在，忽略重复插入错误", contractNo);
                                    failCount--; // 不计入失败
                                }
                            } catch (Exception checkEx) {
                                log.error("检查重复记录时发生异常：{}", checkEx.getMessage());
                            }
                        }
                    }
                }
                
                log.info("任务状态表初始化完成 - 成功创建：{}，失败：{}，总合同数：{}", 
                    successCount, failCount, allContracts.size());
                    
                // 如果失败数量过多，记录警告
                if (failCount > 0 && failCount > allContracts.size() * 0.1) {
                    log.warn("任务状态初始化失败率较高：{}/{}, 请检查数据库连接和唯一键约束", failCount, allContracts.size());
                }
            } else {
                log.info("所有合同的任务状态记录都已存在，无需初始化");
            }
            
        } catch (Exception e) {
            log.error("初始化任务状态表失败：{}", e.getMessage(), e);
            throw new RuntimeException("任务状态表初始化失败，停止任务执行", e);
        }
    }

    /**
     * 获取合同的处理状态
     */
    public String getContractProcessStatus(String contractNo, LocalDate taskDate) {
        try {
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
            if (!taskStatusList.isEmpty()) {
                return taskStatusList.get(0).getProcessStatus();
            }
            
            return null; // 没有记录
            
        } catch (Exception e) {
            log.error("获取合同处理状态失败：contractNo={}, taskDate={}", contractNo, taskDate, e);
            return null;
        }
    }

    /**
     * 更新任务状态为处理中（使用幂等性安全操作）
     */
    public String updateTaskStatusToProcessing(String contractNo, LocalDate taskDate) {
        try {
            // 查找现有的任务状态记录
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
            
            if (!taskStatusList.isEmpty()) {
                // 更新现有记录
                BbpmPenaltyTaskStatusVo taskStatus = taskStatusList.get(0);
                if (!"1".equals(taskStatus.getProcessStatus())) {  // 如果不是已经是处理中状态，才更新
                    taskStatus.setProcessStatus("1"); // 1-处理中
                    taskStatus.setStartTime(new Date());
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                    log.debug("合同 {} 任务状态已更新为处理中", contractNo);
                } else {
                    log.debug("合同 {} 任务状态已是处理中，无需更新", contractNo);
                }
                return taskStatus.getTaskStatusId();
            } else {
                // 创建新记录（但这种情况理论上不应该发生，因为初始化时应该已经创建了所有记录）
                log.warn("合同 {} 在任务日期 {} 没有找到任务状态记录，将创建新记录", contractNo, taskDate);
                
                // 使用幂等性插入：再次检查避免并发创建
                try {
                    BbpmPenaltyTaskStatusVo taskStatus = new BbpmPenaltyTaskStatusVo();
                    taskStatus.setTaskDate(taskDate);
                    taskStatus.setContractCode(contractNo);
                    taskStatus.setProcessStatus("1"); // 1-处理中
                    taskStatus.setBillCount(0);
                    taskStatus.setStartTime(new Date());
                    taskStatus.setDelFlag(1);
                    
                    String taskStatusId = penaltyTaskStatusService.insertRecord(taskStatus);
                    log.info("成功为合同 {} 创建新的任务状态记录：{}", contractNo, taskStatusId);
                    return taskStatusId;
                    
                } catch (Exception insertEx) {
                    // 如果插入失败（可能是唯一键冲突），尝试查询已存在的记录
                    if (insertEx.getMessage() != null && insertEx.getMessage().contains("Duplicate")) {
                        log.info("检测到唯一键冲突，重新查询合同 {} 的任务状态记录", contractNo);
                        List<BbpmPenaltyTaskStatusVo> retryTaskStatusList = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
                        if (!retryTaskStatusList.isEmpty()) {
                            BbpmPenaltyTaskStatusVo existingTaskStatus = retryTaskStatusList.get(0);
                            if (!"1".equals(existingTaskStatus.getProcessStatus())) {
                                existingTaskStatus.setProcessStatus("1");
                                existingTaskStatus.setStartTime(new Date());
                                penaltyTaskStatusService.updateByIdRecord(existingTaskStatus);
                            }
                            return existingTaskStatus.getTaskStatusId();
                        }
                    }
                    throw insertEx; // 其他异常继续抛出
                }
            }
            
        } catch (Exception e) {
            log.error("更新任务状态为处理中失败：contractNo={}, taskDate={}", contractNo, taskDate, e);
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    /**
     * 更新任务状态为已完成
     */
    public void updateTaskStatusToCompleted(String taskStatusId, String contractNo, int billCount, LocalDate taskDate) {
        try {
            if (StringUtils.isNotBlank(taskStatusId)) {
                BbpmPenaltyTaskStatusVo taskStatus = penaltyTaskStatusService.selectByIdRecord(taskStatusId);
                if (taskStatus != null) {
                    taskStatus.setProcessStatus("2"); // 2-已完成
                    taskStatus.setBillCount(billCount);
                    taskStatus.setEndTime(new Date());
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                } else {
                    log.warn("未找到任务状态记录：taskStatusId={}", taskStatusId);
                }
            }
        } catch (Exception e) {
            log.error("更新任务状态为已完成失败：taskStatusId={}, contractNo={}", taskStatusId, contractNo, e);
        }
    }

    /**
     * 更新任务状态为失败
     */
    public void updateTaskStatusToFailed(String taskStatusId, String contractNo, String errorMessage, LocalDate taskDate) {
        try {
            if (StringUtils.isNotBlank(taskStatusId)) {
                BbpmPenaltyTaskStatusVo taskStatus = penaltyTaskStatusService.selectByIdRecord(taskStatusId);
                if (taskStatus != null) {
                    taskStatus.setProcessStatus("3"); // 3-失败
                    taskStatus.setEndTime(new Date());
                    taskStatus.setErrorMessage(errorMessage);
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                } else {
                    log.warn("未找到任务状态记录：taskStatusId={}", taskStatusId);
                }
            }
        } catch (Exception e) {
            log.error("更新任务状态为失败失败：taskStatusId={}, contractNo={}", taskStatusId, contractNo, e);
        }
    }

    /**
     * 创建全局任务状态记录（用于标记特殊情况，如无合同等）
     */
    public void createGlobalTaskStatus(LocalDate taskDate, String processStatus, int billCount, String remark) {
        try {
            // 使用特殊的合同编号来标识全局任务状态
            String globalContractCode = "GLOBAL_TASK_" + taskDate.toString().replace("-", "");

            // 检查是否已存在全局任务状态记录
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, globalContractCode, null);

            if (!taskStatusList.isEmpty()) {
                // 更新现有记录
                BbpmPenaltyTaskStatusVo taskStatus = taskStatusList.get(0);
                taskStatus.setProcessStatus(processStatus);
                taskStatus.setBillCount(billCount);
                taskStatus.setEndTime(new Date());
                taskStatus.setErrorMessage(remark);
                penaltyTaskStatusService.updateByIdRecord(taskStatus);
                log.info("已更新全局任务状态记录：{}", globalContractCode);
            } else {
                // 创建新的全局任务状态记录
                BbpmPenaltyTaskStatusVo globalTaskStatus = new BbpmPenaltyTaskStatusVo();
                globalTaskStatus.setTaskDate(taskDate);
                globalTaskStatus.setContractCode(globalContractCode);
                globalTaskStatus.setProcessStatus(processStatus);
                globalTaskStatus.setBillCount(billCount);
                globalTaskStatus.setStartTime(new Date());
                globalTaskStatus.setEndTime(new Date());
                globalTaskStatus.setErrorMessage(remark);
                globalTaskStatus.setDelFlag(1);

                penaltyTaskStatusService.insertRecord(globalTaskStatus);
                log.info("已创建全局任务状态记录：{}", globalContractCode);
            }

        } catch (Exception e) {
            log.error("创建全局任务状态记录失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 创建任务完成统计（记录到全局任务状态表）
     */
    public void createTaskCompletionSummary(LocalDate taskDate, int totalContracts, int processedContracts) {
        try {
            // 统计各状态的任务数量
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, null, null);

            int completedCount = 0;
            int failedCount = 0;
            int processingCount = 0;

            for (BbpmPenaltyTaskStatusVo task : taskStatusList) {
                String status = task.getProcessStatus();
                if ("2".equals(status)) {
                    completedCount++;
                } else if ("3".equals(status)) {
                    failedCount++;
                } else if ("1".equals(status)) {
                    processingCount++;
                }
            }

            // 创建全局任务统计记录
            String summaryMessage = String.format(
                "任务执行统计 - 总合同数:%d, 处理合同数:%d, 已完成:%d, 失败:%d, 仍在处理:%d",
                totalContracts, processedContracts, completedCount, failedCount, processingCount);

            createGlobalTaskStatus(taskDate, "2", totalContracts, summaryMessage);
            log.info(summaryMessage);

        } catch (Exception e) {
            log.error("创建任务完成统计失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 更新所有处理中的任务状态为失败（用于主任务异常处理）
     */
    public void updateAllProcessingTasksToFailed(LocalDate taskDate, String errorMessage) {
        try {
            log.info("开始更新所有处理中的任务状态为失败，任务日期：{}", taskDate);

            // 查询所有处理中的任务（状态为1）
            List<BbpmPenaltyTaskStatusVo> processingTasks = penaltyDataService.queryTaskStatusList(taskDate, null, "1");

            if (!processingTasks.isEmpty()) {
                int failedCount = 0;
                for (BbpmPenaltyTaskStatusVo taskStatus : processingTasks) {
                    try {
                        taskStatus.setProcessStatus("3"); // 3-失败
                        taskStatus.setEndTime(new Date());
                        taskStatus.setErrorMessage("主任务异常终止：" + errorMessage);
                        penaltyTaskStatusService.updateByIdRecord(taskStatus);
                        failedCount++;
                    } catch (Exception e) {
                        log.error("更新任务状态失败：taskStatusId={}", taskStatus.getTaskStatusId(), e);
                    }
                }
                log.info("已将 {} 个处理中的任务状态更新为失败", failedCount);
            } else {
                log.info("没有找到处理中的任务需要更新为失败状态");
            }

        } catch (Exception e) {
            log.error("批量更新任务状态为失败时发生异常：{}", e.getMessage(), e);
        }
    }
}
