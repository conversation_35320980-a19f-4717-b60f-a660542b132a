package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeDailyDetailEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeDailyDetailMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeDailyDetailService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金每日计算明细 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyFeeDailyDetailServiceImpl extends McpBaseServiceImpl<BbpmPenaltyFeeDailyDetailEntity> implements IBbpmPenaltyFeeDailyDetailService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyFeeDailyDetailMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyFeeDailyDetailService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyFeeDailyDetailVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyFeeDailyDetailEntity entity = new BbpmPenaltyFeeDailyDetailEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setDailyDetailId(null);
        if(!baseService.insert(entity)) {
            log.error("违约金每日计算明细新增失败:" + entity.toString());
            throw new McpException("违约金每日计算明细新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getDailyDetailId(),1)) {
                log.error("违约金每日计算明细新增后保存历史失败:" + entity.toString());
                throw new McpException("违约金每日计算明细新增后保存历史失败");
            }

            log.debug("违约金每日计算明细新增成功:"+entity.getDailyDetailId());
            return entity.getDailyDetailId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyFeeDailyDetailVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyFeeDailyDetailEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyFeeDailyDetailVo item:voList) {
            BbpmPenaltyFeeDailyDetailEntity entity = new BbpmPenaltyFeeDailyDetailEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyFeeDailyDetailEntity item:entityList){
            item.setDailyDetailId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("违约金每日计算明细新增失败");
            throw new McpException("违约金每日计算明细新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyFeeDailyDetailEntity::getDailyDetailId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("违约金每日计算明细批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("违约金每日计算明细批量新增后保存历史失败");
            }

            log.debug("违约金每日计算明细新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param dailyDetailId 需要删除的每日明细ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String dailyDetailId) {
        if(!StringUtils.isEmpty(dailyDetailId)) {
            if(!baseService.saveOperationHisById(dailyDetailId,3)) {
                log.error("违约金每日计算明细删除后保存历史失败:" + dailyDetailId);
                throw new McpException("违约金每日计算明细删除后保存历史失败");
            }

            if(!baseService.removeById(dailyDetailId)) {
                log.error("违约金每日计算明细删除失败");
                throw new McpException("违约金每日计算明细删除失败"+dailyDetailId);
            }
        } else {
            throw new McpException("违约金每日计算明细删除失败每日明细ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param dailyDetailIdList 需要删除的每日明细ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> dailyDetailIdList) {
        if(!CollectionUtils.isEmpty(dailyDetailIdList)) {
            int oldSize = dailyDetailIdList.size();
            dailyDetailIdList = dailyDetailIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(dailyDetailIdList) || oldSize != dailyDetailIdList.size()) {
                throw new McpException("违约金每日计算明细批量删除失败 存在主键id为空的记录"+StringUtils.join(dailyDetailIdList));
            }

            if(!baseService.saveOperationHisByIds(dailyDetailIdList,3)) {
                log.error("违约金每日计算明细批量删除后保存历史失败:" + StringUtils.join(dailyDetailIdList));
                throw new McpException("违约金每日计算明细批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(dailyDetailIdList)) {
                log.error("违约金每日计算明细批量删除失败");
                throw new McpException("违约金每日计算明细批量删除失败"+StringUtils.join(dailyDetailIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyFeeDailyDetailVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeDailyDetailEntity entity = new BbpmPenaltyFeeDailyDetailEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getDailyDetailId())) {
                throw new McpException("违约金每日计算明细更新失败传入每日明细ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("违约金每日计算明细更新失败");
                throw new McpException("违约金每日计算明细更新失败"+entity.getDailyDetailId());
            } else {
                if(!baseService.saveOperationHisById(entity.getDailyDetailId(),2)) {
                    log.error("违约金每日计算明细更新后保存历史失败:" + entity.getDailyDetailId());
                    throw new McpException("违约金每日计算明细更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金每日计算明细更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyFeeDailyDetailVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeDailyDetailEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeDailyDetailVo item:voList){
                BbpmPenaltyFeeDailyDetailEntity entity = new BbpmPenaltyFeeDailyDetailEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getDailyDetailId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("违约金每日计算明细批量更新失败 存在每日明细ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("违约金每日计算明细批量更新失败");
                throw new McpException("违约金每日计算明细批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getDailyDetailId())).map(BbpmPenaltyFeeDailyDetailEntity::getDailyDetailId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("违约金每日计算明细批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金每日计算明细批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyFeeDailyDetailVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeDailyDetailEntity entity = new BbpmPenaltyFeeDailyDetailEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("违约金每日计算明细保存失败");
                throw new McpException("违约金每日计算明细保存失败"+entity.getDailyDetailId());
            } else {
                if(!baseService.saveOperationHisById(entity.getDailyDetailId(),4)) {
                    log.error("违约金每日计算明细保存后保存历史失败:" + entity.getDailyDetailId());
                    throw new McpException("违约金每日计算明细保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金每日计算明细保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金每日计算明细
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyFeeDailyDetailVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeDailyDetailEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeDailyDetailVo item:voList){
                BbpmPenaltyFeeDailyDetailEntity entity = new BbpmPenaltyFeeDailyDetailEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("违约金每日计算明细批量保存失败");
                throw new McpException("违约金每日计算明细批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getDailyDetailId())).map(BbpmPenaltyFeeDailyDetailEntity::getDailyDetailId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("违约金每日计算明细批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金每日计算明细批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param dailyDetailId 需要查询的每日明细ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyFeeDailyDetailVo selectByIdRecord(String dailyDetailId) {
        BbpmPenaltyFeeDailyDetailVo vo = new BbpmPenaltyFeeDailyDetailVo();

        if(!StringUtils.isEmpty(dailyDetailId)) {
            BbpmPenaltyFeeDailyDetailEntity entity = baseService.selectById(dailyDetailId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyFeeDailyDetailPageResultVo>> selectByPageRecord(BbpmPenaltyFeeDailyDetailPageVo vo) {
        List<BbpmPenaltyFeeDailyDetailPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmPenaltyFeeDailyDetailVo> selectListByCondition(BbpmPenaltyFeeDailyDetailVo vo) {
        return baseMapper.selectListByCondition(vo);
    }
}
