package com.bonc.ioc.bzf.business.penalty.service;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeStageSummaryEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金分阶段汇总表 服务类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
public interface IBbpmPenaltyFeeStageSummaryService extends IMcpBaseService<BbpmPenaltyFeeStageSummaryEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    String insertRecord(BbpmPenaltyFeeStageSummaryVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    List<String> insertBatchRecord(List<BbpmPenaltyFeeStageSummaryVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param stageSummaryId 需要删除的分阶段汇总ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdRecord(String stageSummaryId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param stageSummaryIdList 需要删除的分阶段汇总ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdsRecord(List<String> stageSummaryIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateByIdRecord(BbpmPenaltyFeeStageSummaryVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateBatchByIdRecord(List<BbpmPenaltyFeeStageSummaryVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveByIdRecord(BbpmPenaltyFeeStageSummaryVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveBatchByIdRecord(List<BbpmPenaltyFeeStageSummaryVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param stageSummaryId 需要查询的分阶段汇总ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    BbpmPenaltyFeeStageSummaryVo selectByIdRecord(String stageSummaryId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    PageResult<List<BbpmPenaltyFeeStageSummaryPageResultVo>> selectByPageRecord(BbpmPenaltyFeeStageSummaryPageVo vo);

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    List<BbpmPenaltyFeeStageSummaryVo> selectListByCondition(BbpmPenaltyFeeStageSummaryVo vo);
}
