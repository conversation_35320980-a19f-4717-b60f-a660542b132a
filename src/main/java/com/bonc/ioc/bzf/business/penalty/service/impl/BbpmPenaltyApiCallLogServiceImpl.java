package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyApiCallLogEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyApiCallLogMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyApiCallLogService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 接口调用记录表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyApiCallLogServiceImpl extends McpBaseServiceImpl<BbpmPenaltyApiCallLogEntity> implements IBbpmPenaltyApiCallLogService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyApiCallLogMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyApiCallLogService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyApiCallLogVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyApiCallLogEntity entity = new BbpmPenaltyApiCallLogEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setApiCallLogId(null);
        if(!baseService.insert(entity)) {
            log.error("接口调用记录表新增失败:" + entity.toString());
            throw new McpException("接口调用记录表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getApiCallLogId(),1)) {
                log.error("接口调用记录表新增后保存历史失败:" + entity.toString());
                throw new McpException("接口调用记录表新增后保存历史失败");
            }

            log.debug("接口调用记录表新增成功:"+entity.getApiCallLogId());
            return entity.getApiCallLogId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyApiCallLogVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyApiCallLogEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyApiCallLogVo item:voList) {
            BbpmPenaltyApiCallLogEntity entity = new BbpmPenaltyApiCallLogEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyApiCallLogEntity item:entityList){
            item.setApiCallLogId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("接口调用记录表新增失败");
            throw new McpException("接口调用记录表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyApiCallLogEntity::getApiCallLogId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("接口调用记录表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("接口调用记录表批量新增后保存历史失败");
            }

            log.debug("接口调用记录表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param apiCallLogId 需要删除的接口调用日志ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String apiCallLogId) {
        if(!StringUtils.isEmpty(apiCallLogId)) {
            if(!baseService.saveOperationHisById(apiCallLogId,3)) {
                log.error("接口调用记录表删除后保存历史失败:" + apiCallLogId);
                throw new McpException("接口调用记录表删除后保存历史失败");
            }

            if(!baseService.removeById(apiCallLogId)) {
                log.error("接口调用记录表删除失败");
                throw new McpException("接口调用记录表删除失败"+apiCallLogId);
            }
        } else {
            throw new McpException("接口调用记录表删除失败接口调用日志ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param apiCallLogIdList 需要删除的接口调用日志ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> apiCallLogIdList) {
        if(!CollectionUtils.isEmpty(apiCallLogIdList)) {
            int oldSize = apiCallLogIdList.size();
            apiCallLogIdList = apiCallLogIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(apiCallLogIdList) || oldSize != apiCallLogIdList.size()) {
                throw new McpException("接口调用记录表批量删除失败 存在主键id为空的记录"+StringUtils.join(apiCallLogIdList));
            }

            if(!baseService.saveOperationHisByIds(apiCallLogIdList,3)) {
                log.error("接口调用记录表批量删除后保存历史失败:" + StringUtils.join(apiCallLogIdList));
                throw new McpException("接口调用记录表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(apiCallLogIdList)) {
                log.error("接口调用记录表批量删除失败");
                throw new McpException("接口调用记录表批量删除失败"+StringUtils.join(apiCallLogIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的接口调用记录表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyApiCallLogVo vo) {
        if(vo != null) {
            BbpmPenaltyApiCallLogEntity entity = new BbpmPenaltyApiCallLogEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getApiCallLogId())) {
                throw new McpException("接口调用记录表更新失败传入接口调用日志ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("接口调用记录表更新失败");
                throw new McpException("接口调用记录表更新失败"+entity.getApiCallLogId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApiCallLogId(),2)) {
                    log.error("接口调用记录表更新后保存历史失败:" + entity.getApiCallLogId());
                    throw new McpException("接口调用记录表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("接口调用记录表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的接口调用记录表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyApiCallLogVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyApiCallLogEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyApiCallLogVo item:voList){
                BbpmPenaltyApiCallLogEntity entity = new BbpmPenaltyApiCallLogEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getApiCallLogId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("接口调用记录表批量更新失败 存在接口调用日志ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("接口调用记录表批量更新失败");
                throw new McpException("接口调用记录表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApiCallLogId())).map(BbpmPenaltyApiCallLogEntity::getApiCallLogId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("接口调用记录表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("接口调用记录表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的接口调用记录表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyApiCallLogVo vo) {
        if(vo != null) {
            BbpmPenaltyApiCallLogEntity entity = new BbpmPenaltyApiCallLogEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("接口调用记录表保存失败");
                throw new McpException("接口调用记录表保存失败"+entity.getApiCallLogId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApiCallLogId(),4)) {
                    log.error("接口调用记录表保存后保存历史失败:" + entity.getApiCallLogId());
                    throw new McpException("接口调用记录表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("接口调用记录表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的接口调用记录表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyApiCallLogVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyApiCallLogEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyApiCallLogVo item:voList){
                BbpmPenaltyApiCallLogEntity entity = new BbpmPenaltyApiCallLogEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("接口调用记录表批量保存失败");
                throw new McpException("接口调用记录表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApiCallLogId())).map(BbpmPenaltyApiCallLogEntity::getApiCallLogId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("接口调用记录表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("接口调用记录表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param apiCallLogId 需要查询的接口调用日志ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyApiCallLogVo selectByIdRecord(String apiCallLogId) {
        BbpmPenaltyApiCallLogVo vo = new BbpmPenaltyApiCallLogVo();

        if(!StringUtils.isEmpty(apiCallLogId)) {
            BbpmPenaltyApiCallLogEntity entity = baseService.selectById(apiCallLogId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyApiCallLogPageResultVo>> selectByPageRecord(BbpmPenaltyApiCallLogPageVo vo) {
        List<BbpmPenaltyApiCallLogPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
