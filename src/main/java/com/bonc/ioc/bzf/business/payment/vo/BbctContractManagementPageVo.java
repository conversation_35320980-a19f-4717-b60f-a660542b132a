package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 合同主表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-20
 * @change 2023-05-20 by sx for init
 */
@ApiModel(value="BbctContractManagementPageVo对象", description="合同主表")
@Data
public class BbctContractManagementPageVo extends McpBasePageVo implements Serializable{



    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空",groups = {UpdateValidatorGroup.class})
    private String contractId;

    /**
     * 合同分类编号
     */
    @ApiModelProperty(value = "合同分类编号")
    private String contractTypeCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 所属趸租合同编号
     */
    @ApiModelProperty(value = "所属趸租合同编号")
    private String parentContractCode;

    @ApiModelProperty(value = "续租来源合同编号")
    private String reletSourceContractCode;

    @ApiModelProperty(value = "左侧树选择的值")
    private String treeValue;

    @ApiModelProperty(value = "组团")
    private String groupNo;

    @ApiModelProperty(value = "楼号")
    private String buildingNo;

    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 合同状态（1已生效、2未生效、3已结束、4终止）
     */
    @ApiModelProperty(value = "合同状态（1已生效、2未生效、3已结束、4终止、5已逾期、6作废、7冻结）")
    private String contractStatus;

    /**
     * 合同模板ID
     */
    @ApiModelProperty(value = "合同模板ID")
    private String contractTemplateId;

    /**
     * 合同审核人ID
     */
    @ApiModelProperty(value = "合同审核人ID")
    private String examineUserId;

    /**
     * 合同审核人姓名
     */
    @ApiModelProperty(value = "合同审核人姓名")
    private String examineUserName;

    /**
     * pdf文件id
     */
    @ApiModelProperty(value = "pdf文件id")
    private String pdfFileId;

    /**
     * 审核后PDF文件ID
     */
    @ApiModelProperty(value = "审核后PDF文件ID")
    private String newpdfFileId;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginTime;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndTime;

    /**
     * 甲方ID
     */
    @ApiModelProperty(value = "甲方ID")
    private String firstId;

    /**
     * 甲方名称
     */
    @ApiModelProperty(value = "甲方名称")
    private String firstName;

    /**
     * 甲方信用代码
     */
    @ApiModelProperty(value = "甲方信用代码")
    private String firstSocialCode;

    /**
     * 合同总份数
     */
    @ApiModelProperty(value = "合同总份数")
    private String totalContractNum;


    /**
     * 签约房源套数
     */
    @ApiModelProperty(value = "签约房源套数")
    private Integer totalSets;

    @ApiModelProperty(value = "租赁家具家电房屋数量")
    private Integer furnitureSets;

    /**
     * 总租赁面积
     */
    @ApiModelProperty(value = "总租赁面积")
    private String totalArea;

    @ApiModelProperty("面积类型(1.建筑面积 2.套内建筑面积)")
    private String areaType;

    /**
     * 单缴费周期租金
     */
    @ApiModelProperty(value = "单缴费周期租金")
    private Double leaseTermRent;

    @ApiModelProperty(value = "押金")
    private String deposit;
    /**
     * 押金标准code(1.1个月 2.2个月 3.3个月)
     */
    @ApiModelProperty(value = "押金标准code(1.1个月 2.2个月 3.3个月)")
    private String cashPledgeCode;

    /**
     * 押金标准名称
     */
    @ApiModelProperty(value = "押金标准名称")
    private String cashPledgeName;

    /**
     * 缴费周期code(01.月付 02.季度付 03.半年付 04.年付)
     */
    @ApiModelProperty(value = "缴费周期code(01.月付 02.季度付 03.半年付 04.年付)")
    private String paymentCycleCode;

    /**
     * 缴费周期名称
     */
    @ApiModelProperty(value = "缴费周期名称")
    private String paymentCycleName;

    /**
     * 缴费货币
     */
    @ApiModelProperty(value = "缴费货币")
    private String paymentCurrency;

    /**
     * 总缴费租金
     */
    @ApiModelProperty(value = "总缴费租金")
    private Double paymentRent;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private String payMode;

    @ApiModelProperty(value = "缴费类型(1.比例 2.金额)")
    private String payType;

    /**
     * 交付日期
     */
    @ApiModelProperty(value = "交付日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /**
     * 业务分类编号1
     */
    @ApiModelProperty(value = "业务分类编号1（01公租，07保租，02商业，04家具家电公租，05家具家电保租）")
    private String businessTypeCode1;

    /**
     * 业务分类名称1
     */
    @ApiModelProperty(value = "业务分类名称1")
    private String businessTypeName1;

    /**
     * 业务分类编号2
     */
    @ApiModelProperty(value = "业务分类编号2(房屋：01散租，02趸租，03管理协议；04,06家具家电个人，05家具家电企业)")
    private String businessTypeCode2;

    /**
     * 业务分类名称2
     */
    @ApiModelProperty(value = "业务分类名称2")
    private String businessTypeName2;

    /**
     * 业务分类编号3
     */
    @ApiModelProperty(value = "业务分类编号3")
    private String businessTypeCode3;

    /**
     * 业务分类名称3
     */
    @ApiModelProperty(value = "业务分类名称3")
    private String businessTypeName3;

    /**
     * 业务唯一标识
     */
    @ApiModelProperty(value = "业务唯一标识")
    private String businessId;

    /**
     * 合同终止时间
     */
    @ApiModelProperty(value = "合同终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminationDate;

    @ApiModelProperty(value = "是否退租（0否，1是）")
    private String rentTerminate;

    /**
     * 变更次数
     */
    @ApiModelProperty(value = "变更次数")
    private Integer changeNum;

    /**
     * 最后变更日期
     */
    @ApiModelProperty(value = "最后变更日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date changeLastDate;

    /**
     * 变更状态
     */
    @ApiModelProperty(value = "变更状态")
    private String changeCheckStatus;

    @ApiModelProperty("乙方缴费比例")
    private String secondPayPercent;

    @ApiModelProperty("丙方缴费比例")
    private String thirdPayPercent;

    @ApiModelProperty("乙方缴费金额")
    private String secondPayMoney;

    @ApiModelProperty("丙方缴费金额")
    private String thirdPayMoney;

    @ApiModelProperty("是否报盘(0.否 1.是)")
    private String offer;

    @ApiModelProperty("是否报送热力公司(0.否 1.是)")
    private String submitHeatingCompany;

    @ApiModelProperty("合同通过日期（签约日期）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date passTime;

    @ApiModelProperty("租金标准code(01.公租租金标准 02.人才租金标准 03.市场租金标准)")
    private String rentStandardCode;

    @ApiModelProperty("基础租金计算方式code")
    private String baseRentCalculate;

    @ApiModelProperty("基础租金计算方式名称")
    private String baseRentCalculateName;

    /**
     * 签约开始时间
     */
    @ApiModelProperty(value = "查询参数-签约开始时间")
    private String beginSigningTime;
    /**
     * 签约截止时间
     */
    @ApiModelProperty(value = "查询参数-签约截止时间")
    private String endSigningTime;

    /**
     * 是否欠缴(是、否)
     */
    @ApiModelProperty(value = "是否欠缴(是、否)")
    private String isOverdue;

    @ApiModelProperty("合同后付款(0.否 1.是)")
    private String afterContractPay;

    @ApiModelProperty("甲方账户id")
    private String firstAccountId;

    @ApiModelProperty("甲方账户名称")
    private String firstAccountName;

    @ApiModelProperty("甲方开户行")
    private String firstBankNameCode;

    @ApiModelProperty("甲方开户行名称")
    private String firstBankName;

    @ApiModelProperty("五期合同（01正常、02候审期、03过渡期、04占用期、05欠费）")
    @McpDictPoint(dictCode = "PAHSE_FIVE",overTransCopyTo = "pahseFiveName")
    private String pahseFive;

    @ApiModelProperty("五期合同值")
    private String pahseFiveName;

    @ApiModelProperty("签约结果id")
    private String signId;

    @ApiModelProperty(value = "签约房间类型(01.普通公租房 03.人才公租房)")
    private String roomType;

    @ApiModelProperty("合同信息扩展")
    private String contractExtend;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String ext4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String ext5;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "租期")
    private String leaseTerm;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityBuildingName;

    /**
     * 房源地址（产品名称）
     */
    @ApiModelProperty(value = "房源地址（产品名称）")
    private String productName;

    /**
     * 房源产品编号
     */
    @ApiModelProperty(value = "房源产品编号")
    private String productNo;

    /**
     * 房源产品编号集合用','拆分
     */
    @ApiModelProperty(value = "房源产品编号集合用','拆分")
    private String productNos;

    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;

    /**
     * 散户或者企业名称
     */
    @ApiModelProperty(value = "散户或者企业名称")
    private String customerName;

    @ApiModelProperty(value = "租户电话")
    private String customerTel;

    @ApiModelProperty(value = "00:散户  01：企业")
    private String customerType;

    @ApiModelProperty(value = "证件类型")
    private String customerIdType;

    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
    private String customerCreditCode;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
    private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
    private String consignorMobile;

    @ApiModelProperty(value = "查询参数-公司编号")
    private String companyNo;

    @ApiModelProperty(value = "查询参数-公司名称（趸租、管理协议点击左侧时）")
    private String selectedCompanyName;

    @ApiModelProperty(value = "签约时间排序，正序asc,倒序desc")
    private String signOrder;

    @ApiModelProperty(value = "业务端app搜索框查询参数")
    private String queryCriteria;

    @ApiModelProperty(value = "查询参数，合同开始日期开始时间")
    private String contractBeginTimeStart;

    @ApiModelProperty(value = "查询参数，合同开始日期结束时间")
    private String contractBeginTimeEnd;

    @ApiModelProperty(value = "查询参数，合同结束日期开始时间")
    private String contractEndTimeStart;

    @ApiModelProperty(value = "查询参数，合同结束日期结束时间")
    private String contractEndTimeEnd;

    @ApiModelProperty(value = "查询参数，合同开始日期")
    private String beginTime;

    @ApiModelProperty(value = "查询参数，合同开始日期")
    private String endTime;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "查询参数-合同编号")
    private List<String> contractNos;

    @ApiModelProperty(value = "合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.主承租人合同变更合同 6.购房补充协议 7.趸租补充协议)")
    private String contractType;

    @ApiModelProperty(value = "来源合同编号")
    private String sourceContractCode;

    @ApiModelProperty(value = "虚拟合同来源合同号")
    private String virtualSourceCode;

    @ApiModelProperty(value = "是否虚拟合同(0.否 1.是)")
    private String virtualContract;

    @ApiModelProperty(value = "押金标准金额")
    private String cashPledgeMoney;

    @ApiModelProperty(value = "押金支付方式(1.企业付 2.个人付 3.比例支付)")
    private String cashPledgePayMode;

    @ApiModelProperty(value = "押金缴费类型(1.比例 2.金额)")
    private String cashPledgePayType;

    @ApiModelProperty(value = "押金缴费比例")
    private String cashPledgePayPercent;

    @ApiModelProperty(value = "押金缴费金额")
    private String cashPledgePayMoney;

    @ApiModelProperty(value = "是否有增值服务费(0.否 1.是)")
    private String serviceCharge;

    @ApiModelProperty(value = "增值服务费租金标准类型(1.比例 2.金额)")
    private String serviceChargeType;

    @ApiModelProperty(value = "增值服务费标准(月租金百分比)")
    private String serviceChargePercent;

    @ApiModelProperty(value = "增值服务费标准(金额)")
    private String serviceChargeMoney;

    @ApiModelProperty(value = "是否有递增(0.否 1.是)")
    private String increase;

    @ApiModelProperty(value = "递增类型(1.租金 2.租金+增值服务费 3.增值服务费")
    private String increaseType;

    @ApiModelProperty(value = "递增json")
    private String increaseJson;

    @ApiModelProperty(value = "支付标准(1.统一标准 2.非同一标准)")
    private String payStandard;

    @ApiModelProperty(value = "增值服务费缴费类型(1.比例 2.金额)")
    private String serviceChargePayType;

    @ApiModelProperty(value = "增值服务费缴费比例")
    private String serviceChargePayPercent;

    @ApiModelProperty(value = "增值服务费缴费金额")
    private String serviceChargePayMoney;

    @ApiModelProperty(value = "租赁依据")
    private String according;

    @ApiModelProperty(value = "付款银行账户id")
    private String payingBankAccountId;

    @ApiModelProperty(value = "付款银行账户名称")
    private String payingBankAccountName;

    @ApiModelProperty(value = "付款银行开户行")
    private String payingBankNameCode;

    @ApiModelProperty(value = "付款银行开户行名称")
    private String payingBankName;

    @ApiModelProperty(value = "付款银行开户行支行")
    private String payingSubBankNameCode;

    @ApiModelProperty(value = "付款银行开户行支行名称")
    private String payingSubBankName;

    @ApiModelProperty(value = "是否查询独立管理协议，1是")
    private String isIndependentAgreement;

    @ApiModelProperty(value = "是否查询由承租人缴纳租金和押金，1是")
    private String isLesseePay;

    @ApiModelProperty(value = "排序字段")
    private String sortField;

    @ApiModelProperty(value = "排序方式（ascend正序，descend倒序）,默认正序")
    private String sortType;

    @ApiModelProperty(value = "租金标准（元/m²/月）")
    private String rentStandardNo;

    @ApiModelProperty(value = "公租房备案编号")
    private String customerPublicRecordNo;

    @ApiModelProperty(value = "查询参数，排除的分类2，01散租，02趸租，03管理协议")
    private String excludeTypeCode2;

    @ApiModelProperty("附件名前缀")
    private String attachmentPrefix;


    @ApiModelProperty("实际居住人姓名")
    private String checkInUserName;

    @ApiModelProperty("实际居住人手机号")
    private String checkInPhone;

    @ApiModelProperty("实际居住人人证件类型")
    private String checkInIdType;

    @ApiModelProperty("实际居住人证件号码")
    private String checkInIdNum;

    @ApiModelProperty("是否使用数据权限")
    private String isProject;
}
