package com.bonc.ioc.bzf.business.penalty.vo.contractExtend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value="合同扩展", description="合同扩展")
@Data
public class ContractOtherInfo {

    @ApiModelProperty(value = "合同费用(02-07.租金包含物业费 02-08.租金+物业费 02-09.仅租金（物业费由物业公司收取）")
    private String contractFees;

    @ApiModelProperty(value = "租金税率")
    private Double rentTaxRate;

    @ApiModelProperty(value = "物业费税率")
    private Double propTaxRate;

    @ApiModelProperty(value = "租金免租期")
    private FreeVo rentFreeVo;

    @ApiModelProperty(value = "物业费免租期")
    private FreeVo propFreeVo;
    /**
     * 公司注册地址
     */
    @ApiModelProperty(value = "公司注册地址")
    private String registeredAddress;


    @ApiModelProperty(value = "水印")
    private WatermarkVo watermarkVo;

    /**
     * 是否支持先开票后付款	是否支持先开票后付款	是否支持先开票后付款(0.否 1.是)
     */
    @ApiModelProperty(value = "是否支持先开票后付款	是否支持先开票后付款	是否支持先开票后付款(0.否 1.是)")
    private String invoicingBeforePayment;

    @ApiModelProperty(value = "每日逾期率")
    private BigDecimal dailyOverdueRate;

}
