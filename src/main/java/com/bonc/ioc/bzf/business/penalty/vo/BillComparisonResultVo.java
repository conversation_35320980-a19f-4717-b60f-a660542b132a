package com.bonc.ioc.bzf.business.penalty.vo;

import java.util.ArrayList;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;

/**
 * 账单对比结果VO
 */
public class BillComparisonResultVo {
    private final List<BbpmBillManagementPageResultVo> newBills;
    private final List<com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo> statusChangedBills;
    private final List<BbpmBillManagementPageResultVo> unchangedBills;
    
    public BillComparisonResultVo(List<BbpmBillManagementPageResultVo> newBills,
                              List<com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo> statusChangedBills,
                              List<BbpmBillManagementPageResultVo> unchangedBills) {
        this.newBills = newBills != null ? newBills : new ArrayList<>();
        this.statusChangedBills = statusChangedBills != null ? statusChangedBills : new ArrayList<>();
        this.unchangedBills = unchangedBills != null ? unchangedBills : new ArrayList<>();
    }
    
    public List<BbpmBillManagementPageResultVo> getNewBills() { 
        return newBills; 
    }
    
    public List<com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo> getStatusChangedBills() { 
        return statusChangedBills; 
    }
    
    public List<BbpmBillManagementPageResultVo> getUnchangedBills() { 
        return unchangedBills; 
    }
}
