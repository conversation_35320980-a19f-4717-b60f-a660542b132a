package com.bonc.ioc.bzf.business.penalty.dao;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyTaskStatusEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import java.util.List;

/**
 * 任务执行状态 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Mapper
public interface BbpmPenaltyTaskStatusMapper extends McpBaseMapper<BbpmPenaltyTaskStatusEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change 2025-08-08 by tbh for init
     */
    List<BbpmPenaltyTaskStatusPageResultVo> selectByPageCustom(@Param("vo") BbpmPenaltyTaskStatusPageVo vo );

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    List<BbpmPenaltyTaskStatusVo> selectListByCondition(@Param("vo") BbpmPenaltyTaskStatusVo vo );
}
