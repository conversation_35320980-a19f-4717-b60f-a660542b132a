package com.bonc.ioc.bzf.business.penalty.service;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeFinalizedBillEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金计费完结账单 服务类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
public interface IBbpmPenaltyFeeFinalizedBillService extends IMcpBaseService<BbpmPenaltyFeeFinalizedBillEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    String insertRecord(BbpmPenaltyFeeFinalizedBillVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    List<String> insertBatchRecord(List<BbpmPenaltyFeeFinalizedBillVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param finalizedBillId 需要删除的完结账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdRecord(String finalizedBillId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param finalizedBillIdList 需要删除的完结账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdsRecord(List<String> finalizedBillIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateByIdRecord(BbpmPenaltyFeeFinalizedBillVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateBatchByIdRecord(List<BbpmPenaltyFeeFinalizedBillVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveByIdRecord(BbpmPenaltyFeeFinalizedBillVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金计费完结账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveBatchByIdRecord(List<BbpmPenaltyFeeFinalizedBillVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param finalizedBillId 需要查询的完结账单ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    BbpmPenaltyFeeFinalizedBillVo selectByIdRecord(String finalizedBillId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    PageResult<List<BbpmPenaltyFeeFinalizedBillPageResultVo>> selectByPageRecord(BbpmPenaltyFeeFinalizedBillPageVo vo);
}
