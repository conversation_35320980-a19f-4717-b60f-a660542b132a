package com.bonc.ioc.bzf.business.penalty.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 违约金计费完结账单 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@ApiModel(value="BbpmPenaltyFeeFinalizedBillPageVo对象", description="违约金计费完结账单")
public class BbpmPenaltyFeeFinalizedBillPageVo extends McpBasePageVo implements Serializable{


    /**
     * 完结账单ID
     */
    @ApiModelProperty(value = "完结账单ID")
    @NotBlank(message = "完结账单ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String finalizedBillId;

    /**
     * 试算账单ID
     */
    @ApiModelProperty(value = "试算账单ID")
                            private String trialBillId;

    /**
     * 账单ID（工银账单ID）
     */
    @ApiModelProperty(value = "账单ID（工银账单ID）")
                            private String billId;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
                            private String billCycle;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 房源名称（房源地址）
     */
    @ApiModelProperty(value = "房源名称（房源地址）")
                            private String houseName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
                            private String tenantCode;

    /**
     * 租户名称（商户名称）
     */
    @ApiModelProperty(value = "租户名称（商户名称）")
                            private String tenantName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate chargeSubjectBeginDate;

    /**
     * 收费科目终止日期
     */
    @ApiModelProperty(value = "收费科目终止日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate chargeSubjectEndDate;

    /**
     * 账单周期数值（从1开始递增）
     */
    @ApiModelProperty(value = "账单周期数值（从1开始递增）")
                            private Integer chargeSubjectPeriod;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate payableDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
                            private BigDecimal shouldPayAmount;

    /**
     * 实缴金额
     */
    @ApiModelProperty(value = "实缴金额")
                            private BigDecimal payedAmount;

    /**
     * 实际缴费日期（最后一次收款的银行回单日期）
     */
    @ApiModelProperty(value = "实际缴费日期（最后一次收款的银行回单日期）")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate actualPayDate;

    /**
     * 待缴金额（未缴金额）
     */
    @ApiModelProperty(value = "待缴金额（未缴金额）")
                            private BigDecimal replacePayAmount;

    /**
     * 逾期天数
     */
    @ApiModelProperty(value = "逾期天数")
                            private Integer overdueDays;

    /**
     * 每日逾期率
     */
    @ApiModelProperty(value = "每日逾期率")
                            private BigDecimal dailyOverdueRate;

    /**
     * 违约金总金额
     */
    @ApiModelProperty(value = "违约金总金额")
                            private BigDecimal totalPenaltyAmount;

    /**
     * 减免金额
     */
    @ApiModelProperty(value = "减免金额")
                            private BigDecimal reductionAmount;

    /**
     * 生成违约金账单金额
     */
    @ApiModelProperty(value = "生成违约金账单金额")
                            private BigDecimal billGenerationAmount;

    /**
     * 账单对应的收费科目
     */
    @ApiModelProperty(value = "账单对应的收费科目")
                            private String billChargeSubject;

    /**
     * 完结原因：1-已缴足额支付，2-退租办结
     */
    @ApiModelProperty(value = "完结原因：1-已缴足额支付，2-退租办结")
                            private String finalizedReason;

    /**
     * 完结日期
     */
    @ApiModelProperty(value = "完结日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate finalizedDate;

    /**
     * 违约金处置编号
     */
    @ApiModelProperty(value = "违约金处置编号")
                            private String penaltyDisposalNo;

    /**
     * 处置状态：1-未处置，2-处置中，3-已处置
     */
    @ApiModelProperty(value = "处置状态：1-未处置，2-处置中，3-已处置")
                            private String disposalStatus;

    /**
     * 处置类型：1-减免违约金，2-不减免违约金
     */
    @ApiModelProperty(value = "处置类型：1-减免违约金，2-不减免违约金")
                            private String disposalType;

    /**
     * 处置时间
     */
    @ApiModelProperty(value = "处置时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date processTime;

    /**
     * 处置备注
     */
    @ApiModelProperty(value = "处置备注")
                            private String processRemark;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private String delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * @return 完结账单ID
     */
    public String getFinalizedBillId() {
        return finalizedBillId;
    }

    public void setFinalizedBillId(String finalizedBillId) {
        this.finalizedBillId = finalizedBillId;
    }

    /**
     * @return 试算账单ID
     */
    public String getTrialBillId() {
        return trialBillId;
    }

    public void setTrialBillId(String trialBillId) {
        this.trialBillId = trialBillId;
    }

    /**
     * @return 账单ID（工银账单ID）
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 账单周期
     */
    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 房源名称（房源地址）
     */
    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 租户ID
     */
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * @return 租户名称（商户名称）
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 收费科目起始日期
     */
    public LocalDate getChargeSubjectBeginDate() {
        return chargeSubjectBeginDate;
    }

    public void setChargeSubjectBeginDate(LocalDate chargeSubjectBeginDate) {
        this.chargeSubjectBeginDate = chargeSubjectBeginDate;
    }

    /**
     * @return 收费科目终止日期
     */
    public LocalDate getChargeSubjectEndDate() {
        return chargeSubjectEndDate;
    }

    public void setChargeSubjectEndDate(LocalDate chargeSubjectEndDate) {
        this.chargeSubjectEndDate = chargeSubjectEndDate;
    }

    /**
     * @return 账单周期数值（从1开始递增）
     */
    public Integer getChargeSubjectPeriod() {
        return chargeSubjectPeriod;
    }

    public void setChargeSubjectPeriod(Integer chargeSubjectPeriod) {
        this.chargeSubjectPeriod = chargeSubjectPeriod;
    }

    /**
     * @return 应缴费日期
     */
    public LocalDate getPayableDate() {
        return payableDate;
    }

    public void setPayableDate(LocalDate payableDate) {
        this.payableDate = payableDate;
    }

    /**
     * @return 应缴金额
     */
    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    /**
     * @return 实缴金额
     */
    public BigDecimal getPayedAmount() {
        return payedAmount;
    }

    public void setPayedAmount(BigDecimal payedAmount) {
        this.payedAmount = payedAmount;
    }

    /**
     * @return 实际缴费日期（最后一次收款的银行回单日期）
     */
    public LocalDate getActualPayDate() {
        return actualPayDate;
    }

    public void setActualPayDate(LocalDate actualPayDate) {
        this.actualPayDate = actualPayDate;
    }

    /**
     * @return 待缴金额（未缴金额）
     */
    public BigDecimal getReplacePayAmount() {
        return replacePayAmount;
    }

    public void setReplacePayAmount(BigDecimal replacePayAmount) {
        this.replacePayAmount = replacePayAmount;
    }

    /**
     * @return 逾期天数
     */
    public Integer getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(Integer overdueDays) {
        this.overdueDays = overdueDays;
    }

    /**
     * @return 每日逾期率
     */
    public BigDecimal getDailyOverdueRate() {
        return dailyOverdueRate;
    }

    public void setDailyOverdueRate(BigDecimal dailyOverdueRate) {
        this.dailyOverdueRate = dailyOverdueRate;
    }

    /**
     * @return 违约金总金额
     */
    public BigDecimal getTotalPenaltyAmount() {
        return totalPenaltyAmount;
    }

    public void setTotalPenaltyAmount(BigDecimal totalPenaltyAmount) {
        this.totalPenaltyAmount = totalPenaltyAmount;
    }

    /**
     * @return 减免金额
     */
    public BigDecimal getReductionAmount() {
        return reductionAmount;
    }

    public void setReductionAmount(BigDecimal reductionAmount) {
        this.reductionAmount = reductionAmount;
    }

    /**
     * @return 生成违约金账单金额
     */
    public BigDecimal getBillGenerationAmount() {
        return billGenerationAmount;
    }

    public void setBillGenerationAmount(BigDecimal billGenerationAmount) {
        this.billGenerationAmount = billGenerationAmount;
    }

    /**
     * @return 账单对应的收费科目
     */
    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    /**
     * @return 完结原因：1-已缴足额支付，2-退租办结
     */
    public String getFinalizedReason() {
        return finalizedReason;
    }

    public void setFinalizedReason(String finalizedReason) {
        this.finalizedReason = finalizedReason;
    }

    /**
     * @return 完结日期
     */
    public LocalDate getFinalizedDate() {
        return finalizedDate;
    }

    public void setFinalizedDate(LocalDate finalizedDate) {
        this.finalizedDate = finalizedDate;
    }

    /**
     * @return 违约金处置编号
     */
    public String getPenaltyDisposalNo() {
        return penaltyDisposalNo;
    }

    public void setPenaltyDisposalNo(String penaltyDisposalNo) {
        this.penaltyDisposalNo = penaltyDisposalNo;
    }

    /**
     * @return 处置状态：1-未处置，2-处置中，3-已处置
     */
    public String getDisposalStatus() {
        return disposalStatus;
    }

    public void setDisposalStatus(String disposalStatus) {
        this.disposalStatus = disposalStatus;
    }

    /**
     * @return 处置类型：1-减免违约金，2-不减免违约金
     */
    public String getDisposalType() {
        return disposalType;
    }

    public void setDisposalType(String disposalType) {
        this.disposalType = disposalType;
    }

    /**
     * @return 处置时间
     */
    public Date getProcessTime(){
        if(processTime!=null){
            return (Date)processTime.clone();
        }else{
            return null;
        }
    }

    public void setProcessTime(Date processTime) {
        if(processTime==null){
            this.processTime = null;
        }else{
            this.processTime = (Date)processTime.clone();
        }
    }

    /**
     * @return 处置备注
     */
    public String getProcessRemark() {
        return processRemark;
    }

    public void setProcessRemark(String processRemark) {
        this.processRemark = processRemark;
    }

    /**
     * @return 有效标识
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyFeeFinalizedBillPageVo{" +
            "finalizedBillId=" + finalizedBillId +
            ", trialBillId=" + trialBillId +
            ", billId=" + billId +
            ", billCycle=" + billCycle +
            ", projectName=" + projectName +
            ", houseName=" + houseName +
            ", tenantCode=" + tenantCode +
            ", tenantName=" + tenantName +
            ", contractCode=" + contractCode +
            ", chargeSubjectBeginDate=" + chargeSubjectBeginDate +
            ", chargeSubjectEndDate=" + chargeSubjectEndDate +
            ", chargeSubjectPeriod=" + chargeSubjectPeriod +
            ", payableDate=" + payableDate +
            ", shouldPayAmount=" + shouldPayAmount +
            ", payedAmount=" + payedAmount +
            ", actualPayDate=" + actualPayDate +
            ", replacePayAmount=" + replacePayAmount +
            ", overdueDays=" + overdueDays +
            ", dailyOverdueRate=" + dailyOverdueRate +
            ", totalPenaltyAmount=" + totalPenaltyAmount +
            ", reductionAmount=" + reductionAmount +
            ", billGenerationAmount=" + billGenerationAmount +
            ", billChargeSubject=" + billChargeSubject +
            ", finalizedReason=" + finalizedReason +
            ", finalizedDate=" + finalizedDate +
            ", penaltyDisposalNo=" + penaltyDisposalNo +
            ", disposalStatus=" + disposalStatus +
            ", disposalType=" + disposalType +
            ", processTime=" + processTime +
            ", processRemark=" + processRemark +
            ", delFlag=" + delFlag +
            ", projectId=" + projectId +
        "}";
    }
}
