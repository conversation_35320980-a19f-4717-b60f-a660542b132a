package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeLedgerEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeLedgerMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeLedgerService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金台账 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyFeeLedgerServiceImpl extends McpBaseServiceImpl<BbpmPenaltyFeeLedgerEntity> implements IBbpmPenaltyFeeLedgerService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyFeeLedgerMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyFeeLedgerService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyFeeLedgerVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyFeeLedgerEntity entity = new BbpmPenaltyFeeLedgerEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setLedgerId(null);
        if(!baseService.insert(entity)) {
            log.error("违约金台账新增失败:" + entity.toString());
            throw new McpException("违约金台账新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getLedgerId(),1)) {
                log.error("违约金台账新增后保存历史失败:" + entity.toString());
                throw new McpException("违约金台账新增后保存历史失败");
            }

            log.debug("违约金台账新增成功:"+entity.getLedgerId());
            return entity.getLedgerId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyFeeLedgerVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyFeeLedgerEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyFeeLedgerVo item:voList) {
            BbpmPenaltyFeeLedgerEntity entity = new BbpmPenaltyFeeLedgerEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyFeeLedgerEntity item:entityList){
            item.setLedgerId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("违约金台账新增失败");
            throw new McpException("违约金台账新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyFeeLedgerEntity::getLedgerId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("违约金台账批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("违约金台账批量新增后保存历史失败");
            }

            log.debug("违约金台账新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param ledgerId 需要删除的台账ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String ledgerId) {
        if(!StringUtils.isEmpty(ledgerId)) {
            if(!baseService.saveOperationHisById(ledgerId,3)) {
                log.error("违约金台账删除后保存历史失败:" + ledgerId);
                throw new McpException("违约金台账删除后保存历史失败");
            }

            if(!baseService.removeById(ledgerId)) {
                log.error("违约金台账删除失败");
                throw new McpException("违约金台账删除失败"+ledgerId);
            }
        } else {
            throw new McpException("违约金台账删除失败台账ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param ledgerIdList 需要删除的台账ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> ledgerIdList) {
        if(!CollectionUtils.isEmpty(ledgerIdList)) {
            int oldSize = ledgerIdList.size();
            ledgerIdList = ledgerIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(ledgerIdList) || oldSize != ledgerIdList.size()) {
                throw new McpException("违约金台账批量删除失败 存在主键id为空的记录"+StringUtils.join(ledgerIdList));
            }

            if(!baseService.saveOperationHisByIds(ledgerIdList,3)) {
                log.error("违约金台账批量删除后保存历史失败:" + StringUtils.join(ledgerIdList));
                throw new McpException("违约金台账批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(ledgerIdList)) {
                log.error("违约金台账批量删除失败");
                throw new McpException("违约金台账批量删除失败"+StringUtils.join(ledgerIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金台账
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyFeeLedgerVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeLedgerEntity entity = new BbpmPenaltyFeeLedgerEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getLedgerId())) {
                throw new McpException("违约金台账更新失败传入台账ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("违约金台账更新失败");
                throw new McpException("违约金台账更新失败"+entity.getLedgerId());
            } else {
                if(!baseService.saveOperationHisById(entity.getLedgerId(),2)) {
                    log.error("违约金台账更新后保存历史失败:" + entity.getLedgerId());
                    throw new McpException("违约金台账更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金台账更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金台账
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyFeeLedgerVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeLedgerEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeLedgerVo item:voList){
                BbpmPenaltyFeeLedgerEntity entity = new BbpmPenaltyFeeLedgerEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getLedgerId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("违约金台账批量更新失败 存在台账ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("违约金台账批量更新失败");
                throw new McpException("违约金台账批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getLedgerId())).map(BbpmPenaltyFeeLedgerEntity::getLedgerId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("违约金台账批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金台账批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金台账
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyFeeLedgerVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeLedgerEntity entity = new BbpmPenaltyFeeLedgerEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("违约金台账保存失败");
                throw new McpException("违约金台账保存失败"+entity.getLedgerId());
            } else {
                if(!baseService.saveOperationHisById(entity.getLedgerId(),4)) {
                    log.error("违约金台账保存后保存历史失败:" + entity.getLedgerId());
                    throw new McpException("违约金台账保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金台账保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金台账
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyFeeLedgerVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeLedgerEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeLedgerVo item:voList){
                BbpmPenaltyFeeLedgerEntity entity = new BbpmPenaltyFeeLedgerEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("违约金台账批量保存失败");
                throw new McpException("违约金台账批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getLedgerId())).map(BbpmPenaltyFeeLedgerEntity::getLedgerId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("违约金台账批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金台账批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param ledgerId 需要查询的台账ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyFeeLedgerVo selectByIdRecord(String ledgerId) {
        BbpmPenaltyFeeLedgerVo vo = new BbpmPenaltyFeeLedgerVo();

        if(!StringUtils.isEmpty(ledgerId)) {
            BbpmPenaltyFeeLedgerEntity entity = baseService.selectById(ledgerId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyFeeLedgerPageResultVo>> selectByPageRecord(BbpmPenaltyFeeLedgerPageVo vo) {
        List<BbpmPenaltyFeeLedgerPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
