package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeStageSummaryEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeStageSummaryMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeStageSummaryService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金分阶段汇总表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyFeeStageSummaryServiceImpl extends McpBaseServiceImpl<BbpmPenaltyFeeStageSummaryEntity> implements IBbpmPenaltyFeeStageSummaryService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyFeeStageSummaryMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyFeeStageSummaryService baseService;

    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyFeeStageSummaryVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyFeeStageSummaryEntity entity = new BbpmPenaltyFeeStageSummaryEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setStageSummaryId(null);
        if(!baseService.insert(entity)) {
            log.error("违约金分阶段汇总表新增失败:" + entity.toString());
            throw new McpException("违约金分阶段汇总表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getStageSummaryId(),1)) {
                log.error("违约金分阶段汇总表新增后保存历史失败:" + entity.toString());
                throw new McpException("违约金分阶段汇总表新增后保存历史失败");
            }

            log.debug("违约金分阶段汇总表新增成功:"+entity.getStageSummaryId());
            return entity.getStageSummaryId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyFeeStageSummaryVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyFeeStageSummaryEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyFeeStageSummaryVo item:voList) {
            BbpmPenaltyFeeStageSummaryEntity entity = new BbpmPenaltyFeeStageSummaryEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyFeeStageSummaryEntity item:entityList){
            item.setStageSummaryId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("违约金分阶段汇总表新增失败");
            throw new McpException("违约金分阶段汇总表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyFeeStageSummaryEntity::getStageSummaryId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("违约金分阶段汇总表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("违约金分阶段汇总表批量新增后保存历史失败");
            }

            log.debug("违约金分阶段汇总表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param stageSummaryId 需要删除的分阶段汇总ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String stageSummaryId) {
        if(!StringUtils.isEmpty(stageSummaryId)) {
            if(!baseService.saveOperationHisById(stageSummaryId,3)) {
                log.error("违约金分阶段汇总表删除后保存历史失败:" + stageSummaryId);
                throw new McpException("违约金分阶段汇总表删除后保存历史失败");
            }

            if(!baseService.removeById(stageSummaryId)) {
                log.error("违约金分阶段汇总表删除失败");
                throw new McpException("违约金分阶段汇总表删除失败"+stageSummaryId);
            }
        } else {
            throw new McpException("违约金分阶段汇总表删除失败分阶段汇总ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param stageSummaryIdList 需要删除的分阶段汇总ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> stageSummaryIdList) {
        if(!CollectionUtils.isEmpty(stageSummaryIdList)) {
            int oldSize = stageSummaryIdList.size();
            stageSummaryIdList = stageSummaryIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(stageSummaryIdList) || oldSize != stageSummaryIdList.size()) {
                throw new McpException("违约金分阶段汇总表批量删除失败 存在主键id为空的记录"+StringUtils.join(stageSummaryIdList));
            }

            if(!baseService.saveOperationHisByIds(stageSummaryIdList,3)) {
                log.error("违约金分阶段汇总表批量删除后保存历史失败:" + StringUtils.join(stageSummaryIdList));
                throw new McpException("违约金分阶段汇总表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(stageSummaryIdList)) {
                log.error("违约金分阶段汇总表批量删除失败");
                throw new McpException("违约金分阶段汇总表批量删除失败"+StringUtils.join(stageSummaryIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyFeeStageSummaryVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeStageSummaryEntity entity = new BbpmPenaltyFeeStageSummaryEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getStageSummaryId())) {
                throw new McpException("违约金分阶段汇总表更新失败传入分阶段汇总ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("违约金分阶段汇总表更新失败");
                throw new McpException("违约金分阶段汇总表更新失败"+entity.getStageSummaryId());
            } else {
                if(!baseService.saveOperationHisById(entity.getStageSummaryId(),2)) {
                    log.error("违约金分阶段汇总表更新后保存历史失败:" + entity.getStageSummaryId());
                    throw new McpException("违约金分阶段汇总表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金分阶段汇总表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyFeeStageSummaryVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeStageSummaryEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeStageSummaryVo item:voList){
                BbpmPenaltyFeeStageSummaryEntity entity = new BbpmPenaltyFeeStageSummaryEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getStageSummaryId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("违约金分阶段汇总表批量更新失败 存在分阶段汇总ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("违约金分阶段汇总表批量更新失败");
                throw new McpException("违约金分阶段汇总表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getStageSummaryId())).map(BbpmPenaltyFeeStageSummaryEntity::getStageSummaryId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("违约金分阶段汇总表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金分阶段汇总表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyFeeStageSummaryVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeStageSummaryEntity entity = new BbpmPenaltyFeeStageSummaryEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("违约金分阶段汇总表保存失败");
                throw new McpException("违约金分阶段汇总表保存失败"+entity.getStageSummaryId());
            } else {
                if(!baseService.saveOperationHisById(entity.getStageSummaryId(),4)) {
                    log.error("违约金分阶段汇总表保存后保存历史失败:" + entity.getStageSummaryId());
                    throw new McpException("违约金分阶段汇总表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金分阶段汇总表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金分阶段汇总表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyFeeStageSummaryVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeStageSummaryEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeStageSummaryVo item:voList){
                BbpmPenaltyFeeStageSummaryEntity entity = new BbpmPenaltyFeeStageSummaryEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("违约金分阶段汇总表批量保存失败");
                throw new McpException("违约金分阶段汇总表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getStageSummaryId())).map(BbpmPenaltyFeeStageSummaryEntity::getStageSummaryId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("违约金分阶段汇总表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金分阶段汇总表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param stageSummaryId 需要查询的分阶段汇总ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyFeeStageSummaryVo selectByIdRecord(String stageSummaryId) {
        BbpmPenaltyFeeStageSummaryVo vo = new BbpmPenaltyFeeStageSummaryVo();

        if(!StringUtils.isEmpty(stageSummaryId)) {
            BbpmPenaltyFeeStageSummaryEntity entity = baseService.selectById(stageSummaryId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyFeeStageSummaryPageResultVo>> selectByPageRecord(BbpmPenaltyFeeStageSummaryPageVo vo) {
        List<BbpmPenaltyFeeStageSummaryPageResultVo> result = baseMapper.selectByPageCustom(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmPenaltyFeeStageSummaryVo> selectListByCondition(BbpmPenaltyFeeStageSummaryVo vo) {
        return baseMapper.selectListByCondition(vo);
    }
}
