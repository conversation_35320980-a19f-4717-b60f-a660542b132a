package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 合同主表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-20
 * @change 2023-05-20 by sx for init
 */
@ApiModel(value="BbctContractManagementPageResultVo对象", description="合同主表")
@Data
public class BbctContractManagementPageResultVo extends McpBasePageVo implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空",groups = {UpdateValidatorGroup.class})
    private String contractId;

    /**
     * 合同分类编号
     */
    @ApiModelProperty(value = "合同分类编号")
    private String contractTypeCode;

    @ApiModelProperty(value = "合同分类名称")
    private String contractTypeName;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号，在散租和趸租中是合同编号，在管理协议中是协议编号")
    private String contractNo;

    /**
     * 所属趸租合同编号
     */
    @ApiModelProperty(value = "所属趸租合同编号，在管理协议中是合同编号")
    private String parentContractCode;

    @ApiModelProperty(value = "续租来源合同编号")
    private String reletSourceContractCode;

    /**
     * 合同状态（1已生效、2未生效、3已结束、4终止）
     */
    @ApiModelProperty(value = "合同状态（1已生效、2未生效、3已结束、4终止、5已逾期、6作废、7冻结）")
    @McpDictPoint(dictCode = "CONTRACT_STATE", overTransCopyTo = "contractStatusName")
    private String contractStatus;

    /**
     * 合同状态名称（1已生效、2未生效、3已结束、4终止）
     */
    @ApiModelProperty(value = "合同状态名称（已生效、未生效、已结束、终止、已逾期、作废、冻结）")
    private String contractStatusName;

    @ApiModelProperty(value = "虚拟合同来源合同号")
    private String virtualSourceCode;

    @ApiModelProperty(value = "是否虚拟合同(0.否 1.是)")
    private String virtualContract;

    /**
     * 合同模板ID
     */
    @ApiModelProperty(value = "合同模板ID")
    private String contractTemplateId;

    /**
     * 合同审核人ID
     */
    @ApiModelProperty(value = "合同审核人ID")
    private String examineUserId;

    /**
     * 合同审核人姓名
     */
    @ApiModelProperty(value = "合同审核人姓名")
    private String examineUserName;

    /**
     * pdf文件id
     */
    @ApiModelProperty(value = "pdf文件id")
    private String pdfFileId;

    /**
     * 审核后PDF文件ID
     */
    @ApiModelProperty(value = "审核后PDF文件ID")
    private String newpdfFileId;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signTime;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 甲方ID
     */
    @ApiModelProperty(value = "甲方ID")
    private String firstId;

    /**
     * 甲方名称
     */
    @ApiModelProperty(value = "甲方名称")
    private String firstName;

    /**
     * 甲方信用代码
     */
    @ApiModelProperty(value = "甲方信用代码")
    private String firstSocialCode;

    /**
     * 合同总份数
     */
    @ApiModelProperty(value = "合同总份数")
    private String totalContractNum;

    /**
     * 签约房源套数
     */
    @ApiModelProperty(value = "签约房源套数")
    private Integer totalSets;

    @ApiModelProperty(value = "租赁家具家电房屋数量")
    private Integer furnitureSets;

    /**
     * 总租赁面积
     */
    @ApiModelProperty(value = "总租赁面积")
    private String totalArea;

    @ApiModelProperty("面积类型(1.建筑面积 2.套内建筑面积)")
    private String areaType;

    /**
     * 单缴费周期租金
     */
    @ApiModelProperty(value = "单缴费周期租金")
    private Double leaseTermRent;

    @ApiModelProperty(value = "押金")
    private String deposit;

    /**
     * 押金标准code(1.1个月 2.2个月 3.3个月)
     */
    @ApiModelProperty(value = "押金标准code(1.1个月 2.2个月 3.3个月)")
    private String cashPledgeCode;

    /**
     * 押金标准名称
     */
    @ApiModelProperty(value = "押金标准名称")
    private String cashPledgeName;

    /**
     * 缴费周期code(01.月付 02.季度付 03.半年付 04.年付)
     */
    @ApiModelProperty(value = "缴费周期code(01.月付 02.季度付 03.半年付 04.年付)")
    private String paymentCycleCode;

    /**
     * 缴费周期名称
     */
    @ApiModelProperty(value = "缴费周期名称")
    private String paymentCycleName;

    /**
     * 缴费货币
     */
    @ApiModelProperty(value = "缴费货币")
    private String paymentCurrency;

    /**
     * 总缴费租金
     */
    @ApiModelProperty(value = "总缴费租金")
    private Double paymentRent;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private String payMode;

    @ApiModelProperty(value = "缴费类型(1.比例 2.金额)")
    private String payType;

    /**
     * 交付日期
     */
    @ApiModelProperty(value = "交付日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliveryTime;

    /**
     * 业务分类编号1
     */
    @ApiModelProperty(value = "业务分类编号1")
    private String businessTypeCode1;

    /**
     * 业务分类名称1
     */
    @ApiModelProperty(value = "业务分类名称1(房屋实际用途)")
    private String businessTypeName1;

    /**
     * 业务分类编号2
     */
    @ApiModelProperty(value = "业务分类编号2(房屋：01散租，02趸租，03管理协议；04,06家具家电个人，05家具家电企业)")
    private String businessTypeCode2;

    /**
     * 业务分类名称2
     */
    @ApiModelProperty(value = "业务分类名称2")
    private String businessTypeName2;

    /**
     * 业务分类编号3
     */
    @ApiModelProperty(value = "业务分类编号3")
    private String businessTypeCode3;

    /**
     * 业务分类名称3
     */
    @ApiModelProperty(value = "业务分类名称3")
    private String businessTypeName3;

    /**
     * 业务唯一标识
     */
    @ApiModelProperty(value = "业务唯一标识")
    private String businessId;

    /**
     * 合同终止时间
     */
    @ApiModelProperty(value = "合同终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date terminationDate;


    @ApiModelProperty(value = "是否退租（0否，1是）")
    private String rentTerminate;

    /**
     * 变更次数
     */
    @ApiModelProperty(value = "变更次数")
    private Integer changeNum;

    /**
     * 最后变更日期
     */
    @ApiModelProperty(value = "最后变更日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date changeLastDate;

    /**
     * 变更状态
     */
    @ApiModelProperty(value = "变更状态")
    @McpDictPoint(dictCode = "CHANGE_STATUS", overTransCopyTo = "changeCheckStatusName")
    private String changeCheckStatus;

    @ApiModelProperty(value = "变更审核状态名称")
    private String changeCheckStatusName;


    @ApiModelProperty("乙方缴费比例")
    private String secondPayPercent;

    @ApiModelProperty("丙方缴费比例")
    private String thirdPayPercent;

    @ApiModelProperty("乙方缴费金额")
    private String secondPayMoney;

    @ApiModelProperty("丙方缴费金额")
    private String thirdPayMoney;

    @ApiModelProperty("是否报盘(0.否 1.是)")
    private String offer;

    @ApiModelProperty("是否报送热力公司(0.否 1.是)")
    private String submitHeatingCompany;

    @ApiModelProperty("合同通过日期（签约日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date passTime;

    @ApiModelProperty("租金标准code(01.公租租金标准 02.人才租金标准 03.市场租金标准)")
    private String rentStandardCode;

    @ApiModelProperty("基础租金计算方式code")
    private String baseRentCalculate;

    @ApiModelProperty("基础租金计算方式名称")
    private String baseRentCalculateName;

    /**
     * 是否欠缴(是、否)
     */
    @ApiModelProperty(value = "是否欠缴(是、否)")
    private String isOverdue;

    @ApiModelProperty("合同后付款(0.否 1.是)")
    private String afterContractPay;

    @ApiModelProperty("甲方账户id")
    private String firstAccountId;

    @ApiModelProperty("甲方账户名称")
    private String firstAccountName;

    @ApiModelProperty("甲方开户行")
    private String firstBankNameCode;

    @ApiModelProperty("甲方开户行名称")
    private String firstBankName;

    @ApiModelProperty("五期合同状态参考字段")
    private String fiveStatus;

    @ApiModelProperty("五期合同（01正常、02候审期、03过渡期、04占用期、05欠费）")
    @McpDictPoint(dictCode = "PAHSE_FIVE",overTransCopyTo = "pahseFiveName")
    private String pahseFive;

    @ApiModelProperty("五期合同值")
    private String pahseFiveName;

    @ApiModelProperty("五期合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pahseFiveStartTime;

    @ApiModelProperty("五期合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pahseFiveEndTime;

    @ApiModelProperty("签约结果id")
    private String signId;

    @ApiModelProperty(value = "签约房间类型")
    private String roomType;

    @ApiModelProperty("签约房间类型名称")
    private String roomTypeName;

    @ApiModelProperty("合同信息扩展")
    private String contractExtend;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String ext4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String ext5;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "租期")
    private String leaseTerm;

    //===================产品信息====================
    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityBuildingName;

    @ApiModelProperty(value = "项目编码")
    private String projectNo;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 房源地址（产品名称）
     */
    @ApiModelProperty(value = "房源地址（产品名称）")
    private String productName;

    /**
     * 小区所在区
     */
    @ApiModelProperty(value = "小区所在区")
    private String communityRegion;

    /**
     * 小区地址
     */
    @ApiModelProperty(value = "小区地址")
    private String communityAddress;

    @ApiModelProperty(value = "产品租金标准（元/m²/月）")
    private String rentStandardNo;

    @ApiModelProperty(value = "产品租金标准（元/m²/月）名称")
    private String rentStandardName;

    @ApiModelProperty(value = "产品租金标准金额")
    private Double rent;

    @ApiModelProperty(value = "产品租金单位名称（元/㎡/月、元/月")
    private String rentUnit;

    //===================人信息======================
    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;

    /**
     * 散户或者企业名称
     */
    @ApiModelProperty(value = "散户或者企业名称（承租人/单位名称）")
    private String customerName;

    /**
     * 趸租单位名称（仅导出时使用）
     */
    @ApiModelProperty(value = "趸租单位名称（仅导出时使用）")
    private String companyName;

    /**
     * 租户电话
     */
    @ApiModelProperty(value = "租户电话（承租人电话）")
    private String customerTel;


    @ApiModelProperty(value = "用户性别编码")
    @McpDictPoint(dictCode = "SEX",overTransCopyTo = "customerGenderName")
    private String customerGender;

    @ApiModelProperty(value = "用户性别名称")
    private String customerGenderName;

    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE",overTransCopyTo = "customerIdTypeName")
    private String customerIdType;

    @ApiModelProperty(value = "证件类型名称")
    private String customerIdTypeName;

    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 原住址
     */
    @ApiModelProperty(value = "原住址")
    private String customerHouseAddress;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
    private String customerWorkTel;

    /**
     * 公租房备案编号
     */
    @ApiModelProperty(value = "公租房备案编号")
    private String customerPublicRecordNo;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    private String bankName;

    /**
     * 银行卡号
     */
    @ApiModelProperty("银行卡号")
    private String bankCard;

    /**
     * 开户行手机号
     */
    @ApiModelProperty("开户行手机号")
    private String bankPhone;


    /**
     * 开户行名称
     */
    @ApiModelProperty(value = "开户名称")
    private String bankUserName;


    /**
     * 开户行手机号
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 通讯地址
     */
    @ApiModelProperty("通讯地址")
    private String mailAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty("邮政编码")
    private String postalCode;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人")
    private String legalName;

    /**
     * 法定代表人联系电话
     */
    @ApiModelProperty("法定代表人联系电话")
    private String legalMobile;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
    private String customerCreditCode;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
    private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
    private String consignorMobile;



    @ApiModelProperty(value = "承租人所在区县")
    private String communityRegionPerson;

    @ApiModelProperty(value = "承租人所在街道")
    private String communityAddressPerson;


    /**
     * 付款周期
     */
    @ApiModelProperty(value = "付款周期")
    private String paymentPeriod;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "app-租金标准")
    private String rentStandard;

    /**
     * 图片ID
     */
    @ApiModelProperty(value = "app-图片ID")
    private String imgId;

    /**
     * 退租阶段状态
     */
    @ApiModelProperty(value = "app-退租阶段状态")
    private String state;

    /**
     * 是否完善退款路径 0未完善 1已经完善
     */
    @ApiModelProperty(value = "app-是否完善退款路径")
    private Integer isSupath;

    @ApiModelProperty(value = "退租信息维护状态")
    private String maintenanceState;

    @ApiModelProperty(value = "是否办理入住(1=是,0=否)")
    private String isCheckedIn;

    /**
     * app-项目编号（用于获取银行信息）
     */
    @ApiModelProperty(value = "app-项目编号（用于获取银行信息）")
    private String projectCode;

    @ApiModelProperty(value = "账单数量")
    private String billNum;

    @ApiModelProperty(value = "押金条文件地址")
    private String depositFile;

    @ApiModelProperty(value = "押金条签字状态编码，01未签字,02已签字,03不用签字")
    private String depositSignStatus;

    @ApiModelProperty(value = "押金条签字状态名称，未签字,已签字,不用签字")
    private String depositSignStatusName;

    @ApiModelProperty(value = "查验单是否需要重新查验")
    private String isRecheck;

    @ApiModelProperty(value = "退租办理状态")
    private String rentingProcessingState;

    @ApiModelProperty(value = "房源编号NCC编码")
    private String houseNoNcc;

    @ApiModelProperty(value = "承租人信息")
    private List<BbctContractSignerVo> signerVos;

    @ApiModelProperty(value = "房产信息")
    private List<BbctContractSubjectMatterVo> subjectMatterVos;

    @ApiModelProperty(value = "调换房实体")
    private BbheApplyFormResultVo bbheApplyFormResultVo;

    @ApiModelProperty(value = "是否申请续租 0否 1是")
    private String isApplyRenewal;

    @ApiModelProperty(value = "退租唯一标识")
    private String surrenderId; //2023年10月7日增加退租唯一标识

    @ApiModelProperty(value = "补充协议数量")
    private int addAgreementNum;

    @ApiModelProperty(value = "合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.主承租人合同变更合同 6.购房补充协议 7.趸租补充协议)")
    private String contractType;

    @ApiModelProperty(value = "来源合同编号")
    private String sourceContractCode;

    @ApiModelProperty(value = "押金标准金额")
    private String cashPledgeMoney;

    @ApiModelProperty(value = "押金支付方式(1.企业付 2.个人付 3.比例支付)")
    private String cashPledgePayMode;

    @ApiModelProperty(value = "押金缴费类型(1.比例 2.金额)")
    private String cashPledgePayType;

    @ApiModelProperty(value = "押金缴费比例")
    private String cashPledgePayPercent;

    @ApiModelProperty(value = "押金缴费金额")
    private String cashPledgePayMoney;

    @ApiModelProperty(value = "是否有增值服务费(0.否 1.是)")
    private String serviceCharge;

    @ApiModelProperty(value = "增值服务费租金标准类型(1.比例 2.金额)")
    private String serviceChargeType;

    @ApiModelProperty(value = "增值服务费标准(月租金百分比)")
    private String serviceChargePercent;

    @ApiModelProperty(value = "增值服务费标准(金额)")
    private String serviceChargeMoney;

    @ApiModelProperty(value = "是否有递增(0.否 1.是)")
    private String increase;

    @ApiModelProperty(value = "递增类型(1.租金 2.租金+增值服务费 3.增值服务费")
    private String increaseType;

    @ApiModelProperty(value = "递增类型名称")
    private String increaseTypeName;

    @ApiModelProperty(value = "递增json")
    private String increaseJson;

    @ApiModelProperty(value = "支付标准(1.统一标准 2.非同一标准)")
    private String payStandard;

    @ApiModelProperty(value = "增值服务费缴费类型(1.比例 2.金额)")
    private String serviceChargePayType;

    @ApiModelProperty(value = "增值服务费缴费比例")
    private String serviceChargePayPercent;

    @ApiModelProperty(value = "增值服务费缴费金额")
    private String serviceChargePayMoney;

    @ApiModelProperty(value = "租赁依据")
    private String according;


    @ApiModelProperty(value = "付款银行账户id")
    private String payingBankAccountId;

    @ApiModelProperty(value = "付款银行账户名称")
    private String payingBankAccountName;

    @ApiModelProperty(value = "付款银行开户行")
    private String payingBankNameCode;

    @ApiModelProperty(value = "付款银行开户行名称")
    private String payingBankName;

    @ApiModelProperty(value = "付款银行开户行支行")
    private String payingSubBankNameCode;

    @ApiModelProperty(value = "付款银行开户行支行名称")
    private String payingSubBankName;

    @ApiModelProperty(value = "是否合并退租【0=否,1=是】")
    private String isAssociation;

    @ApiModelProperty(value = "合并退租时是否是主合同【0=否,1=是】")
    private String isMainAssociation;

    @ApiModelProperty(value = "导出实际居主人姓名")
    private String personName;

    @ApiModelProperty(value = "导出实际居主人证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE",overTransCopyTo = "personCertificateTypeName")
    private String personCertificateTypeCode;

    @ApiModelProperty(value = "导出实际居主人证件类型名称")
    private String personCertificateTypeName;

    @ApiModelProperty(value = "导出实际居主人证件号")
    private String personCertificateNum;

    @ApiModelProperty(value = "导出实际居主人电话")
    private String personPhone;

    @ApiModelProperty("附件名前缀")
    private String attachmentPrefix;

    @ApiModelProperty(value = "管理协议号")
    private String xyContractNo;

    @ApiModelProperty(value = "使用协议号")
    private String syContractNo;

    @ApiModelProperty("公积金状态(0.停用 1.启用)")
    @McpDictPoint(dictCode = "PROVIDENT_FUND_STATE", overTransCopyTo = "providentFundStateName")
    private String providentFundState;

    @ApiModelProperty("公积金状态名称")
    private String providentFundStateName;

    @ApiModelProperty("公积金停用类型(0.中止提取 1.终止事项)")
    @McpDictPoint(dictCode = "PROVIDENT_FUND_TYPE", overTransCopyTo = "providentFundTypeName")
    private String providentFundType;

    @ApiModelProperty("公积金停用类型名称")
    private String providentFundTypeName;

    @ApiModelProperty("公积金授权状态名称")
    private String providentFundAuthStatusName;

    @ApiModelProperty("是否显示条换房按钮 1：显示 0：不显示")
    private String isShowChangeHouseButton;

    /**
     * 房源编号（产品编号）
     */
    @ApiModelProperty(value = "房源编号（产品编号）")
    private String productNo;


    @ApiModelProperty(value = "导出实际居主人姓名")
    private String xyPersonName;

    @ApiModelProperty(value = "导出实际居主人证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE",overTransCopyTo = "xyPersonCertificateTypeName")
    private String xyPersonCertificateTypeCode;
    @ApiModelProperty(value = "导出实际居主人证件类型名称")
    private String xyPersonCertificateTypeName;

    @ApiModelProperty(value = "导出实际居主人证件号")
    private String xyPersonCertificateNum;

    @ApiModelProperty(value = "导出实际居主人电话")
    private String xyPersonPhone;

    @ApiModelProperty(value = "入住协议文件ID")
    private String cisFileId;

    @ApiModelProperty(value = "管理协议合同Id")
    private String xyFileId;


    @ApiModelProperty(value = "入住时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkInTime;
}
