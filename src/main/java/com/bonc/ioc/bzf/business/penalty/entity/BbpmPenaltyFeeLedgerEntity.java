package com.bonc.ioc.bzf.business.penalty.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 违约金台账 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@TableName("bbpm_penalty_fee_ledger")
@ApiModel(value="BbpmPenaltyFeeLedgerEntity对象", description="违约金台账")
public class BbpmPenaltyFeeLedgerEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_LEDGER_ID = "ledger_id";
    public static final String FIELD_PENALTY_DISPOSAL_NO = "penalty_disposal_no";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_PROJECT_ID = "project_id";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_TENANT_CODE = "tenant_code";
    public static final String FIELD_TENANT_NAME = "tenant_name";
    public static final String FIELD_HOUSE_NAME = "house_name";
    public static final String FIELD_CONTRACT_CODE = "contract_code";
    public static final String FIELD_DISPOSAL_TYPE = "disposal_type";
    public static final String FIELD_REDUCTION_AMOUNT = "reduction_amount";
    public static final String FIELD_BILL_GENERATION_AMOUNT = "bill_generation_amount";
    public static final String FIELD_DISPOSAL_BASIS_FILE_ID = "disposal_basis_file_id";
    public static final String FIELD_DISPOSAL_BASIS_DESCRIPTION = "disposal_basis_description";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 台账ID
     */
    @ApiModelProperty(value = "台账ID")
                                @TableId(value = "ledger_id", type = IdType.ASSIGN_UUID)
                                  private String ledgerId;

    /**
     * 违约金处置编号
     */
    @ApiModelProperty(value = "违约金处置编号")
                            private String penaltyDisposalNo;

    /**
     * 状态：0-暂存，1-审批中，2-已通过，3-未通过
     */
    @ApiModelProperty(value = "状态：0-暂存，1-审批中，2-已通过，3-未通过")
                            private Integer status;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
                            private String tenantCode;

    /**
     * 租户名称（商户名称）
     */
    @ApiModelProperty(value = "租户名称（商户名称）")
                            private String tenantName;

    /**
     * 房源名称（房源地址）
     */
    @ApiModelProperty(value = "房源名称（房源地址）")
                            private String houseName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 处置类型：1-减免违约金，2-不减免违约金
     */
    @ApiModelProperty(value = "处置类型：1-减免违约金，2-不减免违约金")
                            private String disposalType;

    /**
     * 减免金额
     */
    @ApiModelProperty(value = "减免金额")
                            private BigDecimal reductionAmount;

    /**
     * 生成违约金账单金额
     */
    @ApiModelProperty(value = "生成违约金账单金额")
                            private BigDecimal billGenerationAmount;

    /**
     * 违约金处置依据文件ID
     */
    @ApiModelProperty(value = "违约金处置依据文件ID")
                            private String disposalBasisFileId;

    /**
     * 违约金处置依据说明
     */
    @ApiModelProperty(value = "违约金处置依据说明")
                            private String disposalBasisDescription;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private String delFlag;

    /**
     * @return 台账ID
     */
    public String getLedgerId() {
        return ledgerId;
    }

    public void setLedgerId(String ledgerId) {
        this.ledgerId = ledgerId;
    }

    /**
     * @return 违约金处置编号
     */
    public String getPenaltyDisposalNo() {
        return penaltyDisposalNo;
    }

    public void setPenaltyDisposalNo(String penaltyDisposalNo) {
        this.penaltyDisposalNo = penaltyDisposalNo;
    }

    /**
     * @return 状态：0-暂存，1-审批中，2-已通过，3-未通过
     */
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 租户ID
     */
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * @return 租户名称（商户名称）
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 房源名称（房源地址）
     */
    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 处置类型：1-减免违约金，2-不减免违约金
     */
    public String getDisposalType() {
        return disposalType;
    }

    public void setDisposalType(String disposalType) {
        this.disposalType = disposalType;
    }

    /**
     * @return 减免金额
     */
    public BigDecimal getReductionAmount() {
        return reductionAmount;
    }

    public void setReductionAmount(BigDecimal reductionAmount) {
        this.reductionAmount = reductionAmount;
    }

    /**
     * @return 生成违约金账单金额
     */
    public BigDecimal getBillGenerationAmount() {
        return billGenerationAmount;
    }

    public void setBillGenerationAmount(BigDecimal billGenerationAmount) {
        this.billGenerationAmount = billGenerationAmount;
    }

    /**
     * @return 违约金处置依据文件ID
     */
    public String getDisposalBasisFileId() {
        return disposalBasisFileId;
    }

    public void setDisposalBasisFileId(String disposalBasisFileId) {
        this.disposalBasisFileId = disposalBasisFileId;
    }

    /**
     * @return 违约金处置依据说明
     */
    public String getDisposalBasisDescription() {
        return disposalBasisDescription;
    }

    public void setDisposalBasisDescription(String disposalBasisDescription) {
        this.disposalBasisDescription = disposalBasisDescription;
    }

    /**
     * @return 有效标识
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyFeeLedgerEntity{" +
            "ledgerId=" + ledgerId +
            ", penaltyDisposalNo=" + penaltyDisposalNo +
            ", status=" + status +
            ", projectId=" + projectId +
            ", projectName=" + projectName +
            ", tenantCode=" + tenantCode +
            ", tenantName=" + tenantName +
            ", houseName=" + houseName +
            ", contractCode=" + contractCode +
            ", disposalType=" + disposalType +
            ", reductionAmount=" + reductionAmount +
            ", billGenerationAmount=" + billGenerationAmount +
            ", disposalBasisFileId=" + disposalBasisFileId +
            ", disposalBasisDescription=" + disposalBasisDescription +
            ", delFlag=" + delFlag +
        "}";
    }
}