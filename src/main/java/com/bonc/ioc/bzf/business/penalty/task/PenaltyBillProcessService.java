package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo;
import com.bonc.ioc.bzf.business.penalty.vo.ContractInfoVo;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金账单处理服务类
 * 负责合同过滤、账单处理等业务逻辑
 */
@Slf4j
@Service
public class PenaltyBillProcessService {

    @Resource
    private PenaltyDataService penaltyDataService;
    
    @Resource
    private PenaltyCalculationService penaltyCalculationService;
    
    @Resource
    private PenaltyValidationService penaltyValidationService;
    
    @Resource
    private PenaltySummaryService penaltySummaryService;
    
    @Resource
    private PenaltyTaskStatusService penaltyTaskStatusService;

    /**
     * 断点续传过滤方法 - 过滤出未处理的合同
     */
    public List<ContractInfoVo> filterUnprocessedContracts(List<ContractInfoVo> allContracts, LocalDate taskDate) {
        try {
            List<ContractInfoVo> unprocessedContracts = new ArrayList<>();
            
            // 查询未处理的合同（状态为0-待处理或1-处理中）
            for (ContractInfoVo contractInfo : allContracts) {
                String contractNo = contractInfo.getContractNo();
                String processStatus = penaltyTaskStatusService.getContractProcessStatus(contractNo, taskDate);
                
                // 如果是待处理(0)或处理中(1)，则需要处理
                if ("0".equals(processStatus) || "1".equals(processStatus)) {
                    unprocessedContracts.add(contractInfo);
                } else if ("2".equals(processStatus)) {
                    log.debug("合同 {} 今天已完成处理，跳过", contractNo);
                } else if ("3".equals(processStatus)) {
                    log.debug("合同 {} 今天处理失败，重新加入处理队列", contractNo);
                    unprocessedContracts.add(contractInfo);
                } else {
                    // 没有记录的合同也需要处理
                    unprocessedContracts.add(contractInfo);
                }
            }
            
            log.info("筛选出需要处理的合同数量：{}/{}", unprocessedContracts.size(), allContracts.size());
            return unprocessedContracts;
                
        } catch (Exception e) {
            log.error("查询未处理合同列表失败：{}", e.getMessage(), e);
            // 出错时返回所有合同，确保任务能够继续
            return allContracts;
        }
    }

    /**
     * 获取合同已有的试算账单记录映射
     */
    public Map<String, BbpmPenaltyFeeTrialBillVo> getExistingTrialBillMap(String contractNo) {
        Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap = new java.util.HashMap<>();
        try {
            List<BbpmPenaltyFeeTrialBillVo> trialBills = penaltyDataService.queryTrialBillList(contractNo, null);
            for (BbpmPenaltyFeeTrialBillVo trialBill : trialBills) {
                if (trialBill != null) {
                    existingTrialBillMap.put(trialBill.getBillId(), trialBill);
                }
            }
            
            log.info("合同 {} 库中已有 {} 个试算账单记录（包含所有状态）", contractNo, existingTrialBillMap.size());
            
        } catch (Exception e) {
            log.error("查询合同 {} 已有试算账单失败：{}", contractNo, e.getMessage(), e);
        }
        return existingTrialBillMap;
    }

    /**
     * 处理新增账单（优化版：使用预先查询的收款单信息）
     */
    public void processNewBill(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments, 
                               LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 1. 使用预先查询的收款单信息（优化：避免重复查询）
            
            // 2. 分析状态
            String reconciliationStatus = penaltyDataService.analyzeReconciliationStatus(payments);
            LocalDate earliestReceiptDate = penaltyCalculationService.getEarliestBankReceiptDate(payments, calculationDate);
            
            // 3. 创建试算账单记录（新账单一定需要创建）
            penaltyValidationService.ensureTrialBillExists(bill, payments, reconciliationStatus, earliestReceiptDate, dailyOverdueRate);
            
            // 4. 计算违约金（从账单逾期开始计算到今天）
            penaltyCalculationService.calculateHistoricalPenalties(bill, payments, calculationDate, taskStatusId, dailyOverdueRate, projectId);
            
            log.info("新增账单 {} 处理完成", bill.getBillId());
            
        } catch (Exception e) {
            log.error("处理新增账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理状态变化的账单
     */
    public void processStatusChangedBill(BillStatusComparisonVo statusComparison, LocalDate calculationDate, 
                                         String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        BbpmBillManagementPageResultVo bill = statusComparison.getBill();
        String oldStatus = statusComparison.getOldStatus();
        String newStatus = statusComparison.getNewStatus();
        
        try {
            // 1. 记录状态变化到API日志
            penaltyDataService.recordReconciliationStatusChange(bill.getBillId(), oldStatus, newStatus);
            
            // 2. 获取银行回单日期，用于确定调整起始日期
            LocalDate earliestReceiptDate = penaltyCalculationService.getEarliestBankReceiptDate(statusComparison.getCurrentPayments(), calculationDate);
            
            // 3. 触发违约金调整（红冲之前的记录）
            penaltyValidationService.adjustPenalty(bill, earliestReceiptDate != null ? earliestReceiptDate : calculationDate, projectId);
            
            // 4. 更新试算账单的对账状态
            penaltyValidationService.ensureTrialBillExists(bill, statusComparison.getCurrentPayments(), newStatus, earliestReceiptDate, dailyOverdueRate);
            
            // 5. 今日违约金计算（正常流程）
            if (!penaltyCalculationService.isAlreadyProcessedToday(bill.getBillId(), calculationDate)) {
                penaltyCalculationService.calculateDailyPenalty(bill, statusComparison.getCurrentPayments(), calculationDate, taskStatusId, dailyOverdueRate, projectId);
            }
            
            // 6. 更新试算账单汇总信息
            penaltySummaryService.updateTrialBillSummary(bill.getBillId(), calculationDate);
            
            log.info("状态变化账单 {} 处理完成", bill.getBillId());
            
        } catch (Exception e) {
            log.error("处理状态变化账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理无变化账单（正常的每日计算）
     */
    public void processUnchangedBill(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments,
                                    LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 只进行正常的今日违约金计算（如果今天还没算过）
            if (!penaltyCalculationService.isAlreadyProcessedToday(bill.getBillId(), calculationDate)) {
                penaltyCalculationService.calculateDailyPenalty(bill, payments, calculationDate, taskStatusId, dailyOverdueRate, projectId);
                log.debug("无变化账单 {} 今日违约金计算完成", bill.getBillId());
            } else {
                log.debug("无变化账单 {} 今日已计算过违约金，跳过", bill.getBillId());
            }
        } catch (Exception e) {
            log.error("处理无变化账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }
}
