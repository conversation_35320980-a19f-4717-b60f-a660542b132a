package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeDailyDetailService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeDailyDetailVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金计算逻辑服务类
 * 负责违约金的计算逻辑，包括当日违约金计算、历史违约金计算等
 */
@Slf4j
@Service
public class PenaltyCalculationService {

    @Resource
    private IBbpmPenaltyFeeDailyDetailService penaltyFeeDailyDetailService;
    
    @Resource
    private PenaltyDataService penaltyDataService;

    @Resource
    @Lazy
    private PenaltySummaryService penaltySummaryService;

    @Resource
    @Lazy
    private PenaltyValidationService penaltyValidationService;

    /**
     * 计算当日违约金（优化版：使用预先查询的收款单信息）
     */
    public void calculateDailyPenalty(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments, 
                                     LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 1. 使用预先查询的收款单信息（优化：避免重复查询）
            
            // 2. 分析收款状态和计算违约金基数
            BigDecimal shouldPayAmount = parseBigDecimal(bill.getShouldPayAmount());
            BigDecimal totalPaidAmount = calculateTotalPaidAmount(payments);
            BigDecimal penaltyBaseAmount = shouldPayAmount.subtract(totalPaidAmount).max(BigDecimal.ZERO);
            String reconciliationStatus = penaltyDataService.analyzeReconciliationStatus(payments);
            
            // 3. 判断是否应该计算违约金
            if (!shouldCalculatePenalty(bill, calculationDate, reconciliationStatus, penaltyBaseAmount)) {
                log.debug("账单 {} 在日期 {} 无需计算违约金，对账状态：{}，未缴金额：{}", 
                        bill.getBillId(), calculationDate, reconciliationStatus, penaltyBaseAmount);
                return;
            }
            
            // 4. 确保试算账单记录存在（首次计算时创建）
            LocalDate earliestReceiptDate = getEarliestBankReceiptDate(payments, calculationDate);
            penaltyValidationService.ensureTrialBillExists(bill, payments, reconciliationStatus, earliestReceiptDate, dailyOverdueRate);

            // 5. 计算当日违约金（使用传入的每日逾期率，需要除以100转换为百分比）
            BigDecimal actualRate = dailyOverdueRate.divide(new BigDecimal("100"), 6, RoundingMode.HALF_UP);
            BigDecimal dailyPenaltyAmount = penaltyBaseAmount.multiply(actualRate).setScale(2, RoundingMode.HALF_UP);

            // 6. 构建每日明细记录（注意：这里存储的是原始逾期率，计算时已转换）
            BbpmPenaltyFeeDailyDetailVo dailyRecord = buildDailyRecord(bill, calculationDate,
                                                                      penaltyBaseAmount, dailyOverdueRate,
                                                                      dailyPenaltyAmount, reconciliationStatus, payments);

            // 7. 保存到数据库
            penaltyFeeDailyDetailService.insertRecord(dailyRecord);

            // 8. 更新试算账单汇总信息
            penaltySummaryService.updateTrialBillSummary(bill.getBillId(), calculationDate);

            // 9. 生成分阶段汇总数据（包含验证）
            // 需要先获取trialBillId
            BbpmPenaltyFeeTrialBillVo trialBill = penaltyDataService.selectTrialBillByBillId(bill.getBillId());
            if (trialBill != null) {
                penaltySummaryService.generateStageSummaryWithValidation(trialBill.getTrialBillId());
            }

            // 10. 检查是否需要自动完结账单
            penaltyValidationService.checkAndAutoFinalize(bill.getBillId());
            
            log.info("账单 {} 当日违约金计算完成，违约金基数：{}，违约金：{}，对账状态：{}，已缴金额：{}",
                     bill.getBillId(), penaltyBaseAmount, dailyPenaltyAmount, reconciliationStatus, totalPaidAmount);
            
        } catch (Exception e) {
            log.error("计算账单 {} 违约金失败：{}", bill.getBillId(), e.getMessage(), e);
            throw new RuntimeException("违约金计算失败", e);
        }
    }

    /**
     * 计算历史违约金（用于新账单的历史补算）（优化版：使用预先查询的收款单信息）
     */
    public void calculateHistoricalPenalties(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments, 
                                           LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 计算从收费科目起始日期到今天的所有违约金
            LocalDate chargeSubjectBeginDate = parseDate(bill.getChargeSubjectBeginDate());
            LocalDate payableDate = parseDate(bill.getPayableDate());
            
            // 确定历史计算开始日期：优先使用收费科目起始日期（修复：违约金应从收费期间开始计算）
            LocalDate startDate = null;
            if (chargeSubjectBeginDate != null) {
                startDate = chargeSubjectBeginDate; // 从收费科目起始日期开始
            } else if (payableDate != null) {
                startDate = payableDate.plusDays(1); // 如果没有收费科目起始日期，从逾期日开始
            } else {
                log.warn("账单 {} 收费科目起始日期和缴费截止日期都为空，跳过历史违约金计算", bill.getBillId());
                return;
            }
            
            LocalDate endDate = calculationDate.minusDays(1); // 计算到昨天，今天的违约金通过当日计算处理
            
            if (startDate.isAfter(endDate)) {
                log.debug("账单 {} 没有历史欠费期间，无需计算历史违约金", bill.getBillId());
                return;
            }
            
            log.info("开始计算账单 {} 的历史违约金：{} 到 {}，逾期率：{}（将除以100转换为实际比率）", 
                    bill.getBillId(), startDate, endDate, dailyOverdueRate);
            
            // 逐日计算历史违约金
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                try {
                    // 检查当日是否已有计算记录
                    if (!isAlreadyProcessedToday(bill.getBillId(), currentDate)) {
                        calculateDailyPenalty(bill, payments, currentDate, taskStatusId, dailyOverdueRate, projectId);
                        log.debug("计算账单 {} 日期 {} 的历史违约金完成", bill.getBillId(), currentDate);
                    } else {
                        log.debug("账单 {} 日期 {} 已有计算记录，跳过", bill.getBillId(), currentDate);
                    }
                } catch (Exception e) {
                    log.error("计算账单 {} 日期 {} 的历史违约金失败：{}", bill.getBillId(), currentDate, e.getMessage(), e);
                    // 单日失败不影响其他日期的计算
                }
                
                currentDate = currentDate.plusDays(1);
            }
            
            log.info("账单 {} 历史违约金计算完成", bill.getBillId());
            
        } catch (Exception e) {
            log.error("计算账单 {} 历史违约金失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 判断是否应该计算违约金
     */
    public boolean shouldCalculatePenalty(BbpmBillManagementPageResultVo bill,
                                         LocalDate calculationDate,
                                         String reconciliationStatus,
                                         BigDecimal penaltyBaseAmount) {
        // 1. 检查计算日期是否在收费科目起始日期之后（修复：应该从收费科目起始日期开始计算违约金）
        LocalDate chargeSubjectBeginDate = parseDate(bill.getChargeSubjectBeginDate());
        if (chargeSubjectBeginDate != null && calculationDate.isBefore(chargeSubjectBeginDate)) {
            return false; // 计算日期早于收费科目起始日期，不计算违约金
        }
        
        // 2. 检查是否还有未对平金额
        if (penaltyBaseAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("账单 {} 在日期 {} 已全部对平，无需计算违约金", bill.getBillId(), calculationDate);
            return false; // 已全部对平，不计算违约金
        }
        
        // 3. 检查对账状态（关键逻辑）
        if ("01".equals(reconciliationStatus)) {
            // 已对平的情况，不计算违约金
            return false;
        }
        
        return true; // 有未对平金额且未完全对账，需要计算违约金
    }

    /**
     * 获取正确的计算日期（基于账单的逾期开始日期）
     */
    public LocalDate getCorrectCalculationDate(BbpmBillManagementPageResultVo bill, LocalDate taskExecutionDate) {
        // 1. 解析收费科目起始日期和应缴费日期
        LocalDate chargeSubjectBeginDate = parseDate(bill.getChargeSubjectBeginDate());
        LocalDate payableDate = parseDate(bill.getPayableDate());
        
        // 2. 选择较早的日期作为逾期计算起始日期
        LocalDate overdueStartDate = null;
        if (chargeSubjectBeginDate != null && payableDate != null) {
            // 两个日期都存在，选择较早的
            overdueStartDate = chargeSubjectBeginDate.isBefore(payableDate) ? chargeSubjectBeginDate : payableDate;
        } else if (chargeSubjectBeginDate != null) {
            // 只有收费科目起始日期
            overdueStartDate = chargeSubjectBeginDate;
        } else if (payableDate != null) {
            // 只有应缴费日期
            overdueStartDate = payableDate;
        } else {
            // 两个日期都为空，使用任务执行日期前一天
            overdueStartDate = taskExecutionDate.minusDays(1);
            log.warn("账单 {} 的收费科目起始日期和应缴费日期都为空，使用任务执行日期前一天：{}", 
                    bill.getBillId(), overdueStartDate);
        }
        
        log.debug("账单 {} 计算日期确定：收费起始={}, 应缴费={}, 选择={}", 
                bill.getBillId(), chargeSubjectBeginDate, payableDate, overdueStartDate);
        
        return overdueStartDate;
    }

    /**
     * 构建每日违约金明细记录
     */
    public BbpmPenaltyFeeDailyDetailVo buildDailyRecord(BbpmBillManagementPageResultVo bill, 
                                                        LocalDate actualCalculationDate,
                                                        BigDecimal penaltyBaseAmount, 
                                                        BigDecimal dailyRate,
                                                        BigDecimal dailyPenaltyAmount,
                                                        String reconciliationStatus,
                                                        List<BbpmCollectionPageResultVo> payments) {
        BbpmPenaltyFeeDailyDetailVo record = new BbpmPenaltyFeeDailyDetailVo();
        record.setBillId(bill.getBillId());
        
        // 直接使用传入的实际计算日期（修复：确保历史每日明细记录的日期正确）
        record.setCalculationDate(actualCalculationDate);
        
        record.setReplacePayAmount(penaltyBaseAmount);
        record.setDailyOverdueRate(dailyRate);
        record.setDailyPenaltyAmount(dailyPenaltyAmount);
        record.setEntryType("1"); // 1-正常计算
        record.setBillStatus(bill.getBillStatus());
        record.setAccountStatus(reconciliationStatus);
        record.setAdjustmentSeq(1);
        record.setDelFlag("1");
        record.setProjectId(bill.getProjectId());
        
        // 设置银行回单日期（charge_time字段）
        LocalDate chargeTime = getEarliestBankReceiptDate(payments, actualCalculationDate);
        if (chargeTime != null) {
            record.setChargeTime(chargeTime);
        }
        
        return record;
    }

    /**
     * 检查是否已处理今日计算
     */
    public boolean isAlreadyProcessedToday(String billId, LocalDate calculationDate) {
        try {
            // 查询今天是否有该账单的正常计算记录
            List<BbpmPenaltyFeeDailyDetailVo> allDetails = penaltyDataService.selectDailyDetailsByBillId(billId);
            
            // 筛选今天的正常计算记录
            boolean hasRecordToday = allDetails.stream()
                .anyMatch(detail -> "1".equals(detail.getEntryType()) && 
                         calculationDate.equals(detail.getCalculationDate()));
                         
            return hasRecordToday;
        } catch (Exception e) {
            log.error("查询账单处理记录失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 计算总缴费金额
     */
    public BigDecimal calculateTotalPaidAmount(List<BbpmCollectionPageResultVo> payments) {
        return payments.stream()
            .map(payment -> payment.getChargeMoney() != null ? payment.getChargeMoney() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取最早的银行回单日期
     */
    public LocalDate getEarliestBankReceiptDate(List<BbpmCollectionPageResultVo> payments, LocalDate defaultDate) {
        return payments.stream()
            .map(payment -> {
                Date receiptDate = payment.getBankReceiptDate();
                if (receiptDate != null) {
                    return receiptDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
                return null;
            })
            .filter(date -> date != null)
            .min(LocalDate::compareTo)
            .orElse(defaultDate);
    }

    /**
     * 解析日期字符串为LocalDate
     */
    public LocalDate parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            if (dateStr.length() == 10) {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else if (dateStr.length() == 8) {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            return LocalDate.parse(dateStr);
        } catch (Exception e) {
            log.warn("解析日期字符串失败：{}", dateStr);
            return null;
        }
    }

    /**
     * 解析BigDecimal
     */
    public BigDecimal parseBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof String) {
            String str = (String) value;
            if (StringUtils.isBlank(str)) {
                return BigDecimal.ZERO;
            }
            try {
                return new BigDecimal(str);
            } catch (NumberFormatException e) {
                log.warn("解析BigDecimal失败：{}", str);
                return BigDecimal.ZERO;
            }
        }
        return new BigDecimal(value.toString());
    }
}
