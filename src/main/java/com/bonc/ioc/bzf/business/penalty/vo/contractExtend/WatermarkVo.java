package com.bonc.ioc.bzf.business.penalty.vo.contractExtend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value="水印", description="水印")
@Data
public class WatermarkVo {
    /**
     * 合同是否带水印(0.无 1.甲 2.乙)
     */
    @ApiModelProperty(value = "合同是否带水印(0.无 1.甲 2.乙)")
    private String contractWatermark;

    /**
     * 乙方水印名称
     */
    @ApiModelProperty(value = "乙方水印名称")
    private String secondWartmarkName;

    /**
     * 乙方水印logo编码
     */
    @ApiModelProperty(value = "乙方水印logo编码")
    private String secondWartmarkLogoId;

    /**
     * 乙方水印logo名称
     */
    @ApiModelProperty(value = "乙方水印logo名称")
    private String secondWartmarkLogoName;

    /**
     * 乙方水印logo地址
     */
    @ApiModelProperty(value = "乙方水印logo地址")
    private String secondWartmarkLogoUrl;
}
