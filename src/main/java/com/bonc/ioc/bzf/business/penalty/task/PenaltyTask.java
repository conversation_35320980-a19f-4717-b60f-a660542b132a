package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.penalty.enums.PenaltyApiType;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyApiCallLogService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyTaskStatusService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyApiCallLogVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyTaskStatusVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillComparisonResultVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo;
import com.bonc.ioc.bzf.business.penalty.vo.ContractInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.sinovatech.unifiedmanage.client.module.timTask.dto.UniManJobCallBackDTO;
import com.sinovatech.unifiedmanage.client.module.timTask.util.UniManConstants;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金计算任务主调度类（重构版）
 * 负责核心任务调度逻辑、任务状态管理、异常处理
 * 具体的业务逻辑已拆分到各个专门的服务类中
 */
@EnableScheduling
@Slf4j
@Component
public class PenaltyTask {

    @Autowired
    private RedissonClient redissonClient;
    
    @Resource
    private IBbpmPenaltyTaskStatusService penaltyTaskStatusService;
    
    @Resource
    private IBbpmPenaltyApiCallLogService penaltyApiCallLogService;
    
    @Resource
    private PenaltyDataService penaltyDataService;
    
    @Resource
    private PenaltyCalculationService penaltyCalculationService;
    
    @Resource
    private PenaltySummaryService penaltySummaryService;
    
    @Resource
    private PenaltyValidationService penaltyValidationService;

    private static String token;
    private static final String LOCK_KEY = "lock:penaltyFeeTrialTask";

    /**
     * 违约金计算任务主入口
     */
    public UniManJobCallBackDTO penaltyFeeTrialTask() {
        log.info("----商业违约金计算任务开始执行----");

        UniManJobCallBackDTO dto = new UniManJobCallBackDTO();
        String result = "成功";
        boolean mainTaskCompleted = false; // 标记主任务是否成功完成
        LocalDate calculationDate = null;
        List<ContractInfoVo> allContracts = null;
        List<ContractInfoVo> unprocessedContracts = null;

        RLock disLock = redissonClient.getLock(LOCK_KEY);
        if (disLock.tryLock()) {
            token = penaltyDataService.getTokenByFixedUser();
            log.info("----商业违约金计算任务获取的token----"+token);
 
            try {
                calculationDate = LocalDate.now();
                log.info("开始执行违约金计算任务，计算日期：{}", calculationDate);
                
                // 1. 从合同中心获取合同列表
                allContracts = penaltyDataService.getAllActiveContracts();
                
                if (allContracts.isEmpty()) {
                    log.info("未查询到有效的商业合同，任务结束");
                    
                    // 创建全局任务状态记录，标记无合同情况
                    try {
                        createGlobalTaskStatus(calculationDate, "2", 0, "未查询到有效的商业合同"); // 状态2-已完成
                        log.info("已创建全局任务状态记录，标记无合同情况");
                    } catch (Exception e) {
                        log.error("创建全局任务状态记录失败：{}", e.getMessage(), e);
                    }
                    
                    // 插入任务主日志记录，便于后续排查问题
                    try {
                        BbpmPenaltyApiCallLogVo emptyContractLog = new BbpmPenaltyApiCallLogVo();
                        emptyContractLog.setTaskDate(calculationDate);
                        emptyContractLog.setApiType(PenaltyApiType.CONTRACT_QUERY.getCode()); // 合同中心接口调用
                        emptyContractLog.setApiUrl("PENALTY_TASK_NO_CONTRACTS");
                        emptyContractLog.setRequestParams("{\"calculationDate\":\"" + calculationDate + "\",\"businessTypeCode1\":\"02\",\"contractStatus\":\"1\"}");
                        emptyContractLog.setResponseData("{\"message\":\"未查询到有效的商业合同\",\"totalContracts\":0,\"validContracts\":0,\"reason\":\"所有合同每日逾期率为空或为0\"}");
                        emptyContractLog.setCallDuration(0);
                        emptyContractLog.setCallStatus("1");
                        emptyContractLog.setCallTime(new Date());
                        emptyContractLog.setDelFlag(1);
                        
                        penaltyApiCallLogService.insertRecord(emptyContractLog);
                        log.info("已记录空合同列表情况到日志主表，便于后续排查");
                    } catch (Exception e) {
                        log.error("记录空合同列表日志失败：{}", e.getMessage(), e);
                    }
                    
                    // 标记特殊完成状态（无合同也算成功完成）
                    mainTaskCompleted = true;
                    
                    // 设置返回结果描述
                    dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
                    dto.setExecuteDesc("未查询到有效的商业合同，任务正常结束");
                    return dto;
                }
                
                log.info("查询到 {} 个有效合同", allContracts.size());
                
                // 2. 初始化任务状态表（首次执行时创建所有待处理任务记录）
                List<String> contractNos = allContracts.stream()
                    .map(ContractInfoVo::getContractNo)
                    .collect(Collectors.toList());
                initializeTaskStatus(contractNos, calculationDate);
                
                // 3. 过滤出未处理的合同（断点续传）
                unprocessedContracts = filterUnprocessedContracts(allContracts, calculationDate);
                
                log.info("总合同数：{}，待处理合同数：{}", allContracts.size(), unprocessedContracts.size());
                
                // 4. 处理每个未处理的合同
                for (ContractInfoVo contractInfo : unprocessedContracts) {
                    processContract(contractInfo, calculationDate);
                }
                
                log.info("违约金计算任务执行完成，处理合同数：{}", unprocessedContracts.size());
                
                // 标记主任务成功完成
                mainTaskCompleted = true;
                result = "成功";

            } catch (Exception e) {
                e.printStackTrace();
                log.error("罚费试算任务执行失败：{}", e.getMessage(), e);
                
                // 异常后更新所有处理中的任务状态为失败
                try {
                    LocalDate currentDate = LocalDate.now(); // 使用当前日期作为fallback
                    updateAllProcessingTasksToFailed(currentDate, e.getMessage());
                    result = "任务执行失败：" + e.getMessage();
                } catch (Exception updateException) {
                    log.error("更新失败状态时发生异常：{}", updateException.getMessage(), updateException);
                    result = "任务执行失败且状态更新失败";
                }
                
            } finally {
                // 只有在主任务真正成功完成时，才创建全局任务统计
                if (mainTaskCompleted && calculationDate != null && allContracts != null && unprocessedContracts != null) {
                    try {
                        log.info("主任务成功完成，开始创建全局任务统计");
                        createTaskCompletionSummary(calculationDate, allContracts.size(), unprocessedContracts.size());
                    } catch (Exception e) {
                        log.error("创建任务完成统计失败：{}", e.getMessage(), e);
                        // 不影响主任务的成功状态，只记录错误
                    }
                } else if (calculationDate != null) {
                    log.info("主任务未成功完成，跳过创建全局任务统计。mainTaskCompleted={}, allContracts={}, unprocessedContracts={}", 
                        mainTaskCompleted, 
                        (allContracts != null ? allContracts.size() : "null"),
                        (unprocessedContracts != null ? unprocessedContracts.size() : "null"));
                }
                
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        } else {
            log.info("商业违约金计算任务已在运行中...");
            result = "商业违约金计算任务已在运行中...";
        }
        log.info("----商业违约金计算任务结束执行----");

        dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
        dto.setExecuteDesc(result);
        return dto;
    }

    /**
     * 初始化任务状态表（首次执行时创建所有待处理任务记录）- 使用幂等性安全初始化
     */
    private void initializeTaskStatus(List<String> allContracts, LocalDate taskDate) {
        try {
            log.info("开始检查并初始化任务状态表，任务日期：{}，合同数量：{}", taskDate, allContracts.size());
            
            // 查询已存在的任务状态记录
            List<BbpmPenaltyTaskStatusVo> existingTaskStatus = penaltyDataService.queryTaskStatusList(taskDate, null, null);
            Set<String> existingContracts = existingTaskStatus.stream()
                .map(BbpmPenaltyTaskStatusVo::getContractCode)
                .collect(Collectors.toSet());
            
            // 找出缺失的合同记录
            List<String> missingContracts = allContracts.stream()
                .filter(contractNo -> !existingContracts.contains(contractNo))
                .collect(Collectors.toList());
            
            if (!missingContracts.isEmpty()) {
                log.info("发现 {} 个合同缺少任务状态记录，开始补充创建", missingContracts.size());
                
                int successCount = 0;
                int failCount = 0;
                
                for (String contractNo : missingContracts) {
                    try {
                        // 使用幂等性插入：先查询再插入
                        List<BbpmPenaltyTaskStatusVo> existingRecord = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
                        if (existingRecord.isEmpty()) {
                            BbpmPenaltyTaskStatusVo taskStatus = new BbpmPenaltyTaskStatusVo();
                            taskStatus.setTaskDate(taskDate);
                            taskStatus.setContractCode(contractNo);
                            taskStatus.setProcessStatus("0"); // 0-待处理
                            taskStatus.setBillCount(0);
                            taskStatus.setDelFlag(1);
                            
                            penaltyTaskStatusService.insertRecord(taskStatus);
                            successCount++;
                            log.debug("成功创建合同 {} 的任务状态记录", contractNo);
                        } else {
                            log.debug("合同 {} 的任务状态记录已存在，跳过创建", contractNo);
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("创建合同 {} 的任务状态记录失败：{}", contractNo, e.getMessage(), e);
                        
                        // 如果是唯一键冲突，尝试查询确认记录是否真实存在
                        if (e.getMessage() != null && e.getMessage().contains("Duplicate")) {
                            try {
                                List<BbpmPenaltyTaskStatusVo> checkRecord = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
                                if (!checkRecord.isEmpty()) {
                                    log.info("合同 {} 的任务状态记录实际已存在，忽略重复插入错误", contractNo);
                                    failCount--; // 不计入失败
                                }
                            } catch (Exception checkEx) {
                                log.error("检查重复记录时发生异常：{}", checkEx.getMessage());
                            }
                        }
                    }
                }
                
                log.info("任务状态表初始化完成 - 成功创建：{}，失败：{}，总合同数：{}", 
                    successCount, failCount, allContracts.size());
                    
                // 如果失败数量过多，记录警告
                if (failCount > 0 && failCount > allContracts.size() * 0.1) {
                    log.warn("任务状态初始化失败率较高：{}/{}, 请检查数据库连接和唯一键约束", failCount, allContracts.size());
                }
            } else {
                log.info("所有合同的任务状态记录都已存在，无需初始化");
            }
            
        } catch (Exception e) {
            log.error("初始化任务状态表失败：{}", e.getMessage(), e);
            throw new RuntimeException("任务状态表初始化失败，停止任务执行", e);
        }
    }

    /**
     * 断点续传过滤方法 - 过滤出未处理的合同
     */
    private List<ContractInfoVo> filterUnprocessedContracts(List<ContractInfoVo> allContracts, LocalDate taskDate) {
        try {
            List<ContractInfoVo> unprocessedContracts = new ArrayList<>();

            // 查询未处理的合同（状态为0-待处理或1-处理中）
            for (ContractInfoVo contractInfo : allContracts) {
                String contractNo = contractInfo.getContractNo();
                String processStatus = getContractProcessStatus(contractNo, taskDate);

                // 如果是待处理(0)或处理中(1)，则需要处理
                if ("0".equals(processStatus) || "1".equals(processStatus)) {
                    unprocessedContracts.add(contractInfo);
                } else if ("2".equals(processStatus)) {
                    log.debug("合同 {} 今天已完成处理，跳过", contractNo);
                } else if ("3".equals(processStatus)) {
                    log.debug("合同 {} 今天处理失败，重新加入处理队列", contractNo);
                    unprocessedContracts.add(contractInfo);
                } else {
                    // 没有记录的合同也需要处理
                    unprocessedContracts.add(contractInfo);
                }
            }

            log.info("筛选出需要处理的合同数量：{}/{}", unprocessedContracts.size(), allContracts.size());
            return unprocessedContracts;

        } catch (Exception e) {
            log.error("查询未处理合同列表失败：{}", e.getMessage(), e);
            // 出错时返回所有合同，确保任务能够继续
            return allContracts;
        }
    }

    /**
     * 合同处理方法（批量对比版本）
     */
    private void processContract(ContractInfoVo contractInfo, LocalDate calculationDate) {
        String contractNo = contractInfo.getContractNo();
        String projectId = contractInfo.getProjectId();
        String taskStatusId = null;
        try {
            // 更新任务状态为处理中并获取任务状态ID
            taskStatusId = updateTaskStatusToProcessing(contractNo, calculationDate);
            log.info("开始处理合同：{}, 项目ID：{}", contractNo, projectId);

            // 1. 查询两种类型的账单（01-企业，02-个人），并合并结果
            List<BbpmBillManagementPageResultVo> allOverdueBills = new ArrayList<>();

            // 1.1 查询企业账单（chargeOwner=01）
            PageResult<List<BbpmBillManagementPageResultVo>> corporateBillsResult =
                penaltyDataService.queryBillsByContractAndType(taskStatusId, contractNo, projectId, "01", calculationDate);
            if (corporateBillsResult != null && corporateBillsResult.getRows() != null) {
                allOverdueBills.addAll(corporateBillsResult.getRows());
                log.debug("合同 {} 查询到 {} 个企业账单", contractNo, corporateBillsResult.getRows().size());
            }

            // 1.2 查询个人账单（chargeOwner=02）
            PageResult<List<BbpmBillManagementPageResultVo>> personalBillsResult =
                penaltyDataService.queryBillsByContractAndType(taskStatusId, contractNo, projectId, "02", calculationDate);
            if (personalBillsResult != null && personalBillsResult.getRows() != null) {
                allOverdueBills.addAll(personalBillsResult.getRows());
                log.debug("合同 {} 查询到 {} 个个人账单", contractNo, personalBillsResult.getRows().size());
            }

            if (allOverdueBills.isEmpty()) {
                log.info("合同 {} 没有逾期账单，跳过处理", contractNo);
                updateTaskStatusToCompleted(taskStatusId, contractNo, 0, calculationDate);
                return;
            }

            log.info("合同 {} 查询到 {} 个逾期账单（企业+个人）", contractNo, allOverdueBills.size());

            // 2. 批量查询所有账单的收款单信息（优化：每个账单只查询一次）
            Map<String, List<BbpmCollectionPageResultVo>> billPaymentsMap = penaltyDataService.batchQueryPaymentsForBills(
                taskStatusId, allOverdueBills, calculationDate, contractNo, projectId);
            log.info("合同 {} 批量查询了 {} 个账单的收款单信息", contractNo, billPaymentsMap.size());

            // 3. 查询库中该合同已有的试算账单记录（所有状态）
            Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap = getExistingTrialBillMap(contractNo);

            // 4. 分类处理账单：新账单 vs 已存在账单
            BillComparisonResultVo comparisonResult = penaltyValidationService.compareBillsWithDatabase(
                allOverdueBills, existingTrialBillMap, billPaymentsMap, calculationDate, contractInfo.getDailyOverdueRate(), projectId);

            // 统计优化效果：计算被过滤的已缴足额支付账单数量
            long totalBills = allOverdueBills.size();
            long processableBills = comparisonResult.getNewBills().size() +
                                   comparisonResult.getStatusChangedBills().size() +
                                   comparisonResult.getUnchangedBills().size();
            long skippedPaidBills = totalBills - processableBills;

            log.info("合同 {} 账单分类结果：新增 {} 个，状态变化 {} 个，无变化 {} 个",
                    contractNo,
                    comparisonResult.getNewBills().size(),
                    comparisonResult.getStatusChangedBills().size(),
                    comparisonResult.getUnchangedBills().size());

            if (skippedPaidBills > 0) {
                log.info("合同 {} 优化效果：跳过了 {} 个已缴足额支付账单（占比 {:.1f}%），减少了不必要的处理",
                        contractNo, skippedPaidBills, (skippedPaidBills * 100.0 / totalBills));
            }

            int processedCount = 0;

            // 5. 处理新增账单
            for (BbpmBillManagementPageResultVo newBill : comparisonResult.getNewBills()) {
                try {
                    log.info("处理新增账单：{}", newBill.getBillId());
                    List<BbpmCollectionPageResultVo> payments = billPaymentsMap.getOrDefault(newBill.getBillId(), new ArrayList<>());
                    processNewBill(newBill, payments, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理新增账单 {} 失败：{}", newBill.getBillId(), e.getMessage(), e);
                }
            }

            // 6. 处理状态变化的账单
            for (BillStatusComparisonVo statusComparison : comparisonResult.getStatusChangedBills()) {
                try {
                    log.info("处理状态变化账单：{} ({}->{})",
                            statusComparison.getBill().getBillId(),
                            statusComparison.getOldStatus(),
                            statusComparison.getNewStatus());
                    processStatusChangedBill(statusComparison, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理状态变化账单 {} 失败：{}", statusComparison.getBill().getBillId(), e.getMessage(), e);
                }
            }

            // 7. 处理无变化账单（正常的每日计算）
            for (BbpmBillManagementPageResultVo unchangedBill : comparisonResult.getUnchangedBills()) {
                try {
                    // 只进行正常的今日违约金计算（如果今天还没算过）
                    if (!penaltyCalculationService.isAlreadyProcessedToday(unchangedBill.getBillId(), calculationDate)) {
                        List<BbpmCollectionPageResultVo> payments = billPaymentsMap.getOrDefault(unchangedBill.getBillId(), new ArrayList<>());
                        penaltyCalculationService.calculateDailyPenalty(unchangedBill, payments, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                        processedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理无变化账单 {} 失败：{}", unchangedBill.getBillId(), e.getMessage(), e);
                }
            }

            // 8. 检查该合同的试算账单是否需要自动完结
            int finalizedCount = 0;
            for (BbpmPenaltyFeeTrialBillVo trialBill : existingTrialBillMap.values()) {
                if ("1".equals(trialBill.getStatus())) { // 只检查试算中的账单
                    try {
                        if (penaltyValidationService.checkAndAutoFinalize(trialBill.getBillId())) {
                            finalizedCount++;
                        }
                    } catch (Exception e) {
                        log.error("检查账单 {} 自动完结失败：{}", trialBill.getBillId(), e.getMessage(), e);
                    }
                }
            }
            if (finalizedCount > 0) {
                log.info("合同 {} 自动完结了 {} 个试算账单", contractNo, finalizedCount);
            }

            // 9. 更新任务状态为已完成
            updateTaskStatusToCompleted(taskStatusId, contractNo, allOverdueBills.size(), calculationDate);
            log.info("合同 {} 处理完成，总账单数：{}，实际处理：{}", contractNo, allOverdueBills.size(), processedCount);

        } catch (Exception e) {
            // 更新任务状态为失败
            updateTaskStatusToFailed(taskStatusId, contractNo, e.getMessage(), calculationDate);
            log.error("处理合同 {} 失败：{}", contractNo, e.getMessage(), e);
        }
    }

    /**
     * 获取合同已有的试算账单记录映射
     */
    private Map<String, BbpmPenaltyFeeTrialBillVo> getExistingTrialBillMap(String contractNo) {
        Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap = new java.util.HashMap<>();
        try {
            List<BbpmPenaltyFeeTrialBillVo> trialBills = penaltyDataService.queryTrialBillList(contractNo, null);
            for (BbpmPenaltyFeeTrialBillVo trialBill : trialBills) {
                if (trialBill != null) {
                    existingTrialBillMap.put(trialBill.getBillId(), trialBill);
                }
            }

            log.info("合同 {} 库中已有 {} 个试算账单记录（包含所有状态）", contractNo, existingTrialBillMap.size());

        } catch (Exception e) {
            log.error("查询合同 {} 已有试算账单失败：{}", contractNo, e.getMessage(), e);
        }
        return existingTrialBillMap;
    }

    /**
     * 处理新增账单（优化版：使用预先查询的收款单信息）
     */
    private void processNewBill(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments,
                               LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 1. 使用预先查询的收款单信息（优化：避免重复查询）

            // 2. 分析状态
            String reconciliationStatus = penaltyDataService.analyzeReconciliationStatus(payments);
            LocalDate earliestReceiptDate = penaltyCalculationService.getEarliestBankReceiptDate(payments, calculationDate);

            // 3. 创建试算账单记录（新账单一定需要创建）
            penaltyValidationService.ensureTrialBillExists(bill, payments, reconciliationStatus, earliestReceiptDate, dailyOverdueRate);

            // 4. 计算违约金（从账单逾期开始计算到今天）
            penaltyCalculationService.calculateHistoricalPenalties(bill, payments, calculationDate, taskStatusId, dailyOverdueRate, projectId);

            log.info("新增账单 {} 处理完成", bill.getBillId());

        } catch (Exception e) {
            log.error("处理新增账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理状态变化的账单
     */
    private void processStatusChangedBill(BillStatusComparisonVo statusComparison, LocalDate calculationDate,
                                         String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        BbpmBillManagementPageResultVo bill = statusComparison.getBill();
        String oldStatus = statusComparison.getOldStatus();
        String newStatus = statusComparison.getNewStatus();

        try {
            // 1. 记录状态变化到API日志
            penaltyDataService.recordReconciliationStatusChange(bill.getBillId(), oldStatus, newStatus);

            // 2. 获取银行回单日期，用于确定调整起始日期
            LocalDate earliestReceiptDate = penaltyCalculationService.getEarliestBankReceiptDate(statusComparison.getCurrentPayments(), calculationDate);

            // 3. 触发违约金调整（红冲之前的记录）
            penaltyValidationService.adjustPenalty(bill, earliestReceiptDate != null ? earliestReceiptDate : calculationDate, projectId);

            // 4. 更新试算账单的对账状态
            penaltyValidationService.ensureTrialBillExists(bill, statusComparison.getCurrentPayments(), newStatus, earliestReceiptDate, dailyOverdueRate);

            // 5. 今日违约金计算（正常流程）
            if (!penaltyCalculationService.isAlreadyProcessedToday(bill.getBillId(), calculationDate)) {
                penaltyCalculationService.calculateDailyPenalty(bill, statusComparison.getCurrentPayments(), calculationDate, taskStatusId, dailyOverdueRate, projectId);
            }

            // 6. 更新试算账单汇总信息
            penaltySummaryService.updateTrialBillSummary(bill.getBillId(), calculationDate);

            log.info("状态变化账单 {} 处理完成", bill.getBillId());

        } catch (Exception e) {
            log.error("处理状态变化账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取合同的处理状态
     */
    private String getContractProcessStatus(String contractNo, LocalDate taskDate) {
        try {
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
            if (!taskStatusList.isEmpty()) {
                return taskStatusList.get(0).getProcessStatus();
            }

            return null; // 没有记录

        } catch (Exception e) {
            log.error("获取合同处理状态失败：contractNo={}, taskDate={}", contractNo, taskDate, e);
            return null;
        }
    }

    /**
     * 更新任务状态为处理中（使用幂等性安全操作）
     */
    private String updateTaskStatusToProcessing(String contractNo, LocalDate taskDate) {
        try {
            // 查找现有的任务状态记录
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);

            if (!taskStatusList.isEmpty()) {
                // 更新现有记录
                BbpmPenaltyTaskStatusVo taskStatus = taskStatusList.get(0);
                if (!"1".equals(taskStatus.getProcessStatus())) {  // 如果不是已经是处理中状态，才更新
                    taskStatus.setProcessStatus("1"); // 1-处理中
                    taskStatus.setStartTime(new Date());
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                    log.debug("合同 {} 任务状态已更新为处理中", contractNo);
                } else {
                    log.debug("合同 {} 任务状态已是处理中，无需更新", contractNo);
                }
                return taskStatus.getTaskStatusId();
            } else {
                // 创建新记录（但这种情况理论上不应该发生，因为初始化时应该已经创建了所有记录）
                log.warn("合同 {} 在任务日期 {} 没有找到任务状态记录，将创建新记录", contractNo, taskDate);

                // 使用幂等性插入：再次检查避免并发创建
                try {
                    BbpmPenaltyTaskStatusVo taskStatus = new BbpmPenaltyTaskStatusVo();
                    taskStatus.setTaskDate(taskDate);
                    taskStatus.setContractCode(contractNo);
                    taskStatus.setProcessStatus("1"); // 1-处理中
                    taskStatus.setBillCount(0);
                    taskStatus.setStartTime(new Date());
                    taskStatus.setDelFlag(1);

                    String taskStatusId = penaltyTaskStatusService.insertRecord(taskStatus);
                    log.info("成功为合同 {} 创建新的任务状态记录：{}", contractNo, taskStatusId);
                    return taskStatusId;

                } catch (Exception insertEx) {
                    // 如果插入失败（可能是唯一键冲突），尝试查询已存在的记录
                    if (insertEx.getMessage() != null && insertEx.getMessage().contains("Duplicate")) {
                        log.info("检测到唯一键冲突，重新查询合同 {} 的任务状态记录", contractNo);
                        List<BbpmPenaltyTaskStatusVo> retryTaskStatusList = penaltyDataService.queryTaskStatusList(taskDate, contractNo, null);
                        if (!retryTaskStatusList.isEmpty()) {
                            BbpmPenaltyTaskStatusVo existingTaskStatus = retryTaskStatusList.get(0);
                            if (!"1".equals(existingTaskStatus.getProcessStatus())) {
                                existingTaskStatus.setProcessStatus("1");
                                existingTaskStatus.setStartTime(new Date());
                                penaltyTaskStatusService.updateByIdRecord(existingTaskStatus);
                            }
                            return existingTaskStatus.getTaskStatusId();
                        }
                    }
                    throw insertEx; // 其他异常继续抛出
                }
            }

        } catch (Exception e) {
            log.error("更新任务状态为处理中失败：contractNo={}, taskDate={}", contractNo, taskDate, e);
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    /**
     * 更新任务状态为已完成
     */
    private void updateTaskStatusToCompleted(String taskStatusId, String contractNo, int billCount, LocalDate taskDate) {
        try {
            if (StringUtils.isNotBlank(taskStatusId)) {
                BbpmPenaltyTaskStatusVo taskStatus = penaltyTaskStatusService.selectByIdRecord(taskStatusId);
                if (taskStatus != null) {
                    taskStatus.setProcessStatus("2"); // 2-已完成
                    taskStatus.setBillCount(billCount);
                    taskStatus.setEndTime(new Date());
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                } else {
                    log.warn("未找到任务状态记录：taskStatusId={}", taskStatusId);
                }
            }
        } catch (Exception e) {
            log.error("更新任务状态为已完成失败：taskStatusId={}, contractNo={}", taskStatusId, contractNo, e);
        }
    }

    /**
     * 更新任务状态为失败
     */
    private void updateTaskStatusToFailed(String taskStatusId, String contractNo, String errorMessage, LocalDate taskDate) {
        try {
            if (StringUtils.isNotBlank(taskStatusId)) {
                BbpmPenaltyTaskStatusVo taskStatus = penaltyTaskStatusService.selectByIdRecord(taskStatusId);
                if (taskStatus != null) {
                    taskStatus.setProcessStatus("3"); // 3-失败
                    taskStatus.setEndTime(new Date());
                    taskStatus.setErrorMessage(errorMessage);
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                } else {
                    log.warn("未找到任务状态记录：taskStatusId={}", taskStatusId);
                }
            }
        } catch (Exception e) {
            log.error("更新任务状态为失败失败：taskStatusId={}, contractNo={}", taskStatusId, contractNo, e);
        }
    }

    /**
     * 创建全局任务状态记录（用于标记特殊情况，如无合同等）
     */
    private void createGlobalTaskStatus(LocalDate taskDate, String processStatus, int billCount, String remark) {
        try {
            // 使用特殊的合同编号来标识全局任务状态
            String globalContractCode = "GLOBAL_TASK_" + taskDate.toString().replace("-", "");

            // 检查是否已存在全局任务状态记录
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, globalContractCode, null);

            if (!taskStatusList.isEmpty()) {
                // 更新现有记录
                BbpmPenaltyTaskStatusVo taskStatus = taskStatusList.get(0);
                taskStatus.setProcessStatus(processStatus);
                taskStatus.setBillCount(billCount);
                taskStatus.setEndTime(new Date());
                taskStatus.setErrorMessage(remark);
                penaltyTaskStatusService.updateByIdRecord(taskStatus);
                log.info("已更新全局任务状态记录：{}", globalContractCode);
            } else {
                // 创建新的全局任务状态记录
                BbpmPenaltyTaskStatusVo globalTaskStatus = new BbpmPenaltyTaskStatusVo();
                globalTaskStatus.setTaskDate(taskDate);
                globalTaskStatus.setContractCode(globalContractCode);
                globalTaskStatus.setProcessStatus(processStatus);
                globalTaskStatus.setBillCount(billCount);
                globalTaskStatus.setStartTime(new Date());
                globalTaskStatus.setEndTime(new Date());
                globalTaskStatus.setErrorMessage(remark);
                globalTaskStatus.setDelFlag(1);

                penaltyTaskStatusService.insertRecord(globalTaskStatus);
                log.info("已创建全局任务状态记录：{}", globalContractCode);
            }

        } catch (Exception e) {
            log.error("创建全局任务状态记录失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 创建任务完成统计（记录到全局任务状态表）
     */
    private void createTaskCompletionSummary(LocalDate taskDate, int totalContracts, int processedContracts) {
        try {
            // 统计各状态的任务数量
            List<BbpmPenaltyTaskStatusVo> taskStatusList = penaltyDataService.queryTaskStatusList(taskDate, null, null);

            int completedCount = 0;
            int failedCount = 0;
            int processingCount = 0;

            for (BbpmPenaltyTaskStatusVo task : taskStatusList) {
                String status = task.getProcessStatus();
                if ("2".equals(status)) {
                    completedCount++;
                } else if ("3".equals(status)) {
                    failedCount++;
                } else if ("1".equals(status)) {
                    processingCount++;
                }
            }

            // 创建全局任务统计记录
            String summaryMessage = String.format(
                "任务执行统计 - 总合同数:%d, 处理合同数:%d, 已完成:%d, 失败:%d, 仍在处理:%d",
                totalContracts, processedContracts, completedCount, failedCount, processingCount);

            createGlobalTaskStatus(taskDate, "2", totalContracts, summaryMessage);
            log.info(summaryMessage);

        } catch (Exception e) {
            log.error("创建任务完成统计失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 更新所有处理中的任务状态为失败（用于主任务异常处理）
     */
    private void updateAllProcessingTasksToFailed(LocalDate taskDate, String errorMessage) {
        try {
            log.info("开始更新所有处理中的任务状态为失败，任务日期：{}", taskDate);

            // 查询所有处理中的任务（状态为1）
            List<BbpmPenaltyTaskStatusVo> processingTasks = penaltyDataService.queryTaskStatusList(taskDate, null, "1");

            if (!processingTasks.isEmpty()) {
                int failedCount = 0;
                for (BbpmPenaltyTaskStatusVo taskStatus : processingTasks) {
                    try {
                        taskStatus.setProcessStatus("3"); // 3-失败
                        taskStatus.setEndTime(new Date());
                        taskStatus.setErrorMessage("主任务异常终止：" + errorMessage);
                        penaltyTaskStatusService.updateByIdRecord(taskStatus);
                        failedCount++;
                    } catch (Exception e) {
                        log.error("更新任务状态失败：taskStatusId={}", taskStatus.getTaskStatusId(), e);
                    }
                }
                log.info("已将 {} 个处理中的任务状态更新为失败", failedCount);
            } else {
                log.info("没有找到处理中的任务需要更新为失败状态");
            }

        } catch (Exception e) {
            log.error("批量更新任务状态为失败时发生异常：{}", e.getMessage(), e);
        }
    }

    // ==================== 清理和维护方法 ====================

    /**
     * 清理错误的全局任务状态记录（用于修复程序异常后的状态问题）
     * 此方法会删除可能错误创建的 GLOBAL_TASK_ 记录，让任务可以重新执行
     *
     * @param taskDate 任务日期
     */
    public void cleanupIncorrectGlobalTaskStatus(LocalDate taskDate) {
        try {
            String globalContractCode = "GLOBAL_TASK_" + taskDate.toString().replace("-", "");

            log.info("开始检查并清理可能错误的全局任务状态记录：{}", globalContractCode);

            // 查询 GLOBAL_TASK_ 记录
            List<BbpmPenaltyTaskStatusVo> globalTaskList = penaltyDataService.queryTaskStatusList(taskDate, globalContractCode, null);

            if (globalTaskList.isEmpty()) {
                log.info("未找到全局任务状态记录：{}", globalContractCode);
                return;
            }

            // 检查普通合同的任务状态，判断全局状态是否准确
            List<BbpmPenaltyTaskStatusVo> allTaskStatus = penaltyDataService.queryTaskStatusList(taskDate, null, null);
            List<BbpmPenaltyTaskStatusVo> normalContractTasks = allTaskStatus.stream()
                .filter(task -> !task.getContractCode().startsWith("GLOBAL_TASK_"))
                .collect(Collectors.toList());

            if (normalContractTasks.isEmpty()) {
                log.info("没有普通合同任务记录，全局任务状态记录可能是正确的（无合同情况）");
                return;
            }

            // 统计各状态任务数量
            long completedCount = normalContractTasks.stream().filter(t -> "2".equals(t.getProcessStatus())).count();
            long failedCount = normalContractTasks.stream().filter(t -> "3".equals(t.getProcessStatus())).count();
            long processingCount = normalContractTasks.stream().filter(t -> "1".equals(t.getProcessStatus())).count();
            long pendingCount = normalContractTasks.stream().filter(t -> "0".equals(t.getProcessStatus())).count();

            BbpmPenaltyTaskStatusVo globalTask = globalTaskList.get(0);

            log.info("全局任务状态：{}，普通任务统计 - 总数：{}，已完成：{}，失败：{}，处理中：{}，待处理：{}",
                globalTask.getProcessStatus(), normalContractTasks.size(),
                completedCount, failedCount, processingCount, pendingCount);

            // 如果全局任务状态是"已完成"，但还有未完成的普通任务，则认为是错误状态
            boolean isGlobalStatusIncorrect = "2".equals(globalTask.getProcessStatus()) &&
                (processingCount > 0 || pendingCount > 0 || failedCount > 0);

            if (isGlobalStatusIncorrect) {
                log.warn("检测到错误的全局任务状态：全局状态为已完成，但还有 {} 个未完成的普通任务",
                    (processingCount + pendingCount + failedCount));

                // 删除错误的全局任务状态记录
                try {
                    penaltyTaskStatusService.removeByIdRecord(globalTask.getTaskStatusId());
                    log.info("已删除错误的全局任务状态记录：{}", globalContractCode);
                } catch (Exception deleteEx) {
                    log.error("删除错误的全局任务状态记录失败：{}", deleteEx.getMessage());
                }
            } else {
                log.info("全局任务状态看起来是正确的，无需清理");
            }

        } catch (Exception e) {
            log.error("清理错误的全局任务状态记录失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 清理重复或异常的任务状态记录（用于修复数据不一致问题）
     * 注意：此方法应谨慎使用，建议在维护窗口期间执行
     */
    public void cleanupDuplicateTaskStatus(LocalDate taskDate) {
        try {
            log.info("开始检查并清理任务日期 {} 的重复任务状态记录", taskDate);

            // 查询指定日期的所有任务状态记录
            List<BbpmPenaltyTaskStatusVo> allTaskStatus = penaltyDataService.queryTaskStatusList(taskDate, null, null);

            if (allTaskStatus.isEmpty()) {
                log.info("任务日期 {} 没有找到任务状态记录", taskDate);
                return;
            }

            // 按合同编号分组，检查重复
            Map<String, List<BbpmPenaltyTaskStatusVo>> contractGroups = allTaskStatus.stream()
                .collect(Collectors.groupingBy(BbpmPenaltyTaskStatusVo::getContractCode));

            int duplicateCount = 0;
            int cleanedCount = 0;

            for (Map.Entry<String, List<BbpmPenaltyTaskStatusVo>> entry : contractGroups.entrySet()) {
                String contractNo = entry.getKey();
                List<BbpmPenaltyTaskStatusVo> records = entry.getValue();

                if (records.size() > 1) {
                    duplicateCount++;
                    log.warn("发现合同 {} 在任务日期 {} 有 {} 条重复记录", contractNo, taskDate, records.size());

                    // 保留策略：优先保留状态不是"0"（待处理）的记录，其次保留最新创建的记录
                    BbpmPenaltyTaskStatusVo keepRecord = records.stream()
                        .filter(r -> !"0".equals(r.getProcessStatus()))
                        .findFirst()
                        .orElse(records.stream()
                            .max((r1, r2) -> {
                                Date d1 = r1.getCreateTime();
                                Date d2 = r2.getCreateTime();
                                if (d1 == null && d2 == null) return 0;
                                if (d1 == null) return -1;
                                if (d2 == null) return 1;
                                return d1.compareTo(d2);
                            })
                            .orElse(records.get(0)));

                    log.info("合同 {} 将保留记录：ID={}, Status={}, CreateTime={}",
                        contractNo, keepRecord.getTaskStatusId(), keepRecord.getProcessStatus(), keepRecord.getCreateTime());

                    // 删除其他重复记录
                    for (BbpmPenaltyTaskStatusVo record : records) {
                        if (!record.getTaskStatusId().equals(keepRecord.getTaskStatusId())) {
                            try {
                                penaltyTaskStatusService.removeByIdRecord(record.getTaskStatusId());
                                cleanedCount++;
                                log.info("已删除重复记录：ID={}, Status={}", record.getTaskStatusId(), record.getProcessStatus());
                            } catch (Exception deleteEx) {
                                log.error("删除重复记录失败：ID={}, 错误：{}", record.getTaskStatusId(), deleteEx.getMessage());
                            }
                        }
                    }
                }
            }

            log.info("任务状态记录清理完成 - 发现重复合同：{}，清理记录：{}，剩余合同：{}",
                duplicateCount, cleanedCount, contractGroups.size());

        } catch (Exception e) {
            log.error("清理重复任务状态记录失败：{}", e.getMessage(), e);
        }
    }
}
