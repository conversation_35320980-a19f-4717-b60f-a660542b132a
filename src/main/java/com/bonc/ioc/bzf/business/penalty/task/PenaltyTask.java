package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.penalty.enums.PenaltyApiType;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyApiCallLogService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyTaskStatusService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyApiCallLogVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyTaskStatusVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillComparisonResultVo;
import com.bonc.ioc.bzf.business.penalty.vo.BillStatusComparisonVo;
import com.bonc.ioc.bzf.business.penalty.vo.ContractInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.sinovatech.unifiedmanage.client.module.timTask.dto.UniManJobCallBackDTO;
import com.sinovatech.unifiedmanage.client.module.timTask.util.UniManConstants;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金计算任务主调度类（重构版）
 * 负责核心任务调度逻辑、任务状态管理、异常处理
 * 具体的业务逻辑已拆分到各个专门的服务类中
 */
@EnableScheduling
@Slf4j
@Component
public class PenaltyTask {

    @Autowired
    private RedissonClient redissonClient;
    
    @Resource
    private IBbpmPenaltyApiCallLogService penaltyApiCallLogService;

    @Resource
    private PenaltyDataService penaltyDataService;

    @Resource
    private PenaltyValidationService penaltyValidationService;

    @Resource
    private PenaltyTaskStatusService penaltyTaskStatusService;

    @Resource
    private PenaltyBillProcessService penaltyBillProcessService;

    private static String token;
    private static final String LOCK_KEY = "lock:penaltyFeeTrialTask";

    /**
     * 违约金计算任务主入口
     */
    public UniManJobCallBackDTO penaltyFeeTrialTask() {
        log.info("----商业违约金计算任务开始执行----");

        UniManJobCallBackDTO dto = new UniManJobCallBackDTO();
        String result = "成功";
        boolean mainTaskCompleted = false; // 标记主任务是否成功完成
        LocalDate calculationDate = null;
        List<ContractInfoVo> allContracts = null;
        List<ContractInfoVo> unprocessedContracts = null;

        RLock disLock = redissonClient.getLock(LOCK_KEY);
        if (disLock.tryLock()) {
            token = penaltyDataService.getTokenByFixedUser();
            log.info("----商业违约金计算任务获取的token----"+token);
 
            try {
                calculationDate = LocalDate.now();
                log.info("开始执行违约金计算任务，计算日期：{}", calculationDate);
                
                // 1. 从合同中心获取合同列表
                allContracts = penaltyDataService.getAllActiveContracts();
                
                if (allContracts.isEmpty()) {
                    log.info("未查询到有效的商业合同，任务结束");
                    
                    // 创建全局任务状态记录，标记无合同情况
                    try {
                        penaltyTaskStatusService.createGlobalTaskStatus(calculationDate, "2", 0, "未查询到有效的商业合同"); // 状态2-已完成
                        log.info("已创建全局任务状态记录，标记无合同情况");
                    } catch (Exception e) {
                        log.error("创建全局任务状态记录失败：{}", e.getMessage(), e);
                    }
                    
                    // 插入任务主日志记录，便于后续排查问题
                    try {
                        BbpmPenaltyApiCallLogVo emptyContractLog = new BbpmPenaltyApiCallLogVo();
                        emptyContractLog.setTaskDate(calculationDate);
                        emptyContractLog.setApiType(PenaltyApiType.CONTRACT_QUERY.getCode()); // 合同中心接口调用
                        emptyContractLog.setApiUrl("PENALTY_TASK_NO_CONTRACTS");
                        emptyContractLog.setRequestParams("{\"calculationDate\":\"" + calculationDate + "\",\"businessTypeCode1\":\"02\",\"contractStatus\":\"1\"}");
                        emptyContractLog.setResponseData("{\"message\":\"未查询到有效的商业合同\",\"totalContracts\":0,\"validContracts\":0,\"reason\":\"所有合同每日逾期率为空或为0\"}");
                        emptyContractLog.setCallDuration(0);
                        emptyContractLog.setCallStatus("1");
                        emptyContractLog.setCallTime(new Date());
                        emptyContractLog.setDelFlag(1);
                        
                        penaltyApiCallLogService.insertRecord(emptyContractLog);
                        log.info("已记录空合同列表情况到日志主表，便于后续排查");
                    } catch (Exception e) {
                        log.error("记录空合同列表日志失败：{}", e.getMessage(), e);
                    }
                    
                    // 标记特殊完成状态（无合同也算成功完成）
                    mainTaskCompleted = true;
                    
                    // 设置返回结果描述
                    dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
                    dto.setExecuteDesc("未查询到有效的商业合同，任务正常结束");
                    return dto;
                }
                
                log.info("查询到 {} 个有效合同", allContracts.size());
                
                // 2. 初始化任务状态表（首次执行时创建所有待处理任务记录）
                List<String> contractNos = allContracts.stream()
                    .map(ContractInfoVo::getContractNo)
                    .collect(Collectors.toList());
                penaltyTaskStatusService.initializeTaskStatus(contractNos, calculationDate);

                // 3. 过滤出未处理的合同（断点续传）
                unprocessedContracts = penaltyBillProcessService.filterUnprocessedContracts(allContracts, calculationDate);
                
                log.info("总合同数：{}，待处理合同数：{}", allContracts.size(), unprocessedContracts.size());
                
                // 4. 处理每个未处理的合同
                for (ContractInfoVo contractInfo : unprocessedContracts) {
                    processContract(contractInfo, calculationDate);
                }
                
                log.info("违约金计算任务执行完成，处理合同数：{}", unprocessedContracts.size());
                
                // 标记主任务成功完成
                mainTaskCompleted = true;
                result = "成功";

            } catch (Exception e) {
                e.printStackTrace();
                log.error("罚费试算任务执行失败：{}", e.getMessage(), e);
                
                // 异常后更新所有处理中的任务状态为失败
                try {
                    LocalDate currentDate = LocalDate.now(); // 使用当前日期作为fallback
                    penaltyTaskStatusService.updateAllProcessingTasksToFailed(currentDate, e.getMessage());
                    result = "任务执行失败：" + e.getMessage();
                } catch (Exception updateException) {
                    log.error("更新失败状态时发生异常：{}", updateException.getMessage(), updateException);
                    result = "任务执行失败且状态更新失败";
                }
                
            } finally {
                // 只有在主任务真正成功完成时，才创建全局任务统计
                if (mainTaskCompleted && calculationDate != null && allContracts != null && unprocessedContracts != null) {
                    try {
                        log.info("主任务成功完成，开始创建全局任务统计");
                        penaltyTaskStatusService.createTaskCompletionSummary(calculationDate, allContracts.size(), unprocessedContracts.size());
                    } catch (Exception e) {
                        log.error("创建任务完成统计失败：{}", e.getMessage(), e);
                        // 不影响主任务的成功状态，只记录错误
                    }
                } else if (calculationDate != null) {
                    log.info("主任务未成功完成，跳过创建全局任务统计。mainTaskCompleted={}, allContracts={}, unprocessedContracts={}",
                        mainTaskCompleted,
                        (allContracts != null ? allContracts.size() : "null"),
                        (unprocessedContracts != null ? unprocessedContracts.size() : "null"));
                }
                
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        } else {
            log.info("商业违约金计算任务已在运行中...");
            result = "商业违约金计算任务已在运行中...";
        }
        log.info("----商业违约金计算任务结束执行----");

        dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
        dto.setExecuteDesc(result);
        return dto;
    }



    /**
     * 合同处理方法（批量对比版本）
     */
    private void processContract(ContractInfoVo contractInfo, LocalDate calculationDate) {
        String contractNo = contractInfo.getContractNo();
        String projectId = contractInfo.getProjectId();
        String taskStatusId = null;
        try {
            // 更新任务状态为处理中并获取任务状态ID
            taskStatusId = penaltyTaskStatusService.updateTaskStatusToProcessing(contractNo, calculationDate);
            log.info("开始处理合同：{}, 项目ID：{}", contractNo, projectId);

            // 1. 查询两种类型的账单（01-企业，02-个人），并合并结果
            List<BbpmBillManagementPageResultVo> allOverdueBills = new ArrayList<>();

            // 1.1 查询企业账单（chargeOwner=01）
            PageResult<List<BbpmBillManagementPageResultVo>> corporateBillsResult =
                penaltyDataService.queryBillsByContractAndType(taskStatusId, contractNo, projectId, "01", calculationDate);
            if (corporateBillsResult != null && corporateBillsResult.getRows() != null) {
                allOverdueBills.addAll(corporateBillsResult.getRows());
                log.debug("合同 {} 查询到 {} 个企业账单", contractNo, corporateBillsResult.getRows().size());
            }

            // 1.2 查询个人账单（chargeOwner=02）
            PageResult<List<BbpmBillManagementPageResultVo>> personalBillsResult =
                penaltyDataService.queryBillsByContractAndType(taskStatusId, contractNo, projectId, "02", calculationDate);
            if (personalBillsResult != null && personalBillsResult.getRows() != null) {
                allOverdueBills.addAll(personalBillsResult.getRows());
                log.debug("合同 {} 查询到 {} 个个人账单", contractNo, personalBillsResult.getRows().size());
            }

            if (allOverdueBills.isEmpty()) {
                log.info("合同 {} 没有逾期账单，跳过处理", contractNo);
                penaltyTaskStatusService.updateTaskStatusToCompleted(taskStatusId, contractNo, 0, calculationDate);
                return;
            }

            log.info("合同 {} 查询到 {} 个逾期账单（企业+个人）", contractNo, allOverdueBills.size());

            // 2. 批量查询所有账单的收款单信息（优化：每个账单只查询一次）
            Map<String, List<BbpmCollectionPageResultVo>> billPaymentsMap = penaltyDataService.batchQueryPaymentsForBills(
                taskStatusId, allOverdueBills, calculationDate, contractNo, projectId);
            log.info("合同 {} 批量查询了 {} 个账单的收款单信息", contractNo, billPaymentsMap.size());

            // 3. 查询库中该合同已有的试算账单记录（所有状态）
            Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap = penaltyBillProcessService.getExistingTrialBillMap(contractNo);

            // 4. 分类处理账单：新账单 vs 已存在账单
            BillComparisonResultVo comparisonResult = penaltyValidationService.compareBillsWithDatabase(
                allOverdueBills, existingTrialBillMap, billPaymentsMap, calculationDate, contractInfo.getDailyOverdueRate(), projectId);

            // 统计优化效果：计算被过滤的已缴足额支付账单数量
            long totalBills = allOverdueBills.size();
            long processableBills = comparisonResult.getNewBills().size() +
                                   comparisonResult.getStatusChangedBills().size() +
                                   comparisonResult.getUnchangedBills().size();
            long skippedPaidBills = totalBills - processableBills;

            log.info("合同 {} 账单分类结果：新增 {} 个，状态变化 {} 个，无变化 {} 个",
                    contractNo,
                    comparisonResult.getNewBills().size(),
                    comparisonResult.getStatusChangedBills().size(),
                    comparisonResult.getUnchangedBills().size());

            if (skippedPaidBills > 0) {
                log.info("合同 {} 优化效果：跳过了 {} 个已缴足额支付账单（占比 {:.1f}%），减少了不必要的处理",
                        contractNo, skippedPaidBills, (skippedPaidBills * 100.0 / totalBills));
            }

            int processedCount = 0;

            // 5. 处理新增账单
            for (BbpmBillManagementPageResultVo newBill : comparisonResult.getNewBills()) {
                try {
                    log.info("处理新增账单：{}", newBill.getBillId());
                    List<BbpmCollectionPageResultVo> payments = billPaymentsMap.getOrDefault(newBill.getBillId(), new ArrayList<>());
                    penaltyBillProcessService.processNewBill(newBill, payments, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理新增账单 {} 失败：{}", newBill.getBillId(), e.getMessage(), e);
                }
            }

            // 6. 处理状态变化的账单
            for (BillStatusComparisonVo statusComparison : comparisonResult.getStatusChangedBills()) {
                try {
                    log.info("处理状态变化账单：{} ({}->{})",
                            statusComparison.getBill().getBillId(),
                            statusComparison.getOldStatus(),
                            statusComparison.getNewStatus());
                    penaltyBillProcessService.processStatusChangedBill(statusComparison, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理状态变化账单 {} 失败：{}", statusComparison.getBill().getBillId(), e.getMessage(), e);
                }
            }

            // 7. 处理无变化账单（正常的每日计算）
            for (BbpmBillManagementPageResultVo unchangedBill : comparisonResult.getUnchangedBills()) {
                try {
                    List<BbpmCollectionPageResultVo> payments = billPaymentsMap.getOrDefault(unchangedBill.getBillId(), new ArrayList<>());
                    penaltyBillProcessService.processUnchangedBill(unchangedBill, payments, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理无变化账单 {} 失败：{}", unchangedBill.getBillId(), e.getMessage(), e);
                }
            }

            // 8. 检查该合同的试算账单是否需要自动完结
            int finalizedCount = 0;
            for (BbpmPenaltyFeeTrialBillVo trialBill : existingTrialBillMap.values()) {
                if ("1".equals(trialBill.getStatus())) { // 只检查试算中的账单
                    try {
                        if (penaltyValidationService.checkAndAutoFinalize(trialBill.getBillId())) {
                            finalizedCount++;
                        }
                    } catch (Exception e) {
                        log.error("检查账单 {} 自动完结失败：{}", trialBill.getBillId(), e.getMessage(), e);
                    }
                }
            }
            if (finalizedCount > 0) {
                log.info("合同 {} 自动完结了 {} 个试算账单", contractNo, finalizedCount);
            }

            // 9. 更新任务状态为已完成
            penaltyTaskStatusService.updateTaskStatusToCompleted(taskStatusId, contractNo, allOverdueBills.size(), calculationDate);
            log.info("合同 {} 处理完成，总账单数：{}，实际处理：{}", contractNo, allOverdueBills.size(), processedCount);

        } catch (Exception e) {
            // 更新任务状态为失败
            penaltyTaskStatusService.updateTaskStatusToFailed(taskStatusId, contractNo, e.getMessage(), calculationDate);
            log.error("处理合同 {} 失败：{}", contractNo, e.getMessage(), e);
        }
    }
}
