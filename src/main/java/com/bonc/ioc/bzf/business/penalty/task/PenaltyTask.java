package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;

import javax.annotation.Resource;


import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.bonc.ioc.bzf.business.payment.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.utils.JsonToObjectUtil;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.business.penalty.enums.PenaltyApiType;
import com.bonc.ioc.bzf.business.penalty.service.*;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import com.bonc.ioc.bzf.business.penalty.vo.contractExtend.ContractOtherInfo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.feign.service.IBmsBaseServiceFeignService;
import com.sinovatech.unifiedmanage.client.module.timTask.dto.UniManJobCallBackDTO;
import com.sinovatech.unifiedmanage.client.module.timTask.util.UniManConstants;

import lombok.extern.slf4j.Slf4j;

@EnableScheduling
@Slf4j
@Component
public class PenaltyTask {
    

    @Autowired
    private RedissonClient redissonClient;
 
    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;
 
    @Resource
    private IBmsBaseServiceFeignService bmsBaseServiceFeignService;

    @Resource 
    private BbctContractFeignClient contractFeignClient;
    
    @Resource
    private IBbpmPenaltyFeeTrialBillService penaltyFeeTrialBillService;
    
    @Resource
    private IBbpmPenaltyFeeDailyDetailService penaltyFeeDailyDetailService;
    
    @Resource
    private IBbpmPenaltyTaskStatusService penaltyTaskStatusService;
    
    @Resource
    private IBbpmPenaltyApiCallLogService penaltyApiCallLogService;
    
    @Resource
    private IBbpmPenaltyFeeStageSummaryService penaltyFeeStageSummaryService;

    @Resource
    private IBbpmPenaltyFeeFinalizedBillService penaltyFeeFinalizedBillService;

    private static String token;
    
    private static final String LOCK_KEY = "lock:penaltyFeeTrialTask";

 
//    @Scheduled(cron ="${reminderRules.schedule.cron}")
//    @Scheduled(cron = "*/10 * * * * ?")
//    @Scheduled(cron ="0 0 10 * * ?")
//    @Transactional(rollbackFor = {Exception.class})
    public UniManJobCallBackDTO penaltyFeeTrialTask() {
        log.info("----商业违约金计算任务开始执行----");

        UniManJobCallBackDTO dto = new UniManJobCallBackDTO();

        String reuslt = "成功";
        boolean mainTaskCompleted = false; // 标记主任务是否成功完成
        LocalDate calculationDate = null;
        List<ContractInfoVo> allContracts = null;
        List<ContractInfoVo> unprocessedContracts = null;

        RLock disLock = redissonClient.getLock(LOCK_KEY);
        if (disLock.tryLock()) {
            token = getTokenByFixedUser();
            log.info("----商业违约金计算任务获取的token----"+token);
 
            try {
                calculationDate = LocalDate.now();
                log.info("开始执行违约金计算任务，计算日期：{}", calculationDate);
                
                // 1. 从合同中心获取合同列表
                allContracts = getAllActiveContracts();
                
                if (allContracts.isEmpty()) {
                    log.info("未查询到有效的商业合同，任务结束");
                    
                    // 创建全局任务状态记录，标记无合同情况
                    try {
                        createGlobalTaskStatus(calculationDate, "2", 0, "未查询到有效的商业合同"); // 状态2-已完成
                        log.info("已创建全局任务状态记录，标记无合同情况");
                    } catch (Exception e) {
                        log.error("创建全局任务状态记录失败：{}", e.getMessage(), e);
                    }
                    
                    // 插入任务主日志记录，便于后续排查问题
                    try {
                        BbpmPenaltyApiCallLogVo emptyContractLog = new BbpmPenaltyApiCallLogVo();
                        emptyContractLog.setTaskDate(calculationDate);
                        emptyContractLog.setApiType(PenaltyApiType.CONTRACT_QUERY.getCode()); // 合同中心接口调用
                        emptyContractLog.setApiUrl("PENALTY_TASK_NO_CONTRACTS");
                        emptyContractLog.setRequestParams("{\"calculationDate\":\"" + calculationDate + "\",\"businessTypeCode1\":\"02\",\"contractStatus\":\"1\"}");
                        emptyContractLog.setResponseData("{\"message\":\"未查询到有效的商业合同\",\"totalContracts\":0,\"validContracts\":0,\"reason\":\"所有合同每日逾期率为空或为0\"}");
                        emptyContractLog.setCallDuration(0);
                        emptyContractLog.setCallStatus("1");
                        emptyContractLog.setCallTime(new Date());
                        emptyContractLog.setDelFlag(1);
                        
                        penaltyApiCallLogService.insertRecord(emptyContractLog);
                        log.info("已记录空合同列表情况到日志主表，便于后续排查");
                    } catch (Exception e) {
                        log.error("记录空合同列表日志失败：{}", e.getMessage(), e);
                    }
                    
                    // 标记特殊完成状态（无合同也算成功完成）
                    mainTaskCompleted = true;
                    
                    // 设置返回结果描述
                    dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
                    dto.setExecuteDesc("未查询到有效的商业合同，任务正常结束");
                    return dto;
                }
                
                log.info("查询到 {} 个有效合同", allContracts.size());
                
                // 2. 初始化任务状态表（首次执行时创建所有待处理任务记录）
                List<String> contractNos = allContracts.stream()
                    .map(ContractInfoVo::getContractNo)
                    .collect(Collectors.toList());
                initializeTaskStatus(contractNos, calculationDate);
                
                // 3. 过滤出未处理的合同（断点续传）
                unprocessedContracts = filterUnprocessedContracts(allContracts, calculationDate);
                
                log.info("总合同数：{}，待处理合同数：{}", allContracts.size(), unprocessedContracts.size());
                
                // 4. 处理每个未处理的合同
                for (ContractInfoVo contractInfo : unprocessedContracts) {
                    processContract(contractInfo, calculationDate);
                }
                

                
                log.info("违约金计算任务执行完成，处理合同数：{}", unprocessedContracts.size());
                
                // 标记主任务成功完成
                mainTaskCompleted = true;
                reuslt = "成功";

            } catch (Exception e) {
                e.printStackTrace();
                log.error("罚费试算任务执行失败：{}", e.getMessage(), e);
                
                // 异常后更新所有处理中的任务状态为失败
                try {
                    LocalDate currentDate = LocalDate.now(); // 使用当前日期作为fallback
                    updateAllProcessingTasksToFailed(currentDate, e.getMessage());
                    reuslt = "任务执行失败：" + e.getMessage();
                } catch (Exception updateException) {
                    log.error("更新失败状态时发生异常：{}", updateException.getMessage(), updateException);
                    reuslt = "任务执行失败且状态更新失败";
                }
                
            } finally {
                // 只有在主任务真正成功完成时，才创建全局任务统计
                if (mainTaskCompleted && calculationDate != null && allContracts != null && unprocessedContracts != null) {
                    try {
                        log.info("主任务成功完成，开始创建全局任务统计");
                        createTaskCompletionSummary(calculationDate, allContracts.size(), unprocessedContracts.size());
                    } catch (Exception e) {
                        log.error("创建任务完成统计失败：{}", e.getMessage(), e);
                        // 不影响主任务的成功状态，只记录错误
                    }
                } else if (calculationDate != null) {
                    log.info("主任务未成功完成，跳过创建全局任务统计。mainTaskCompleted={}, allContracts={}, unprocessedContracts={}", 
                        mainTaskCompleted, 
                        (allContracts != null ? allContracts.size() : "null"),
                        (unprocessedContracts != null ? unprocessedContracts.size() : "null"));
                }
                
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        } else {
            log.info("商业违约金计算任务已在运行中...");
            reuslt = "商业违约金计算任务已在运行中...";
        }
        log.info("----商业违约金计算任务结束执行----");

        dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
        dto.setExecuteDesc(reuslt);
        return dto;
    }


    /**
     * 获取所有有效的商业合同（包含projectId和每日逾期率）
     */
    private List<ContractInfoVo> getAllActiveContracts() {
        List<ContractInfoVo> contractList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        String requestParamsJson = "";
        String responseBody = "";
        
        try {
            BbctContractManagementPageVo queryVo = new BbctContractManagementPageVo();
            queryVo.setIsProject("1"); // 不卡数据权限
            queryVo.setBusinessTypeCode1("02"); // 商业合同
            queryVo.setContractStatus("1"); // 已生效
            queryVo.setPageSize(1000);
            queryVo.setPageNumber(1);
            queryVo.setFindAll(true);
            
            
            requestParamsJson = JSONObject.toJSONString(queryVo);
            log.info("开始调用合同中心接口获取有效商业合同，请求参数：{}", requestParamsJson);
            
            AppReply<PageResult<List<BbctContractManagementPageResultVo>>> contractAppReply = 
                contractFeignClient.selectByPageCommon(queryVo);
            
            responseBody = JSONObject.toJSONString(contractAppReply);
            log.debug("合同中心接口响应：{}", responseBody);
            
            if (contractAppReply != null && contractAppReply.getData() != null 
                && contractAppReply.getData().getRows() != null) {
                
                List<BbctContractManagementPageResultVo> allContracts = contractAppReply.getData().getRows();
                log.info("合同中心接口返回合同总数：{}，开始筛选有每日逾期率的合同", allContracts.size());
                
                int validContractCount = 0;
                int invalidContractCount = 0;
                
                for (BbctContractManagementPageResultVo contract : allContracts) {
                    try {
                        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(contract.getContractExtend());
                        
                        // 只处理每日逾期率不为0的合同
                        if (contractOtherInfo != null && contractOtherInfo.getDailyOverdueRate() != null 
                            && contractOtherInfo.getDailyOverdueRate().compareTo(BigDecimal.ZERO) != 0) {
                            
                            // 获取projectId
                            String projectId = null;
                            if (contract.getSubjectMatterVos() != null && !contract.getSubjectMatterVos().isEmpty()) {
                                projectId = contract.getSubjectMatterVos().get(0).getProjectId();
                            }
                            
                            contractList.add(new ContractInfoVo(
                                contract.getContractNo(), 
                                projectId, 
                                contractOtherInfo.getDailyOverdueRate()
                            ));
                            validContractCount++;
                            
                            log.debug("添加有效合同：合同号={}，项目ID={}，每日逾期率={}", 
                                    contract.getContractNo(), projectId, contractOtherInfo.getDailyOverdueRate());
                        } else {
                            invalidContractCount++;
                            log.debug("跳过无效合同：合同号={}，原因：每日逾期率为空或为0", contract.getContractNo());
                        }
                    } catch (Exception e) {
                        log.error("处理合同 {} 时发生异常：{}", contract.getContractNo(), e.getMessage(), e);
                        invalidContractCount++;
                    }
                }
                
                log.info("合同筛选完成，有效合同数：{}，无效合同数：{}，耗时：{}ms", 
                        validContractCount, invalidContractCount, System.currentTimeMillis() - startTime);
                
            } else {
                log.warn("合同中心接口返回数据为空：contractAppReply={}", contractAppReply);
            }
            
            // 记录成功的API调用日志
            logApiCall(null, LocalDate.now(), null, null, PenaltyApiType.CONTRACT_QUERY.getCode(), 
                "/contract/selectByPageCommon", requestParamsJson, 
                responseBody.length() > 2000 ? responseBody.substring(0, 2000) + "..." : responseBody, 
                System.currentTimeMillis() - startTime, null);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            
            log.error("获取有效合同列表失败，耗时：{}ms，错误：{}", duration, errorMessage, e);
            
            // 记录失败的API调用日志
            logApiCall(null, LocalDate.now(), null, null, PenaltyApiType.CONTRACT_QUERY.getCode(),
                "/contract/selectByPageCommon", requestParamsJson, responseBody,
                duration, errorMessage);
        }
        
        return contractList;
    }
    
    /**
     * 初始化任务状态表（首次执行时创建所有待处理任务记录）- 使用幂等性安全初始化
     */
    private void initializeTaskStatus(List<String> allContracts, LocalDate taskDate) {
        try {
            log.info("开始检查并初始化任务状态表，任务日期：{}，合同数量：{}", taskDate, allContracts.size());
            
            // 查询已存在的任务状态记录
            List<BbpmPenaltyTaskStatusVo> existingTaskStatus = queryTaskStatusList(taskDate, null, null);
            Set<String> existingContracts = existingTaskStatus.stream()
                .map(BbpmPenaltyTaskStatusVo::getContractCode)
                .collect(Collectors.toSet());
            
            // 找出缺失的合同记录
            List<String> missingContracts = allContracts.stream()
                .filter(contractNo -> !existingContracts.contains(contractNo))
                .collect(Collectors.toList());
            
            if (!missingContracts.isEmpty()) {
                log.info("发现 {} 个合同缺少任务状态记录，开始补充创建", missingContracts.size());
                
                int successCount = 0;
                int failCount = 0;
                
                for (String contractNo : missingContracts) {
                    try {
                        // 使用幂等性插入：先查询再插入
                        List<BbpmPenaltyTaskStatusVo> existingRecord = queryTaskStatusList(taskDate, contractNo, null);
                        if (existingRecord.isEmpty()) {
                            BbpmPenaltyTaskStatusVo taskStatus = new BbpmPenaltyTaskStatusVo();
                            taskStatus.setTaskDate(taskDate);
                            taskStatus.setContractCode(contractNo);
                            taskStatus.setProcessStatus("0"); // 0-待处理
                            taskStatus.setBillCount(0);
                            taskStatus.setDelFlag(1);
                            
                            penaltyTaskStatusService.insertRecord(taskStatus);
                            successCount++;
                            log.debug("成功创建合同 {} 的任务状态记录", contractNo);
                        } else {
                            log.debug("合同 {} 的任务状态记录已存在，跳过创建", contractNo);
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("创建合同 {} 的任务状态记录失败：{}", contractNo, e.getMessage(), e);
                        
                        // 如果是唯一键冲突，尝试查询确认记录是否真实存在
                        if (e.getMessage() != null && e.getMessage().contains("Duplicate")) {
                            try {
                                List<BbpmPenaltyTaskStatusVo> checkRecord = queryTaskStatusList(taskDate, contractNo, null);
                                if (!checkRecord.isEmpty()) {
                                    log.info("合同 {} 的任务状态记录实际已存在，忽略重复插入错误", contractNo);
                                    failCount--; // 不计入失败
                                }
                            } catch (Exception checkEx) {
                                log.error("检查重复记录时发生异常：{}", checkEx.getMessage());
                            }
                        }
                    }
                }
                
                log.info("任务状态表初始化完成 - 成功创建：{}，失败：{}，总合同数：{}", 
                    successCount, failCount, allContracts.size());
                    
                // 如果失败数量过多，记录警告
                if (failCount > 0 && failCount > allContracts.size() * 0.1) {
                    log.warn("任务状态初始化失败率较高：{}/{}, 请检查数据库连接和唯一键约束", failCount, allContracts.size());
                }
            } else {
                log.info("所有合同的任务状态记录都已存在，无需初始化");
            }
            
        } catch (Exception e) {
            log.error("初始化任务状态表失败：{}", e.getMessage(), e);
            throw new RuntimeException("任务状态表初始化失败，停止任务执行", e);
        }
    }
    
    /**
     * 断点续传过滤方法 - 过滤出未处理的合同
     */
    private List<ContractInfoVo> filterUnprocessedContracts(List<ContractInfoVo> allContracts, LocalDate taskDate) {
        try {
            List<ContractInfoVo> unprocessedContracts = new ArrayList<>();
            
            // 查询未处理的合同（状态为0-待处理或1-处理中）
            for (ContractInfoVo contractInfo : allContracts) {
                String contractNo = contractInfo.getContractNo();
                String processStatus = getContractProcessStatus(contractNo, taskDate);
                
                // 如果是待处理(0)或处理中(1)，则需要处理
                if ("0".equals(processStatus) || "1".equals(processStatus)) {
                    unprocessedContracts.add(contractInfo);
                } else if ("2".equals(processStatus)) {
                    log.debug("合同 {} 今天已完成处理，跳过", contractNo);
                } else if ("3".equals(processStatus)) {
                    log.debug("合同 {} 今天处理失败，重新加入处理队列", contractNo);
                    unprocessedContracts.add(contractInfo);
                } else {
                    // 没有记录的合同也需要处理
                    unprocessedContracts.add(contractInfo);
                }
            }
            
            log.info("筛选出需要处理的合同数量：{}/{}", unprocessedContracts.size(), allContracts.size());
            return unprocessedContracts;
                
        } catch (Exception e) {
            log.error("查询未处理合同列表失败：{}", e.getMessage(), e);
            // 出错时返回所有合同，确保任务能够继续
            return allContracts;
        }
    }
    
    /**
     * 合同处理方法（批量对比版本）
     */
    private void processContract(ContractInfoVo contractInfo, LocalDate calculationDate) {
        String contractNo = contractInfo.getContractNo();
        String projectId = contractInfo.getProjectId();
        String taskStatusId = null;
        try {
            // 更新任务状态为处理中并获取任务状态ID
            taskStatusId = updateTaskStatusToProcessing(contractNo, calculationDate);
            log.info("开始处理合同：{}, 项目ID：{}", contractNo, projectId);
            
            // 1. 查询两种类型的账单（01-企业，02-个人），并合并结果
            List<BbpmBillManagementPageResultVo> allOverdueBills = new ArrayList<>();
            
            // 1.1 查询企业账单（chargeOwner=01）
            PageResult<List<BbpmBillManagementPageResultVo>> corporateBillsResult = 
                queryBillsByContractAndType(taskStatusId, contractNo, projectId, "01", calculationDate);
            if (corporateBillsResult != null && corporateBillsResult.getRows() != null) {
                allOverdueBills.addAll(corporateBillsResult.getRows());
                log.debug("合同 {} 查询到 {} 个企业账单", contractNo, corporateBillsResult.getRows().size());
            }
            
            // 1.2 查询个人账单（chargeOwner=02）
            PageResult<List<BbpmBillManagementPageResultVo>> personalBillsResult = 
                queryBillsByContractAndType(taskStatusId, contractNo, projectId, "02", calculationDate);
            if (personalBillsResult != null && personalBillsResult.getRows() != null) {
                allOverdueBills.addAll(personalBillsResult.getRows());
                log.debug("合同 {} 查询到 {} 个个人账单", contractNo, personalBillsResult.getRows().size());
            }
            
            if (allOverdueBills.isEmpty()) {
                log.info("合同 {} 没有逾期账单，跳过处理", contractNo);
                updateTaskStatusToCompleted(taskStatusId, contractNo, 0, calculationDate);
                return;
            }
            
            log.info("合同 {} 查询到 {} 个逾期账单（企业+个人）", contractNo, allOverdueBills.size());
            
            // 2. 批量查询所有账单的收款单信息（优化：每个账单只查询一次）
            Map<String, List<BbpmCollectionPageResultVo>> billPaymentsMap = batchQueryPaymentsForBills(
                taskStatusId, allOverdueBills, calculationDate, contractNo, projectId);
            log.info("合同 {} 批量查询了 {} 个账单的收款单信息", contractNo, billPaymentsMap.size());
            
            // 3. 查询库中该合同已有的试算账单记录（所有状态）
            Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap = new HashMap<>();
            try {
                List<BbpmPenaltyFeeTrialBillVo> trialBills = queryTrialBillList(contractNo, null);
                for (BbpmPenaltyFeeTrialBillVo trialBill : trialBills) {
                    if (trialBill != null) {
                        existingTrialBillMap.put(trialBill.getBillId(), trialBill);
                    }
                }
                
                log.info("合同 {} 库中已有 {} 个试算账单记录（包含所有状态）", contractNo, existingTrialBillMap.size());
                
            } catch (Exception e) {
                log.error("查询合同 {} 已有试算账单失败：{}", contractNo, e.getMessage(), e);
            }
            
            // 4. 分类处理账单：新账单 vs 已存在账单
            BillComparisonResultVo comparisonResult = compareBillsWithDatabase(allOverdueBills, existingTrialBillMap, billPaymentsMap, calculationDate, contractInfo.getDailyOverdueRate(), projectId);
            
            // 统计优化效果：计算被过滤的已缴足额支付账单数量
            long totalBills = allOverdueBills.size();
            long processableBills = comparisonResult.getNewBills().size() + 
                                   comparisonResult.getStatusChangedBills().size() + 
                                   comparisonResult.getUnchangedBills().size();
            long skippedPaidBills = totalBills - processableBills;
            
            log.info("合同 {} 账单分类结果：新增 {} 个，状态变化 {} 个，无变化 {} 个", 
                    contractNo, 
                    comparisonResult.getNewBills().size(),
                    comparisonResult.getStatusChangedBills().size(),
                    comparisonResult.getUnchangedBills().size());
            
            if (skippedPaidBills > 0) {
                log.info("合同 {} 优化效果：跳过了 {} 个已缴足额支付账单（占比 {:.1f}%），减少了不必要的处理", 
                        contractNo, skippedPaidBills, (skippedPaidBills * 100.0 / totalBills));
            }
            
            int processedCount = 0;
            
            // 5. 处理新增账单
            for (BbpmBillManagementPageResultVo newBill : comparisonResult.getNewBills()) {
                try {
                    log.info("处理新增账单：{}", newBill.getBillId());
                    List<BbpmCollectionPageResultVo> payments = billPaymentsMap.getOrDefault(newBill.getBillId(), new ArrayList<>());
                    processNewBill(newBill, payments, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理新增账单 {} 失败：{}", newBill.getBillId(), e.getMessage(), e);
                }
            }
            
            // 6. 处理状态变化的账单
            for (BillStatusComparisonVo statusComparison : comparisonResult.getStatusChangedBills()) {
                try {
                    log.info("处理状态变化账单：{} ({}->{})", 
                            statusComparison.getBill().getBillId(),
                            statusComparison.getOldStatus(),
                            statusComparison.getNewStatus());
                    processStatusChangedBill(statusComparison, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理状态变化账单 {} 失败：{}", statusComparison.getBill().getBillId(), e.getMessage(), e);
                }
            }
            
            // 7. 处理无变化账单（正常的每日计算）
            for (BbpmBillManagementPageResultVo unchangedBill : comparisonResult.getUnchangedBills()) {
                try {
                    // 只进行正常的今日违约金计算（如果今天还没算过）
                    if (!isAlreadyProcessedToday(unchangedBill.getBillId(), calculationDate)) {
                        List<BbpmCollectionPageResultVo> payments = billPaymentsMap.getOrDefault(unchangedBill.getBillId(), new ArrayList<>());
                        calculateDailyPenalty(unchangedBill, payments, calculationDate, taskStatusId, contractInfo.getDailyOverdueRate(), projectId);
                        processedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理无变化账单 {} 失败：{}", unchangedBill.getBillId(), e.getMessage(), e);
                }
            }
            
            // 8. 检查该合同的试算账单是否需要自动完结
            int finalizedCount = 0;
            for (BbpmPenaltyFeeTrialBillVo trialBill : existingTrialBillMap.values()) {
                if ("1".equals(trialBill.getStatus())) { // 只检查试算中的账单
                    try {
                        if (checkAndAutoFinalize(trialBill.getBillId())) {
                            finalizedCount++;
                        }
                    } catch (Exception e) {
                        log.error("检查账单 {} 自动完结失败：{}", trialBill.getBillId(), e.getMessage(), e);
                    }
                }
            }
            if (finalizedCount > 0) {
                log.info("合同 {} 自动完结了 {} 个试算账单", contractNo, finalizedCount);
            }
            
            // 8. 更新任务状态为已完成
            updateTaskStatusToCompleted(taskStatusId, contractNo, allOverdueBills.size(), calculationDate);
            log.info("合同 {} 处理完成，总账单数：{}，实际处理：{}", contractNo, allOverdueBills.size(), processedCount);
            
        } catch (Exception e) {
            // 更新任务状态为失败
            updateTaskStatusToFailed(taskStatusId, contractNo, e.getMessage(), calculationDate);
            log.error("处理合同 {} 失败：{}", contractNo, e.getMessage(), e);
        }
    }
    
    /**
     * 查询任务状态记录（不使用分页框架）
     */
    private List<BbpmPenaltyTaskStatusVo> queryTaskStatusList(LocalDate taskDate, String contractCode, String processStatus) {
        try {
            BbpmPenaltyTaskStatusVo queryVo = new BbpmPenaltyTaskStatusVo();
            if (taskDate != null) {
                queryVo.setTaskDate(taskDate);
            }
            if (StringUtils.isNotBlank(contractCode)) {
                queryVo.setContractCode(contractCode);
            }
            if (StringUtils.isNotBlank(processStatus)) {
                queryVo.setProcessStatus(processStatus);
            }
            
            return penaltyTaskStatusService.selectListByCondition(queryVo);
        } catch (Exception e) {
            log.error("查询任务状态记录失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询试算账单记录（不使用分页框架）
     */
    private List<BbpmPenaltyFeeTrialBillVo> queryTrialBillList(String contractCode, String billId) {
        try {
            BbpmPenaltyFeeTrialBillVo queryVo = new BbpmPenaltyFeeTrialBillVo();
            if (StringUtils.isNotBlank(contractCode)) {
                queryVo.setContractCode(contractCode);
            }
            if (StringUtils.isNotBlank(billId)) {
                queryVo.setBillId(billId);
            }
            
            return penaltyFeeTrialBillService.selectListByCondition(queryVo);
        } catch (Exception e) {
            log.error("查询试算账单记录失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    

    
    /**
     * 计算当日违约金（优化版：使用预先查询的收款单信息）
     */
    private void calculateDailyPenalty(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments, LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 1. 使用预先查询的收款单信息（优化：避免重复查询）
            
            // 2. 分析收款状态和计算违约金基数
            BigDecimal shouldPayAmount = parseBigDecimal(bill.getShouldPayAmount());
            BigDecimal totalPaidAmount = calculateTotalPaidAmount(payments);
            BigDecimal penaltyBaseAmount = shouldPayAmount.subtract(totalPaidAmount).max(BigDecimal.ZERO);
            String reconciliationStatus = analyzeReconciliationStatus(payments);
            
            // 3. 判断是否应该计算违约金
            if (!shouldCalculatePenalty(bill, calculationDate, reconciliationStatus, penaltyBaseAmount)) {
                log.debug("账单 {} 在日期 {} 无需计算违约金，对账状态：{}，未缴金额：{}", 
                        bill.getBillId(), calculationDate, reconciliationStatus, penaltyBaseAmount);
                return;
            }
            
            // 4. 确保试算账单记录存在（首次计算时创建）
            LocalDate earliestReceiptDate = getEarliestBankReceiptDate(payments, calculationDate);
            ensureTrialBillExists(bill, payments, reconciliationStatus, earliestReceiptDate, dailyOverdueRate);
            
            // 5. 计算当日违约金（使用传入的每日逾期率，需要除以100转换为百分比）
            BigDecimal actualRate = dailyOverdueRate.divide(new BigDecimal("100"), 6, RoundingMode.HALF_UP);
            BigDecimal dailyPenaltyAmount = penaltyBaseAmount.multiply(actualRate).setScale(2, RoundingMode.HALF_UP);
            
            // 6. 构建每日明细记录（注意：这里存储的是原始逾期率，计算时已转换）
            BbpmPenaltyFeeDailyDetailVo dailyRecord = buildDailyRecord(bill, calculationDate,
                                                                      penaltyBaseAmount, dailyOverdueRate,
                                                                      dailyPenaltyAmount, reconciliationStatus, payments);
            
            // 7. 保存到数据库
            penaltyFeeDailyDetailService.insertRecord(dailyRecord);
            
            // 8. 更新试算账单汇总信息
            updateTrialBillSummary(bill.getBillId(), calculationDate);
            
            // 9. 生成分阶段汇总数据（包含验证）
            generateStageSummaryWithValidation(bill.getBillId());
            
            // 10. 检查是否需要自动完结账单
            checkAndAutoFinalize(bill.getBillId());
            
            log.info("账单 {} 当日违约金计算完成，违约金基数：{}，违约金：{}，对账状态：{}，已缴金额：{}",
                     bill.getBillId(), penaltyBaseAmount, dailyPenaltyAmount, reconciliationStatus, totalPaidAmount);
            
        } catch (Exception e) {
            log.error("计算账单 {} 违约金失败：{}", bill.getBillId(), e.getMessage(), e);
            throw new RuntimeException("违约金计算失败", e);
        }
    }
  
    /**
     * 批量查询账单的收款单信息（优化：每个账单只查询一次）
     */
    private Map<String, List<BbpmCollectionPageResultVo>> batchQueryPaymentsForBills(
            String taskStatusId, 
            List<BbpmBillManagementPageResultVo> bills, 
            LocalDate calculationDate, 
            String contractNo, 
            String projectId) {
        
        Map<String, List<BbpmCollectionPageResultVo>> paymentsMap = new HashMap<>();
        
        for (BbpmBillManagementPageResultVo bill : bills) {
            // 优化策略：对于已缴足额支付的账单，需要区分情况
            // 1. 新增账单且已缴足额支付 → 跳过收款单查询
            // 2. 可能存在状态变化的账单 → 仍需查询收款单
            // 由于无法提前知道状态变化情况，采用保守策略：只对明确不需要处理的新增账单跳过查询
            
            try {
                PageResult<List<BbpmCollectionPageResultVo>> paymentsResult = 
                    queryPaymentsByBillId(taskStatusId, bill.getBillId(), calculationDate, bill.getContractNo(), projectId);
                
                List<BbpmCollectionPageResultVo> payments = 
                    (paymentsResult != null && paymentsResult.getRows() != null) ? 
                    paymentsResult.getRows() : new ArrayList<>();
                
                paymentsMap.put(bill.getBillId(), payments);
                
                log.debug("账单 {} 查询到 {} 个收款单", bill.getBillId(), payments.size());
                
            } catch (Exception e) {
                log.error("查询账单 {} 收款单信息失败：{}", bill.getBillId(), e.getMessage(), e);
                // 失败时存储空列表，避免后续处理时出现null
                paymentsMap.put(bill.getBillId(), new ArrayList<>());
            }
        }
        
        log.info("批量查询完成：合同 {}，共 {} 个账单，成功 {} 个", 
                contractNo, bills.size(), paymentsMap.size());
        
        return paymentsMap;
    }
 
    
    /**
     * 查询合同指定类型的逾期账单（支持projectId和chargeOwner）
     */
    private PageResult<List<BbpmBillManagementPageResultVo>> queryBillsByContractAndType(String taskStatusId, 
            String contractNo, String projectId, String chargeOwner, LocalDate calculationDate) {
        long startTime = System.currentTimeMillis();
        String requestParamsJson = "";
        String responseBody = "";
        
        try {
            // 构建查询参数
            BbpmBillManagementPageVo queryVo = new BbpmBillManagementPageVo();
            queryVo.setContractCode(contractNo);
            // 设置projectId（新增）
            if (StringUtils.isNotBlank(projectId)) {
                queryVo.setProjectId(projectId);
            }
            // 设置账单类型（新增）
            queryVo.setChargeOwner(chargeOwner);
            // 设置payableEndDate为昨天日期（新增）
            LocalDate yesterday = calculationDate.minusDays(1);
            queryVo.setPayableEndDate(yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 不设置账单状态过滤，查询所有状态的账单
            queryVo.setFullPage("true");
            queryVo.setPageSize(100);
            queryVo.setPageNo(1);
            queryVo.setPrimaryChargeCodeFlag("1");
            
            // 构建请求对象
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<BbpmBillManagementPageVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(queryVo);
            
            requestParamsJson = JSONObject.toJSONString(parentRequest);
            log.info("调用工银账单接口请求参数（合同：{}，项目：{}，类型：{}，截止：{}）：{}", 
                    contractNo, projectId, chargeOwner, yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd")), requestParamsJson);
            
            // 调用工银账单查询接口
            responseBody = bfipChargeFeignClient.listByContract(parentRequest);
            log.debug("调用工银账单接口返回：{}", responseBody);
            
            // 解析响应结果
            PageResult<List<BbpmBillManagementPageResultVo>> pageResult = resetBillData(responseBody);
            
            // 注释：不需要再次按应缴费日期过滤账单，因为查询时已通过payableEndDate参数限制
            
            // 记录成功调用日志
            logApiCall(taskStatusId, calculationDate, contractNo, null, PenaltyApiType.BILL_QUERY.getCode(), 
                "/charge/v1/bill/listByContract", requestParamsJson, 
                responseBody, System.currentTimeMillis() - startTime, null);
            
            int resultCount = (pageResult != null && pageResult.getRows() != null) ? pageResult.getRows().size() : 0;
            log.info("调用工银账单接口成功，合同：{}，项目：{}，类型：{}，返回逾期账单数：{}，耗时：{}ms", 
                    contractNo, projectId, chargeOwner, resultCount, System.currentTimeMillis() - startTime);
            
            return pageResult;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            
            // 记录失败调用日志
            logApiCall(taskStatusId, calculationDate, contractNo, null, PenaltyApiType.BILL_QUERY.getCode(),
                "/charge/v1/bill/listByContract", requestParamsJson, responseBody,
                duration, errorMessage);
            
            log.error("调用工银账单接口失败，合同：{}，项目：{}，类型：{}，错误：{}，耗时：{}ms", 
                    contractNo, projectId, chargeOwner, errorMessage, duration);
            
            return new PageResult<>(new ArrayList<>());
        }
    }
    
    /**
     * 解析工银账单接口响应（参考BbpmBillManagementServiceImpl.resetBill）
     */
    private PageResult<List<BbpmBillManagementPageResultVo>> resetBillData(String responseBody) {
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmBillManagementPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            log.warn("工银账单接口返回错误：code={}, message={}", billListResultFaceHttpResultTwo.getCode(), billListResultFaceHttpResultTwo.getMessage());
            return new PageResult<>(new ArrayList<>());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(new ArrayList<>());
        }
        
        List<BbpmBillManagementPageResultVo> billListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(), BbpmBillManagementPageResultVo.class);
        List<BbpmBillManagementPageResultVo> result = new ArrayList<>();
        for(int i=0;i<billListResultList.size();i++){
            BbpmBillManagementPageResultVo bill = billListResultList.get(i);
             bill.setBillNo(bill.getBillId());
            bill.setContractNo(bill.getContractCode());
            //账单周期相关  第X期（YYYY/MM/DD - YYYY/MM/DD）
            bill.setBillCycle("第"+bill.getChargeSubjectPeriod()+"期("+bill.getChargeSubjectBeginDate()+"至"+bill.getChargeSubjectEndDate()+")");
            result.add(bill);
        }
        
        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(), result);
    }
    
    /**
     * 检查是否已处理今日计算
     */
    private boolean isAlreadyProcessedToday(String billId, LocalDate calculationDate) {
        try {
            // 查询今天是否有该账单的正常计算记录
            List<BbpmPenaltyFeeDailyDetailVo> allDetails = selectDailyDetailsByBillId(billId);
            
            // 筛选今天的正常计算记录
            boolean hasRecordToday = allDetails.stream()
                .anyMatch(detail -> "1".equals(detail.getEntryType()) && 
                         calculationDate.equals(detail.getCalculationDate()));
                         
            return hasRecordToday;
        } catch (Exception e) {
            log.error("查询账单处理记录失败：{}", e.getMessage(), e);
            return false;
        }
    }
    

    

    
    private void logApiCall(String taskStatusId, LocalDate taskDate, String contractCode, 
                           String billId, String apiType, String apiUrl, 
                           String requestParams, String responseData, long duration, String errorMessage) {
        try {
            BbpmPenaltyApiCallLogVo logVo = new BbpmPenaltyApiCallLogVo();
            logVo.setTaskStatusId(taskStatusId);
            logVo.setTaskDate(taskDate);
            logVo.setContractCode(contractCode);
            logVo.setBillId(billId);
            logVo.setApiType(apiType);
            logVo.setApiUrl(apiUrl);
            logVo.setRequestParams(requestParams);
            logVo.setResponseData(responseData);
            logVo.setCallDuration((int) duration);
            logVo.setCallStatus(StringUtils.isBlank(errorMessage) ? "1" : "2");
            logVo.setErrorMessage(errorMessage);
            logVo.setCallTime(new Date());
            logVo.setDelFlag(1);
            
            penaltyApiCallLogService.insertRecord(logVo);
        } catch (Exception e) {
            log.error("记录API调用日志失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 解析日期字符串为LocalDate
     */
    private LocalDate parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            if (dateStr.length() == 10) {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else if (dateStr.length() == 8) {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            return LocalDate.parse(dateStr);
        } catch (Exception e) {
            log.warn("解析日期字符串失败：{}", dateStr);
            return null;
        }
    }
    
    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof String) {
            String str = (String) value;
            if (StringUtils.isBlank(str)) {
                return BigDecimal.ZERO;
            }
            try {
                return new BigDecimal(str);
            } catch (NumberFormatException e) {
                log.warn("解析BigDecimal失败：{}", str);
                return BigDecimal.ZERO;
            }
        }
        return new BigDecimal(value.toString());
    }
    

    

    
    /**
     * 对比工银账单与库中试算账单，找出新增、变化、无变化的账单
     */
    private BillComparisonResultVo compareBillsWithDatabase(
            List<BbpmBillManagementPageResultVo> allBills, 
            Map<String, BbpmPenaltyFeeTrialBillVo> existingTrialBillMap,
            Map<String, List<BbpmCollectionPageResultVo>> billPaymentsMap,
            LocalDate calculationDate,
            BigDecimal dailyOverdueRate,
            String projectId) {
        
        List<BbpmBillManagementPageResultVo> newBills = new ArrayList<>();
        List<BillStatusComparisonVo> statusChangedBills = new ArrayList<>();
        List<BbpmBillManagementPageResultVo> unchangedBills = new ArrayList<>();
        
        for (BbpmBillManagementPageResultVo bill : allBills) {
            String billId = bill.getBillId();
            BbpmPenaltyFeeTrialBillVo existingTrialBill = existingTrialBillMap.get(billId);
            
            if (existingTrialBill == null) {
                // 新增账单（库中完全没有记录）
                // 优化：首次执行时，已缴足额支付的账单无需统计
                if ("01".equals(bill.getBillStatus())) {
                    log.debug("新增账单 {} 已缴足额支付（billStatus=01），跳过违约金计算", billId);
                } else {
                    newBills.add(bill);
                    log.debug("发现新增账单：{}", billId);
                }
            } else {
                // 库中已存在记录，根据试算账单状态判断处理方式
                String trialBillStatus = existingTrialBill.getStatus();
                
                if (!"1".equals(trialBillStatus)) {
                    // 试算账单已完结（状态2）或已取消（状态3），但账单重新出现
                    // 根据业务需求，可能需要重新激活或当作新账单处理
                    if ("2".equals(trialBillStatus)) {
                        // 优化：已完结的账单重新出现，但如果已缴足额支付，则无需重新处理
                        if ("01".equals(bill.getBillStatus())) {
                            log.debug("已完结账单 {} 重新出现但已缴足额支付（billStatus=01），跳过处理", billId);
                        } else {
                            log.info("账单 {} 对应的试算账单已完结，但账单重新出现，当作新账单处理", billId);
                            newBills.add(bill);
                        }
                    } else {
                        // 处理未定义的试算账单状态（如状态3等）
                        // 根据业务设计文档，试算账单只有两个状态：1-试算中，2-已完结
                        // 对于未定义的状态，记录警告并按异常状态处理
                        log.warn("账单 {} 对应的试算账单状态异常：{}，当作无变化账单处理", billId, trialBillStatus);
                        // 状态异常的账单，如果已缴足额支付，也跳过处理
                        if (!"01".equals(bill.getBillStatus())) {
                            unchangedBills.add(bill);
                        }
                    }
                } else {
                    // 试算账单正在试算中（状态1），检查对账状态是否变化
                    try {
                        // 使用预先查询的收款单信息（优化：避免重复查询）
                        List<BbpmCollectionPageResultVo> currentPayments = 
                            billPaymentsMap.getOrDefault(billId, new ArrayList<>());
                        
                        String currentStatus = analyzeReconciliationStatus(currentPayments);
                        String savedStatus = existingTrialBill.getAccountStatus();
                        
                        // 检测账单缴费状态变化
                        String currentBillStatus = bill.getBillStatus();
                        String savedBillStatus = existingTrialBill.getBillStatus();
                        
                        boolean accountStatusChanged = !Objects.equals(currentStatus, savedStatus);
                        boolean billStatusChanged = !Objects.equals(currentBillStatus, savedBillStatus);
                        
                        if (accountStatusChanged || billStatusChanged) {
                            // 对账状态或账单缴费状态发生变化
                            statusChangedBills.add(new BillStatusComparisonVo(
                                bill, existingTrialBill, savedStatus, currentStatus, currentPayments
                            ));
                            if (accountStatusChanged && billStatusChanged) {
                                log.debug("发现状态变化账单：{} 对账状态({}->{}), 缴费状态({}->{})", 
                                    billId, savedStatus, currentStatus, savedBillStatus, currentBillStatus);
                            } else if (accountStatusChanged) {
                                log.debug("发现状态变化账单：{} 对账状态({}->{})", billId, savedStatus, currentStatus);
                            } else {
                                log.debug("发现状态变化账单：{} 缴费状态({}->{})", billId, savedBillStatus, currentBillStatus);
                            }
                        } else {
                            // 对账状态无变化
                            // 优化：如果账单已缴足额支付，无需进行每日计算
                            if ("01".equals(bill.getBillStatus())) {
                                log.debug("无变化账单 {} 已缴足额支付（billStatus=01），跳过每日计算", billId);
                            } else {
                                unchangedBills.add(bill);
                                log.debug("账单状态无变化：{} ({})", billId, currentStatus);
                            }
                        }
                    } catch (Exception e) {
                        log.error("检查账单 {} 状态失败：{}", billId, e.getMessage(), e);
                        // 出错时当作无变化账单处理
                        unchangedBills.add(bill);
                    }
                }
            }
        }
        
        return new BillComparisonResultVo(newBills, statusChangedBills, unchangedBills);
    }
    
    /**
     * 处理新增账单（优化版：使用预先查询的收款单信息）
     */
    private void processNewBill(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments, LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 1. 使用预先查询的收款单信息（优化：避免重复查询）
            
            // 2. 分析状态
            String reconciliationStatus = analyzeReconciliationStatus(payments);
            LocalDate earliestReceiptDate = getEarliestBankReceiptDate(payments, calculationDate);
            
            // 3. 创建试算账单记录（新账单一定需要创建）
            ensureTrialBillExists(bill, payments, reconciliationStatus, earliestReceiptDate, dailyOverdueRate);
            
            // 4. 计算违约金（从账单逾期开始计算到今天）
            calculateHistoricalPenalties(bill, payments, calculationDate, taskStatusId, dailyOverdueRate, projectId);
            
            log.info("新增账单 {} 处理完成", bill.getBillId());
            
        } catch (Exception e) {
            log.error("处理新增账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 处理状态变化的账单
     */
    private void processStatusChangedBill(BillStatusComparisonVo statusComparison, LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        BbpmBillManagementPageResultVo bill = statusComparison.getBill();
        String oldStatus = statusComparison.getOldStatus();
        String newStatus = statusComparison.getNewStatus();
        BbpmPenaltyFeeTrialBillVo existingTrialBill = statusComparison.getExistingTrialBill();
        
        try {
            // 1. 记录状态变化到API日志
            recordReconciliationStatusChange(bill.getBillId(), oldStatus, newStatus);
            
            // 2. 获取银行回单日期，用于确定调整起始日期
            LocalDate earliestReceiptDate = getEarliestBankReceiptDate(statusComparison.getCurrentPayments(), calculationDate);
            
            // 3. 触发违约金调整（红冲之前的记录）
            adjustPenalty(bill, earliestReceiptDate != null ? earliestReceiptDate : calculationDate, projectId);
            
            // 4. 更新试算账单的对账状态
            existingTrialBill.setAccountStatus(newStatus);
            penaltyFeeTrialBillService.updateByIdRecord(existingTrialBill);
            
            // 5. 今日违约金计算（正常流程）
            if (!isAlreadyProcessedToday(bill.getBillId(), calculationDate)) {
                calculateDailyPenalty(bill, statusComparison.getCurrentPayments(), calculationDate, taskStatusId, dailyOverdueRate, projectId);
            }
            
            // 6. 更新试算账单汇总信息
            updateTrialBillSummary(bill.getBillId(), calculationDate);
            
            log.info("状态变化账单 {} 处理完成", bill.getBillId());
            
        } catch (Exception e) {
            log.error("处理状态变化账单 {} 失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 计算历史违约金（用于新账单的历史补算）（优化版：使用预先查询的收款单信息）
     */
    private void calculateHistoricalPenalties(BbpmBillManagementPageResultVo bill, List<BbpmCollectionPageResultVo> payments, LocalDate calculationDate, String taskStatusId, BigDecimal dailyOverdueRate, String projectId) {
        try {
            // 计算从收费科目起始日期到今天的所有违约金
            LocalDate chargeSubjectBeginDate = parseDate(bill.getChargeSubjectBeginDate());
            LocalDate payableDate = parseDate(bill.getPayableDate());
            
            // 确定历史计算开始日期：使用收费科目起始日期和应缴费日期的较早者
            LocalDate startDate = null;
            if (chargeSubjectBeginDate != null && payableDate != null) {
                startDate = chargeSubjectBeginDate.isBefore(payableDate) ? chargeSubjectBeginDate : payableDate;
            } else if (chargeSubjectBeginDate != null) {
                startDate = chargeSubjectBeginDate;
            } else if (payableDate != null) {
                startDate = payableDate.plusDays(1); // 如果只有应缴费日期，从逾期日开始
            } else {
                log.warn("账单 {} 收费科目起始日期和缴费截止日期都为空，跳过历史违约金计算", bill.getBillId());
                return;
            }
            
            LocalDate endDate = calculationDate.minusDays(1); // 计算到昨天，今天的违约金通过当日计算处理
            
            if (startDate.isAfter(endDate)) {
                log.debug("账单 {} 没有历史欠费期间，无需计算历史违约金", bill.getBillId());
                return;
            }
            
            log.info("开始计算账单 {} 的历史违约金：{} 到 {}，逾期率：{}（将除以100转换为实际比率）", bill.getBillId(), startDate, endDate, dailyOverdueRate);
            
            // 逐日计算历史违约金
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                try {
                    // 检查当日是否已有计算记录
                    if (!isAlreadyProcessedToday(bill.getBillId(), currentDate)) {
                        calculateDailyPenalty(bill, payments, currentDate, taskStatusId, dailyOverdueRate, projectId);
                        log.debug("计算账单 {} 日期 {} 的历史违约金完成", bill.getBillId(), currentDate);
                    } else {
                        log.debug("账单 {} 日期 {} 已有计算记录，跳过", bill.getBillId(), currentDate);
                    }
                } catch (Exception e) {
                    log.error("计算账单 {} 日期 {} 的历史违约金失败：{}", bill.getBillId(), currentDate, e.getMessage(), e);
                    // 单日失败不影响其他日期的计算
                }
                
                currentDate = currentDate.plusDays(1);
            }
            
            log.info("账单 {} 历史违约金计算完成", bill.getBillId());
            
        } catch (Exception e) {
            log.error("计算账单 {} 历史违约金失败：{}", bill.getBillId(), e.getMessage(), e);
            throw e;
        }
    }
    

    

    
    /**
     * 记录对账状态变化到API调用日志
     */
    private void recordReconciliationStatusChange(String billId, String oldStatus, String newStatus) {
        try {
            BbpmPenaltyApiCallLogVo logVo = new BbpmPenaltyApiCallLogVo();
            logVo.setTaskDate(LocalDate.now());
            logVo.setBillId(billId);
            logVo.setApiType(PenaltyApiType.STATUS_CHANGE.getCode()); // 状态变化记录
            logVo.setApiUrl("RECONCILIATION_STATUS_CHANGE");
            logVo.setRequestParams(String.format("{\"oldStatus\":\"%s\"}", oldStatus));
            logVo.setResponseData(String.format("{\"newStatus\":\"%s\"}", newStatus));
            logVo.setCallDuration(0);
            logVo.setCallStatus("1");
            logVo.setCallTime(new Date());
            logVo.setDelFlag(1);
            
            penaltyApiCallLogService.insertRecord(logVo);
            
        } catch (Exception e) {
            log.error("记录对账状态变化日志失败：{}", e.getMessage(), e);
        }
    }
    

    
    /**
     * 查询账单的收款单信息（参考BbpmCollectionServiceImpl）
     */
    private PageResult<List<BbpmCollectionPageResultVo>> queryPaymentsByBillId(String taskStatusId, String billId, LocalDate taskDate, String contractNo, String projectId) {
        long startTime = System.currentTimeMillis();
        String requestParamsJson = "";
        String responseBody = "";
        
        try {
            // 构建查询参数
            BbpmCollectionPageVo queryVo = new BbpmCollectionPageVo();
            queryVo.setBillId(billId);
            queryVo.setOptType("02"); // 01:详情 02:列表，默认详情
            queryVo.setFullPage("true");
            queryVo.setSize(1000);
            queryVo.setCurrent(1);
            // 设置projectId（新增）
            if (StringUtils.isNotBlank(projectId)) {
                queryVo.setProjectId(projectId);
            }
            
            // 构建请求对象
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<BbpmCollectionPageVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(queryVo);
            
            requestParamsJson = JSONObject.toJSONString(parentRequest);
            log.debug("调用工银收款单接口请求参数：{}", requestParamsJson);
            
            // 调用工银收款单查询接口
            responseBody = bfipChargeFeignClient.listByBill(parentRequest);
            log.debug("调用工银收款单接口返回：{}", responseBody);
            
            // 解析响应结果
            PageResult<List<BbpmCollectionPageResultVo>> pageResult = resetCollectionData(responseBody);
            
            // 记录成功调用日志
            logApiCall(taskStatusId, taskDate, contractNo, billId, PenaltyApiType.COLLECTION_QUERY.getCode(), 
                "/charge/v2/receipt/listByBill", requestParamsJson, 
                responseBody, System.currentTimeMillis() - startTime, null);
                
            int resultCount = (pageResult != null && pageResult.getRows() != null) ? pageResult.getRows().size() : 0;
            log.debug("调用工银收款单接口成功，账单：{}，返回收款单数：{}，耗时：{}ms",
                     billId, resultCount, System.currentTimeMillis() - startTime);
                
            return pageResult;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            
            // 记录失败调用日志
            logApiCall(taskStatusId, taskDate, contractNo, billId, PenaltyApiType.COLLECTION_QUERY.getCode(),
                "/charge/v2/receipt/listByBill", requestParamsJson, responseBody,
                duration, errorMessage);
                
            log.error("调用工银收款单接口失败，账单：{}，错误：{}，耗时：{}ms",
                     billId, errorMessage, duration);
            return new PageResult<>(new ArrayList<>());
        }
    }
    
    /**
     * 解析工银收款单接口响应（参考BbpmCollectionServiceImpl.resetCollection）
     */
    private PageResult<List<BbpmCollectionPageResultVo>> resetCollectionData(String responseBody) {
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmCollectionPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            log.warn("工银收款单接口返回错误：code={}, message={}", billListResultFaceHttpResultTwo.getCode(), billListResultFaceHttpResultTwo.getMessage());
            return new PageResult<>(new ArrayList<>());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(new ArrayList<>());
        }
        
        List<BbpmCollectionPageResultVo> collectionListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(), BbpmCollectionPageResultVo.class);

        // 注意：这里简化处理，不做复杂的数据转换，因为违约金计算只需要基本信息
        // 如果需要完整的数据转换，可以参考BbpmCollectionServiceImpl.resetCollection的完整逻辑
        
        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(), collectionListResultList);
    }
    
    /**
     * 计算总缴费金额
     */
    private BigDecimal calculateTotalPaidAmount(List<BbpmCollectionPageResultVo> payments) {
        return payments.stream()
            .map(payment -> payment.getChargeMoney() != null ? payment.getChargeMoney() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 分析对账状态
     */
    private String analyzeReconciliationStatus(List<BbpmCollectionPageResultVo> payments) {
        if (payments.isEmpty()) {
            return "02"; // 没有收款单，返回未对平
        }
        
        // 直接使用reconciliationResult字段判断对账状态
        // 优先级：01对平 > 03待对账 > 02未对平
        boolean hasReconciled = payments.stream()
            .anyMatch(payment -> "01".equals(payment.getReconciliationResult())); // 01-对平
        
        if (hasReconciled) {
            return "01"; // 对平
        }
        
        boolean hasPending = payments.stream()
            .anyMatch(payment -> "03".equals(payment.getReconciliationResult())); // 03-待对账
            
        if (hasPending) {
            return "03"; // 待对账
        }
        
        return "02"; // 未对平
    }
    
    /**
     * 判断是否应该计算违约金
     */
    private boolean shouldCalculatePenalty(BbpmBillManagementPageResultVo bill,
                                         LocalDate calculationDate,
                                         String reconciliationStatus,
                                         BigDecimal penaltyBaseAmount) {
        // 1. 检查是否已经逾期
        LocalDate payableDate = parseDate(bill.getPayableDate());
        if (payableDate == null || !calculationDate.isAfter(payableDate)) {
            return false; // 还没到期，不计算违约金
        }
        
        // 2. 检查是否还有未对平金额
        if (penaltyBaseAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("账单 {} 在日期 {} 已全部对平，无需计算违约金", bill.getBillId(), calculationDate);
            return false; // 已全部对平，不计算违约金
        }
        
        // 3. 检查对账状态（关键逻辑）
        if ("01".equals(reconciliationStatus)) {
            // 已对平的情况，不计算违约金
            return false;
        }
        
        return true; // 有未对平金额且未完全对账，需要计算违约金
    }
    
    /**
     * 获取最早的银行回单日期
     */
    private LocalDate getEarliestBankReceiptDate(List<BbpmCollectionPageResultVo> payments, LocalDate defaultDate) {
        return payments.stream()
            .map(payment -> {
                Date receiptDate = payment.getBankReceiptDate();
                if (receiptDate != null) {
                    return receiptDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
                return null;
            })
            .filter(date -> date != null)
            .min(LocalDate::compareTo)
            .orElse(defaultDate);
    }
    
    /**
     * 确保试算账单记录存在（增强版本）
     */
    private void ensureTrialBillExists(BbpmBillManagementPageResultVo bill, 
                                     List<BbpmCollectionPageResultVo> payments, 
                                     String reconciliationStatus, 
                                     LocalDate earliestReceiptDate,
                                     BigDecimal dailyOverdueRate) {
        try {
            // 1. 检查试算账单是否已存在
            BbpmPenaltyFeeTrialBillVo existingBill = null;
            try {
                existingBill = selectTrialBillByBillId(bill.getBillId());
            } catch (Exception e) {
                log.debug("查询试算账单时出错：{}", e.getMessage());
            }
            
            if (existingBill == null) {
                // 2. 创建新的试算账单记录
                BbpmPenaltyFeeTrialBillVo trialBill = new BbpmPenaltyFeeTrialBillVo();
                
                // 设置试算账单ID
                trialBill.setTrialBillId(UUID.randomUUID().toString().replace("-", ""));
                
                // 设置工银账单基本信息
                trialBill.setBillId(bill.getBillId());
                trialBill.setBillCycle(bill.getBillCycle());
                trialBill.setProjectName(bill.getProjectName());
                trialBill.setHouseName(bill.getHouseName());
                trialBill.setTenantCode(bill.getTenantCode());
                trialBill.setTenantName(bill.getTenantName());
                trialBill.setContractCode(bill.getContractNo());
                trialBill.setChargeSubjectBeginDate(parseDate(bill.getChargeSubjectBeginDate()));
                trialBill.setChargeSubjectEndDate(parseDate(bill.getChargeSubjectEndDate()));
                trialBill.setChargeSubjectPeriod(bill.getChargeSubjectPeriod());
                trialBill.setPayableDate(parseDate(bill.getPayableDate()));
                trialBill.setShouldPayAmount(parseBigDecimal(bill.getShouldPayAmount()));
                trialBill.setPayedAmount(parseBigDecimal(bill.getPayedAmount()));
                trialBill.setReplacePayAmount(parseBigDecimal(bill.getReplacePayAmount()));
                trialBill.setBillChargeSubject(bill.getBillChargeSubject());
                trialBill.setProjectId(bill.getProjectId());
                
                // 设置账单状态和对账状态
                trialBill.setBillStatus(bill.getBillStatus());
                trialBill.setAccountStatus(reconciliationStatus);
                
                // 计算逾期天数
                LocalDate payableDate = parseDate(bill.getPayableDate());
                if (payableDate != null) {
                    int overdueDays = (int) java.time.temporal.ChronoUnit.DAYS.between(payableDate, LocalDate.now());
                    trialBill.setOverdueDays(Math.max(0, overdueDays));
                }
                
                // 设置每日逾期率
                trialBill.setDailyOverdueRate(dailyOverdueRate);
                
                // 设置试算账单状态
                trialBill.setStatus("1"); // 1-试算中
                trialBill.setDelFlag("1");
                
                // 设置创建信息（简化实现，这些字段可能通过父类设置）
                // trialBill.setCreateTime(new Date());
                // trialBill.setCreateUser("SYSTEM_PENALTY_TASK");
                
                // 3. 保存试算账单记录
                penaltyFeeTrialBillService.insertRecord(trialBill);
                
                log.info("创建试算账单记录：账单ID={}, 试算账单ID={}, 合同号={}, 应缴金额={}", 
                        bill.getBillId(), trialBill.getTrialBillId(), bill.getContractNo(), 
                        trialBill.getShouldPayAmount());
                        
            } else {
                // 4. 更新已存在的试算账单信息（如对账状态等可能变化的字段）
                boolean needUpdate = false;
                
                if (!Objects.equals(existingBill.getAccountStatus(), reconciliationStatus)) {
                    existingBill.setAccountStatus(reconciliationStatus);
                    needUpdate = true;
                }
                
                if (!Objects.equals(existingBill.getBillStatus(), bill.getBillStatus())) {
                    existingBill.setBillStatus(bill.getBillStatus());
                    needUpdate = true;
                }
                
                BigDecimal currentPayedAmount = parseBigDecimal(bill.getPayedAmount());
                if (existingBill.getPayedAmount() != null && 
                    existingBill.getPayedAmount().compareTo(currentPayedAmount) != 0) {
                    existingBill.setPayedAmount(currentPayedAmount);
                    existingBill.setReplacePayAmount(parseBigDecimal(bill.getReplacePayAmount()));
                    needUpdate = true;
                }
                
                if (needUpdate) {
                    // 更新试算账单
                    penaltyFeeTrialBillService.updateByIdRecord(existingBill);
                    log.debug("更新试算账单记录：{}", bill.getBillId());
                }
            }
            
        } catch (Exception e) {
            log.error("确保试算账单存在时发生异常：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 构建每日违约金明细记录
     */
    private BbpmPenaltyFeeDailyDetailVo buildDailyRecord(BbpmBillManagementPageResultVo bill, 
                                                        LocalDate actualCalculationDate,
                                                        BigDecimal penaltyBaseAmount, 
                                                        BigDecimal dailyRate,
                                                        BigDecimal dailyPenaltyAmount,
                                                        String reconciliationStatus,
                                                        List<BbpmCollectionPageResultVo> payments) {
        BbpmPenaltyFeeDailyDetailVo record = new BbpmPenaltyFeeDailyDetailVo();
        record.setBillId(bill.getBillId());
        
        // 使用正确的计算日期：基于收费科目起始日期或应缴费日期的较早者
        LocalDate correctCalculationDate = getCorrectCalculationDate(bill, actualCalculationDate);
        record.setCalculationDate(correctCalculationDate);
        
        record.setReplacePayAmount(penaltyBaseAmount);
        record.setDailyOverdueRate(dailyRate);
        record.setDailyPenaltyAmount(dailyPenaltyAmount);
        record.setEntryType("1"); // 1-正常计算
        record.setBillStatus(bill.getBillStatus());
        record.setAccountStatus(reconciliationStatus);
        record.setAdjustmentSeq(1);
        record.setDelFlag("1");
        record.setProjectId(bill.getProjectId());
        
        // 设置银行回单日期（charge_time字段）
        LocalDate chargeTime = getEarliestBankReceiptDate(payments, actualCalculationDate);
        if (chargeTime != null) {
            record.setChargeTime(chargeTime);
        }
        
        return record;
    }
    
    /**
     * 获取正确的计算日期（基于账单的逾期开始日期）
     */
    private LocalDate getCorrectCalculationDate(BbpmBillManagementPageResultVo bill, LocalDate taskExecutionDate) {
        // 1. 解析收费科目起始日期和应缴费日期
        LocalDate chargeSubjectBeginDate = parseDate(bill.getChargeSubjectBeginDate());
        LocalDate payableDate = parseDate(bill.getPayableDate());
        
        // 2. 选择较早的日期作为逾期计算起始日期
        LocalDate overdueStartDate = null;
        if (chargeSubjectBeginDate != null && payableDate != null) {
            // 两个日期都存在，选择较早的
            overdueStartDate = chargeSubjectBeginDate.isBefore(payableDate) ? chargeSubjectBeginDate : payableDate;
        } else if (chargeSubjectBeginDate != null) {
            // 只有收费科目起始日期
            overdueStartDate = chargeSubjectBeginDate;
        } else if (payableDate != null) {
            // 只有应缴费日期
            overdueStartDate = payableDate;
        } else {
            // 两个日期都为空，使用任务执行日期前一天
            overdueStartDate = taskExecutionDate.minusDays(1);
            log.warn("账单 {} 的收费科目起始日期和应缴费日期都为空，使用任务执行日期前一天：{}", 
                    bill.getBillId(), overdueStartDate);
        }
        
        log.debug("账单 {} 计算日期确定：收费起始={}, 应缴费={}, 选择={}", 
                bill.getBillId(), chargeSubjectBeginDate, payableDate, overdueStartDate);
        
        return overdueStartDate;
    }
    
    /**
     * 更新试算账单汇总信息
     */
    private void updateTrialBillSummary(String billId, LocalDate calculationDate) {
        try {
            // 查询该账单的所有每日明细记录
            List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = selectDailyDetailsByBillId(billId);
            
            if (dailyDetails != null && !dailyDetails.isEmpty()) {
                // 计算违约金总金额（只计算正常计算记录，排除红冲记录）
                BigDecimal totalPenaltyAmount = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType())) // 1-正常计算
                    .map(detail -> detail.getDailyPenaltyAmount() != null ? detail.getDailyPenaltyAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                // 计算逾期天数（从最早计算日期到最晚计算日期）
                LocalDate earliestDate = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType()))
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .min(LocalDate::compareTo)
                    .orElse(calculationDate);
                
                LocalDate latestDate = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType()))
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .max(LocalDate::compareTo)
                    .orElse(calculationDate);
                
                int overdueDays = (int) java.time.temporal.ChronoUnit.DAYS.between(earliestDate, latestDate) + 1;
                
                // 查询试算账单并更新汇总字段
                BbpmPenaltyFeeTrialBillVo trialBill = selectTrialBillByBillId(billId);
                if (trialBill != null) {
                    trialBill.setTotalPenaltyAmount(totalPenaltyAmount);
                    trialBill.setOverdueDays(overdueDays);
                    
                    // 更新试算账单
                    penaltyFeeTrialBillService.updateByIdRecord(trialBill);
                    
                    log.debug("更新试算账单汇总信息：{}，违约金总额：{}，逾期天数：{}", 
                             billId, totalPenaltyAmount, overdueDays);
                }
            }
            
        } catch (Exception e) {
            log.error("更新试算账单汇总信息失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成分阶段汇总数据（包含验证）
     */
    private void generateStageSummaryWithValidation(String trialBillId) {
        try {
            // 1. 生成分阶段汇总
            generateStageSummary(trialBillId);
            
            // 2. 查询生成的汇总数据
            List<BbpmPenaltyFeeStageSummaryVo> stageSummaries = selectStageSummaryByTrialBillId(trialBillId);
            
            // 3. 验证准确性
            validateStageSummary(trialBillId, stageSummaries);
            
            log.info("分阶段汇总生成并验证完成: trialBillId={}, 阶段数={}", trialBillId, stageSummaries.size());
            
        } catch (Exception e) {
            log.error("生成分阶段汇总失败: trialBillId={}", trialBillId, e);
            throw new RuntimeException("生成分阶段汇总失败", e);
        }
    }
    
    /**
     * 生成分阶段汇总
     */
    private void generateStageSummary(String trialBillId) {
        try {
            // 1. 查询试算账单信息获取billId
            BbpmPenaltyFeeTrialBillVo trialBill = penaltyFeeTrialBillService.selectByIdRecord(trialBillId);
            if (trialBill == null) {
                log.warn("试算账单 {} 不存在，跳过分阶段汇总", trialBillId);
                return;
            }
            
            // 2. 查询该账单的所有每日明细记录
            List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = selectDailyDetailsByBillId(trialBill.getBillId());
            
            if (dailyDetails == null || dailyDetails.isEmpty()) {
                log.debug("试算账单 {} 没有每日明细记录，跳过分阶段汇总", trialBillId);
                return;
            }
            
            // 3. 按收款时间（charge_time）分组生成阶段
            Map<LocalDate, List<BbpmPenaltyFeeDailyDetailVo>> stageGroups = dailyDetails.stream()
                .filter(detail -> "1".equals(detail.getEntryType())) // 只考虑正常计算记录
                .collect(Collectors.groupingBy(detail -> {
                    // 按charge_time分组，如果没有charge_time则按calculation_date分组
                    if (detail.getChargeTime() != null) {
                        return detail.getChargeTime(); // 已经是LocalDate类型
                    } else {
                        return detail.getCalculationDate();
                    }
                }));
            
            // 4. 删除现有的分阶段汇总记录
            deleteStageSummaryByTrialBillId(trialBillId);
            
            // 5. 为每个阶段生成汇总记录
            int stageNo = 1;
            for (Map.Entry<LocalDate, List<BbpmPenaltyFeeDailyDetailVo>> entry : stageGroups.entrySet()) {
                LocalDate chargeDate = entry.getKey();
                List<BbpmPenaltyFeeDailyDetailVo> stageDetails = entry.getValue();
                
                // 计算阶段汇总信息
                LocalDate penaltyStartDate = stageDetails.stream()
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .min(LocalDate::compareTo)
                    .orElse(chargeDate);
                
                LocalDate penaltyEndDate = stageDetails.stream()
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .max(LocalDate::compareTo)
                    .orElse(chargeDate);
                
                int overdueDays = (int) java.time.temporal.ChronoUnit.DAYS.between(penaltyStartDate, penaltyEndDate) + 1;
                
                BigDecimal stagePenaltyAmount = stageDetails.stream()
                    .map(detail -> detail.getDailyPenaltyAmount() != null ? detail.getDailyPenaltyAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                BigDecimal unpaidAmount = stageDetails.stream()
                    .map(detail -> detail.getReplacePayAmount() != null ? detail.getReplacePayAmount() : BigDecimal.ZERO)
                    .findFirst() // 取第一个作为欠缴金额
                    .orElse(BigDecimal.ZERO);
                
                BigDecimal dailyOverdueRate = stageDetails.stream()
                    .map(detail -> detail.getDailyOverdueRate() != null ? detail.getDailyOverdueRate() : BigDecimal.ZERO)
                    .findFirst() // 取第一个作为每日逾期率
                    .orElse(BigDecimal.ZERO);
                
                // 构建分阶段汇总记录
                BbpmPenaltyFeeStageSummaryVo stageSummary = new BbpmPenaltyFeeStageSummaryVo();
                stageSummary.setTrialBillId(trialBillId);
                stageSummary.setCurrentStage(String.valueOf(stageNo++));
                stageSummary.setPenaltyStartDate(penaltyStartDate);
                stageSummary.setPenaltyEndDate(penaltyEndDate);
                stageSummary.setOverdueDays(overdueDays);
                stageSummary.setDailyOverdueRate(dailyOverdueRate);
                stageSummary.setUnpaidAmount(unpaidAmount);
                stageSummary.setStagePenaltyAmount(stagePenaltyAmount);
                stageSummary.setDelFlag("1");
                
                // 保存分阶段汇总记录
                penaltyFeeStageSummaryService.insertRecord(stageSummary);
                
                log.debug("生成分阶段汇总：试算账单={}, 阶段={}, 违约金={}, 逾期天数={}", 
                         trialBillId, stageSummary.getCurrentStage(), stagePenaltyAmount, overdueDays);
            }
            
            log.info("试算账单 {} 分阶段汇总生成完成，共 {} 个阶段", trialBillId, stageGroups.size());
            
        } catch (Exception e) {
            log.error("生成分阶段汇总失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 验证分阶段汇总准确性
     */
    private void validateStageSummary(String trialBillId, List<BbpmPenaltyFeeStageSummaryVo> stageSummaries) {
        try {
            if (stageSummaries == null || stageSummaries.isEmpty()) {
                // 查询试算账单下的分阶段汇总记录
                stageSummaries = selectStageSummaryByTrialBillId(trialBillId);
                if (stageSummaries.isEmpty()) {
                    log.debug("没有分阶段汇总数据需要验证：{}", trialBillId);
                    return;
                }
            }
            
            // 1. 验证汇总金额是否正确
            BigDecimal totalStagePenalty = stageSummaries.stream()
                .map(stage -> stage.getStagePenaltyAmount() != null ? stage.getStagePenaltyAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 2. 查询试算账单获取billId，然后查询每日明细的汇总金额进行对比
            BbpmPenaltyFeeTrialBillVo trialBill = penaltyFeeTrialBillService.selectByIdRecord(trialBillId);
            if (trialBill != null) {
                List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = selectDailyDetailsByBillId(trialBill.getBillId());
                BigDecimal totalDailyPenalty = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType())) // 只计算正常计算记录
                    .map(detail -> detail.getDailyPenaltyAmount() != null ? detail.getDailyPenaltyAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                // 3. 比较金额是否一致
                if (totalStagePenalty.compareTo(totalDailyPenalty) != 0) {
                    log.warn("分阶段汇总金额不一致：试算账单={}, 汇总金额={}, 明细金额={}", 
                            trialBillId, totalStagePenalty, totalDailyPenalty);
                } else {
                    log.debug("分阶段汇总验证通过：试算账单={}, 金额={}", trialBillId, totalStagePenalty);
                }
            }
            
            // 4. 验证阶段连续性
            stageSummaries.sort((a, b) -> Integer.compare(
                Integer.parseInt(a.getCurrentStage()), 
                Integer.parseInt(b.getCurrentStage())
            ));
            
            for (int i = 0; i < stageSummaries.size(); i++) {
                BbpmPenaltyFeeStageSummaryVo stage = stageSummaries.get(i);
                
                // 验证阶段编号
                int expectedStageNo = i + 1;
                if (!String.valueOf(expectedStageNo).equals(stage.getCurrentStage())) {
                    log.warn("分阶段编号不连续：试算账单={}, 期望={}, 实际={}", 
                            trialBillId, expectedStageNo, stage.getCurrentStage());
                }
                
                // 验证日期合理性
                if (stage.getPenaltyStartDate() != null && stage.getPenaltyEndDate() != null) {
                    if (stage.getPenaltyStartDate().isAfter(stage.getPenaltyEndDate())) {
                        log.warn("分阶段日期错误：试算账单={}, 阶段={}, 开始日期={}, 结束日期={}", 
                                trialBillId, stage.getCurrentStage(), stage.getPenaltyStartDate(), stage.getPenaltyEndDate());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("验证分阶段汇总失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查是否需要自动完结账单
     */
    private boolean checkAndAutoFinalize(String billId) {
        try {
            // 1. 查询试算账单信息
            BbpmPenaltyFeeTrialBillVo trialBill = selectTrialBillByBillId(billId);
            if (trialBill == null || !"1".equals(trialBill.getStatus())) {
                log.debug("试算账单不存在或已完结：{}", billId);
                return false;
            }
            
            // 2. 检查自动完结条件
            boolean shouldAutoFinalize = false;
            String finalizeReason = null;
            
            // 条件1：账单状态为已缴足额支付（01）且已对平（01）
            if ("01".equals(trialBill.getBillStatus()) && "01".equals(trialBill.getAccountStatus())) {
                shouldAutoFinalize = true;
                finalizeReason = "1"; // 已缴足额支付
                log.debug("账单 {} 满足自动完结条件：已缴足额且已对平", billId);
            }
            
            // 条件2：试算时间超过指定天数（如30天）
            // 由于Entity中没有createTime字段，这里先简化处理
            // if (!shouldAutoFinalize) {
            //     // 可以通过其他方式判断试算时间，比如查询最早的每日明细记录日期
            //     List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = selectDailyDetailsByBillId(billId);
            //     if (!dailyDetails.isEmpty()) {
            //         LocalDate earliestDate = dailyDetails.stream()
            //             .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
            //             .min(LocalDate::compareTo)
            //             .orElse(LocalDate.now());
            //         long daysSinceCreation = java.time.temporal.ChronoUnit.DAYS.between(earliestDate, LocalDate.now());
            //         
            //         if (daysSinceCreation >= 30) { // 30天自动完结
            //             shouldAutoFinalize = true;
            //             finalizeReason = "3"; // 超时完结
            //             log.debug("账单 {} 满足自动完结条件：试算超过30天", billId);
            //         }
            //     }
            // }
            
            // 3. 执行自动完结
            if (shouldAutoFinalize) {
                autoFinalizeTrialBill(trialBill, finalizeReason);
                return true;
            } else {
                log.debug("账单 {} 不满足自动完结条件", billId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("检查自动完结失败：{}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 执行自动完结试算账单
     */
    private void autoFinalizeTrialBill(BbpmPenaltyFeeTrialBillVo trialBill, String finalizeReason) {
        try {
            // 1. 创建完结账单记录
            BbpmPenaltyFeeFinalizedBillVo finalizedBill = new BbpmPenaltyFeeFinalizedBillVo();
            
            // 复制试算账单的基本信息
            finalizedBill.setTrialBillId(trialBill.getTrialBillId());
            finalizedBill.setBillId(trialBill.getBillId());
            finalizedBill.setBillCycle(trialBill.getBillCycle());
            finalizedBill.setProjectName(trialBill.getProjectName());
            finalizedBill.setHouseName(trialBill.getHouseName());
            finalizedBill.setTenantCode(trialBill.getTenantCode());
            finalizedBill.setTenantName(trialBill.getTenantName());
            finalizedBill.setContractCode(trialBill.getContractCode());
            finalizedBill.setChargeSubjectBeginDate(trialBill.getChargeSubjectBeginDate());
            finalizedBill.setChargeSubjectEndDate(trialBill.getChargeSubjectEndDate());
            finalizedBill.setChargeSubjectPeriod(trialBill.getChargeSubjectPeriod());
            finalizedBill.setPayableDate(trialBill.getPayableDate());
            finalizedBill.setShouldPayAmount(trialBill.getShouldPayAmount());
            finalizedBill.setPayedAmount(trialBill.getPayedAmount());
            finalizedBill.setReplacePayAmount(trialBill.getReplacePayAmount());
            finalizedBill.setOverdueDays(trialBill.getOverdueDays());
            finalizedBill.setDailyOverdueRate(trialBill.getDailyOverdueRate());
            finalizedBill.setTotalPenaltyAmount(trialBill.getTotalPenaltyAmount());
            finalizedBill.setBillChargeSubject(trialBill.getBillChargeSubject());
            
            // 设置完结相关信息
            finalizedBill.setFinalizedReason(finalizeReason);
            finalizedBill.setFinalizedDate(LocalDate.now());
            finalizedBill.setDisposalStatus("1"); // 未处置
            finalizedBill.setProjectId(trialBill.getProjectId());
            finalizedBill.setDelFlag("1");
            
            // 2. 保存完结账单
            penaltyFeeFinalizedBillService.insertRecord(finalizedBill);
            
            // 3. 更新试算账单状态为已完结
            trialBill.setStatus("2"); // 已完结
            penaltyFeeTrialBillService.updateByIdRecord(trialBill);
            
            log.info("自动完结试算账单成功：账单ID={}, 完结原因={}", trialBill.getBillId(), finalizeReason);
            
        } catch (Exception e) {
            log.error("自动完结试算账单失败：{}", e.getMessage(), e);
        }
    }
    

    
    /**
     * 调整违约金（红冲+重新计算）
     */
    private void adjustPenalty(BbpmBillManagementPageResultVo bill, LocalDate discoveryDate, String projectId) {
        try {
            // 1. 查询当前账单的收款单信息（带日志记录）
            PageResult<List<BbpmCollectionPageResultVo>> paymentsResult = queryPaymentsByBillId(
                null, bill.getBillId(), discoveryDate, bill.getContractNo(), projectId);
            List<BbpmCollectionPageResultVo> currentPayments = 
                (paymentsResult != null && paymentsResult.getRows() != null) ? paymentsResult.getRows() : new ArrayList<>();
                
            LocalDate bankReceiptDate = getEarliestBankReceiptDate(currentPayments, discoveryDate);
            
            if (bankReceiptDate == null) {
                log.warn("账单 {} 没有银行回单日期，跳过调整", bill.getBillId());
                return;
            }
            
            // 2. 查询需要调整的记录（从银行回单日期开始）
            List<BbpmPenaltyFeeDailyDetailVo> recordsToAdjust = selectDailyDetailsFromDate(bill.getBillId(), bankReceiptDate);
            
            if (recordsToAdjust.isEmpty()) {
                log.info("账单 {} 从日期 {} 开始没有需要调整的违约金记录", bill.getBillId(), bankReceiptDate);
                return;
            }
            
            log.info("找到账单 {} 从日期 {} 开始需要调整的 {} 条违约金记录", 
                    bill.getBillId(), bankReceiptDate, recordsToAdjust.size());
            
            // 3. 对每条记录进行精确红冲
            for (BbpmPenaltyFeeDailyDetailVo record : recordsToAdjust) {
                try {
                    insertPreciseAdjustmentRecord(record, "对账状态变化调整");
                } catch (Exception e) {
                    log.error("红冲记录失败：记录ID={}，错误：{}", record.getDailyDetailId(), e.getMessage(), e);
                }
            }
            
            // 4. 更新试算账单汇总信息
            updateTrialBillSummary(bill.getBillId(), discoveryDate);
            
        } catch (Exception e) {
            log.error("调整违约金失败：{}", e.getMessage(), e);
        }
    }
 


    public String getTokenByFixedUser(){
        String token = bmsBaseServiceFeignService.getToken();
        return token;
    }
    
    /**
     * 清理错误的全局任务状态记录（用于修复程序异常后的状态问题）
     * 此方法会删除可能错误创建的 GLOBAL_TASK_ 记录，让任务可以重新执行
     * 
     * @param taskDate 任务日期
     */
    public void cleanupIncorrectGlobalTaskStatus(LocalDate taskDate) {
        try {
            String globalContractCode = "GLOBAL_TASK_" + taskDate.toString().replace("-", "");
            
            log.info("开始检查并清理可能错误的全局任务状态记录：{}", globalContractCode);
            
            // 查询 GLOBAL_TASK_ 记录
            List<BbpmPenaltyTaskStatusVo> globalTaskList = queryTaskStatusList(taskDate, globalContractCode, null);
            
            if (globalTaskList.isEmpty()) {
                log.info("未找到全局任务状态记录：{}", globalContractCode);
                return;
            }
            
            // 检查普通合同的任务状态，判断全局状态是否准确
            List<BbpmPenaltyTaskStatusVo> allTaskStatus = queryTaskStatusList(taskDate, null, null);
            List<BbpmPenaltyTaskStatusVo> normalContractTasks = allTaskStatus.stream()
                .filter(task -> !task.getContractCode().startsWith("GLOBAL_TASK_"))
                .collect(Collectors.toList());
            
            if (normalContractTasks.isEmpty()) {
                log.info("没有普通合同任务记录，全局任务状态记录可能是正确的（无合同情况）");
                return;
            }
            
            // 统计各状态任务数量
            long completedCount = normalContractTasks.stream().filter(t -> "2".equals(t.getProcessStatus())).count();
            long failedCount = normalContractTasks.stream().filter(t -> "3".equals(t.getProcessStatus())).count();
            long processingCount = normalContractTasks.stream().filter(t -> "1".equals(t.getProcessStatus())).count();
            long pendingCount = normalContractTasks.stream().filter(t -> "0".equals(t.getProcessStatus())).count();
            
            BbpmPenaltyTaskStatusVo globalTask = globalTaskList.get(0);
            
            log.info("全局任务状态：{}，普通任务统计 - 总数：{}，已完成：{}，失败：{}，处理中：{}，待处理：{}", 
                globalTask.getProcessStatus(), normalContractTasks.size(), 
                completedCount, failedCount, processingCount, pendingCount);
            
            // 如果全局任务状态是"已完成"，但还有未完成的普通任务，则认为是错误状态
            boolean isGlobalStatusIncorrect = "2".equals(globalTask.getProcessStatus()) && 
                (processingCount > 0 || pendingCount > 0 || failedCount > 0);
            
            if (isGlobalStatusIncorrect) {
                log.warn("检测到错误的全局任务状态：全局状态为已完成，但还有 {} 个未完成的普通任务", 
                    (processingCount + pendingCount + failedCount));
                
                // 删除错误的全局任务状态记录
                try {
                    penaltyTaskStatusService.removeByIdRecord(globalTask.getTaskStatusId());
                    log.info("已删除错误的全局任务状态记录：{}", globalContractCode);
                } catch (Exception deleteEx) {
                    log.error("删除错误的全局任务状态记录失败：{}", deleteEx.getMessage());
                }
            } else {
                log.info("全局任务状态看起来是正确的，无需清理");
            }
            
        } catch (Exception e) {
            log.error("清理错误的全局任务状态记录失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 清理重复或异常的任务状态记录（用于修复数据不一致问题）
     * 注意：此方法应谨慎使用，建议在维护窗口期间执行
     */
    public void cleanupDuplicateTaskStatus(LocalDate taskDate) {
        try {
            log.info("开始检查并清理任务日期 {} 的重复任务状态记录", taskDate);
            
            // 查询指定日期的所有任务状态记录
            List<BbpmPenaltyTaskStatusVo> allTaskStatus = queryTaskStatusList(taskDate, null, null);
            
            if (allTaskStatus.isEmpty()) {
                log.info("任务日期 {} 没有找到任务状态记录", taskDate);
                return;
            }
            
            // 按合同编号分组，检查重复
            Map<String, List<BbpmPenaltyTaskStatusVo>> contractGroups = allTaskStatus.stream()
                .collect(Collectors.groupingBy(BbpmPenaltyTaskStatusVo::getContractCode));
            
            int duplicateCount = 0;
            int cleanedCount = 0;
            
            for (Map.Entry<String, List<BbpmPenaltyTaskStatusVo>> entry : contractGroups.entrySet()) {
                String contractNo = entry.getKey();
                List<BbpmPenaltyTaskStatusVo> records = entry.getValue();
                
                if (records.size() > 1) {
                    duplicateCount++;
                    log.warn("发现合同 {} 在任务日期 {} 有 {} 条重复记录", contractNo, taskDate, records.size());
                    
                    // 保留策略：优先保留状态不是"0"（待处理）的记录，其次保留最新创建的记录
                    BbpmPenaltyTaskStatusVo keepRecord = records.stream()
                        .filter(r -> !"0".equals(r.getProcessStatus()))
                        .findFirst()
                        .orElse(records.stream()
                            .max((r1, r2) -> {
                                Date d1 = r1.getCreateTime();
                                Date d2 = r2.getCreateTime();
                                if (d1 == null && d2 == null) return 0;
                                if (d1 == null) return -1;
                                if (d2 == null) return 1;
                                return d1.compareTo(d2);
                            })
                            .orElse(records.get(0)));
                    
                    log.info("合同 {} 将保留记录：ID={}, Status={}, CreateTime={}", 
                        contractNo, keepRecord.getTaskStatusId(), keepRecord.getProcessStatus(), keepRecord.getCreateTime());
                    
                    // 删除其他重复记录
                    for (BbpmPenaltyTaskStatusVo record : records) {
                        if (!record.getTaskStatusId().equals(keepRecord.getTaskStatusId())) {
                            try {
                                penaltyTaskStatusService.removeByIdRecord(record.getTaskStatusId());
                                cleanedCount++;
                                log.info("已删除重复记录：ID={}, Status={}", record.getTaskStatusId(), record.getProcessStatus());
                            } catch (Exception deleteEx) {
                                log.error("删除重复记录失败：ID={}, 错误：{}", record.getTaskStatusId(), deleteEx.getMessage());
                            }
                        }
                    }
                }
            }
            
            log.info("任务状态记录清理完成 - 发现重复合同：{}，清理记录：{}，剩余合同：{}", 
                duplicateCount, cleanedCount, contractGroups.size());
                
        } catch (Exception e) {
            log.error("清理重复任务状态记录失败：{}", e.getMessage(), e);
        }
    }

    // ==================== 自定义查询方法 ====================
    
    /**
     * 根据billId查询每日明细记录
     */
    private List<BbpmPenaltyFeeDailyDetailVo> selectDailyDetailsByBillId(String billId) {
        try {
            // 直接查询每日明细记录
            BbpmPenaltyFeeDailyDetailVo queryVo = new BbpmPenaltyFeeDailyDetailVo();
            queryVo.setBillId(billId);
            
            return penaltyFeeDailyDetailService.selectListByCondition(queryVo);
            
        } catch (Exception e) {
            log.error("查询每日明细记录失败：billId={}", billId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据billId查询试算账单
     */
    private BbpmPenaltyFeeTrialBillVo selectTrialBillByBillId(String billId) {
        try {
            // 直接查询试算账单
            List<BbpmPenaltyFeeTrialBillVo> trialBills = queryTrialBillList(null, billId);
            return trialBills.isEmpty() ? null : trialBills.get(0);
        } catch (Exception e) {
            log.error("查询试算账单失败：billId={}", billId, e);
            return null;
        }
    }
    
    /**
     * 根据trialBillId查询分阶段汇总记录
     */
    private List<BbpmPenaltyFeeStageSummaryVo> selectStageSummaryByTrialBillId(String trialBillId) {
        try {
            // 直接查询阶段汇总记录
            BbpmPenaltyFeeStageSummaryVo queryVo = new BbpmPenaltyFeeStageSummaryVo();
            queryVo.setTrialBillId(trialBillId);
            
            return penaltyFeeStageSummaryService.selectListByCondition(queryVo);
        } catch (Exception e) {
            log.error("查询分阶段汇总记录失败：trialBillId={}", trialBillId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据trialBillId删除分阶段汇总记录
     */
    private void deleteStageSummaryByTrialBillId(String trialBillId) {
        try {
            // 先查询所有相关的记录
            List<BbpmPenaltyFeeStageSummaryVo> stageSummaries = selectStageSummaryByTrialBillId(trialBillId);
            
            // 删除所有记录
            for (BbpmPenaltyFeeStageSummaryVo stageSummary : stageSummaries) {
                penaltyFeeStageSummaryService.removeByIdRecord(stageSummary.getStageSummaryId());
            }
            
            log.debug("删除分阶段汇总记录：trialBillId={}, 删除数量={}", trialBillId, stageSummaries.size());
            
        } catch (Exception e) {
            log.error("删除分阶段汇总记录失败：trialBillId={}", trialBillId, e);
        }
    }
    
    /**
     * 查询从指定日期开始的每日明细记录
     */
    private List<BbpmPenaltyFeeDailyDetailVo> selectDailyDetailsFromDate(String billId, LocalDate fromDate) {
        try {
            // 先查询该账单的所有每日明细记录
            List<BbpmPenaltyFeeDailyDetailVo> allDetails = selectDailyDetailsByBillId(billId);
            
            // 筛选出从指定日期开始的正常计算记录
            return allDetails.stream()
                .filter(detail -> "1".equals(detail.getEntryType())) // 只处理正常计算记录
                .filter(detail -> detail.getCalculationDate() != null)
                .filter(detail -> !detail.getCalculationDate().isBefore(fromDate)) // 从指定日期开始
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("查询从日期 {} 开始的每日明细记录失败：billId={}", fromDate, billId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 插入精确红冲记录
     */
    private void insertPreciseAdjustmentRecord(BbpmPenaltyFeeDailyDetailVo originalRecord, String adjustmentReason) {
        try {
            // 创建红冲记录
            BbpmPenaltyFeeDailyDetailVo adjustmentRecord = new BbpmPenaltyFeeDailyDetailVo();
            
            // 复制原记录的基本信息
            adjustmentRecord.setBillId(originalRecord.getBillId());
            adjustmentRecord.setCalculationDate(originalRecord.getCalculationDate());
            adjustmentRecord.setReplacePayAmount(originalRecord.getReplacePayAmount());
            adjustmentRecord.setDailyOverdueRate(originalRecord.getDailyOverdueRate());
            adjustmentRecord.setBillStatus(originalRecord.getBillStatus());
            adjustmentRecord.setAccountStatus(originalRecord.getAccountStatus());
            adjustmentRecord.setChargeTime(originalRecord.getChargeTime());
            adjustmentRecord.setProjectId(originalRecord.getProjectId());
            
            // 设置红冲特有字段
            adjustmentRecord.setEntryType("2"); // 2-调整红冲
            adjustmentRecord.setAdjustmentReason(adjustmentReason);
            adjustmentRecord.setOriginalEntryId(originalRecord.getDailyDetailId()); // 关联原记录
            
            // 红冲金额为负数（与原记录相反）
            BigDecimal originalAmount = originalRecord.getDailyPenaltyAmount();
            if (originalAmount != null) {
                adjustmentRecord.setDailyPenaltyAmount(originalAmount.negate());
            } else {
                adjustmentRecord.setDailyPenaltyAmount(BigDecimal.ZERO);
            }
            
            // 获取当前调整序号（同一天同一原记录的调整次数）
            int adjustmentSeq = getNextAdjustmentSeq(originalRecord.getBillId(), 
                                                    originalRecord.getCalculationDate(), 
                                                    originalRecord.getDailyDetailId());
            adjustmentRecord.setAdjustmentSeq(adjustmentSeq);
            
            // 设置删除标识
            adjustmentRecord.setDelFlag("1");
            
            // 保存红冲记录
            penaltyFeeDailyDetailService.insertRecord(adjustmentRecord);
            
            log.debug("插入红冲记录成功：原记录ID={}, 红冲金额={}, 调整序号={}", 
                     originalRecord.getDailyDetailId(), adjustmentRecord.getDailyPenaltyAmount(), adjustmentSeq);
            
        } catch (Exception e) {
            log.error("插入红冲记录失败：原记录ID={}, 错误：{}", originalRecord.getDailyDetailId(), e.getMessage(), e);
            throw new RuntimeException("插入红冲记录失败", e);
        }
    }
    
    /**
     * 获取下一个调整序号
     */
    private int getNextAdjustmentSeq(String billId, LocalDate calculationDate, String originalEntryId) {
        try {
            // 查询该账单、该日期、该原记录的所有调整记录
            List<BbpmPenaltyFeeDailyDetailVo> allDetails = selectDailyDetailsByBillId(billId);
            
            // 筛选相关的调整记录
            int maxSeq = allDetails.stream()
                .filter(detail -> "2".equals(detail.getEntryType())) // 调整红冲记录
                .filter(detail -> calculationDate.equals(detail.getCalculationDate()))
                .filter(detail -> originalEntryId.equals(detail.getOriginalEntryId()))
                .mapToInt(detail -> detail.getAdjustmentSeq() != null ? detail.getAdjustmentSeq() : 0)
                .max()
                .orElse(1); // 如果没有调整记录，从2开始（1是正常计算记录）
                
            return maxSeq + 1;
            
        } catch (Exception e) {
            log.error("获取调整序号失败：billId={}, calculationDate={}, originalEntryId={}", 
                     billId, calculationDate, originalEntryId, e);
            return 2; // 默认返回2
        }
    }
    
    /**
     * 获取合同的处理状态
     */
    private String getContractProcessStatus(String contractNo, LocalDate taskDate) {
        try {
            List<BbpmPenaltyTaskStatusVo> taskStatusList = queryTaskStatusList(taskDate, contractNo, null);
            if (!taskStatusList.isEmpty()) {
                return taskStatusList.get(0).getProcessStatus();
            }
            
            return null; // 没有记录
            
        } catch (Exception e) {
            log.error("获取合同处理状态失败：contractNo={}, taskDate={}", contractNo, taskDate, e);
            return null;
        }
    }
    
    /**
     * 更新任务状态为处理中（使用幂等性安全操作）
     */
    private String updateTaskStatusToProcessing(String contractNo, LocalDate taskDate) {
        try {
            // 查找现有的任务状态记录
            List<BbpmPenaltyTaskStatusVo> taskStatusList = queryTaskStatusList(taskDate, contractNo, null);
            
            if (!taskStatusList.isEmpty()) {
                // 更新现有记录
                BbpmPenaltyTaskStatusVo taskStatus = taskStatusList.get(0);
                if (!"1".equals(taskStatus.getProcessStatus())) {  // 如果不是已经是处理中状态，才更新
                    taskStatus.setProcessStatus("1"); // 1-处理中
                    taskStatus.setStartTime(new Date());
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                    log.debug("合同 {} 任务状态已更新为处理中", contractNo);
                } else {
                    log.debug("合同 {} 任务状态已是处理中，无需更新", contractNo);
                }
                return taskStatus.getTaskStatusId();
            } else {
                // 创建新记录（但这种情况理论上不应该发生，因为初始化时应该已经创建了所有记录）
                log.warn("合同 {} 在任务日期 {} 没有找到任务状态记录，将创建新记录", contractNo, taskDate);
                
                // 使用幂等性插入：再次检查避免并发创建
                try {
                    BbpmPenaltyTaskStatusVo taskStatus = new BbpmPenaltyTaskStatusVo();
                    taskStatus.setTaskDate(taskDate);
                    taskStatus.setContractCode(contractNo);
                    taskStatus.setProcessStatus("1"); // 1-处理中
                    taskStatus.setBillCount(0);
                    taskStatus.setStartTime(new Date());
                    taskStatus.setDelFlag(1);
                    
                    String taskStatusId = penaltyTaskStatusService.insertRecord(taskStatus);
                    log.info("成功为合同 {} 创建新的任务状态记录：{}", contractNo, taskStatusId);
                    return taskStatusId;
                    
                } catch (Exception insertEx) {
                    // 如果插入失败（可能是唯一键冲突），尝试查询已存在的记录
                    if (insertEx.getMessage() != null && insertEx.getMessage().contains("Duplicate")) {
                        log.info("检测到唯一键冲突，重新查询合同 {} 的任务状态记录", contractNo);
                        List<BbpmPenaltyTaskStatusVo> retryTaskStatusList = queryTaskStatusList(taskDate, contractNo, null);
                        if (!retryTaskStatusList.isEmpty()) {
                            BbpmPenaltyTaskStatusVo existingTaskStatus = retryTaskStatusList.get(0);
                            if (!"1".equals(existingTaskStatus.getProcessStatus())) {
                                existingTaskStatus.setProcessStatus("1");
                                existingTaskStatus.setStartTime(new Date());
                                penaltyTaskStatusService.updateByIdRecord(existingTaskStatus);
                            }
                            return existingTaskStatus.getTaskStatusId();
                        }
                    }
                    throw insertEx; // 其他异常继续抛出
                }
            }
            
        } catch (Exception e) {
            log.error("更新任务状态为处理中失败：contractNo={}, taskDate={}", contractNo, taskDate, e);
            return UUID.randomUUID().toString().replace("-", "");
        }
    }
    
    /**
     * 更新任务状态为已完成
     */
    private void updateTaskStatusToCompleted(String taskStatusId, String contractNo, int billCount, LocalDate taskDate) {
        try {
            if (StringUtils.isNotBlank(taskStatusId)) {
                BbpmPenaltyTaskStatusVo taskStatus = penaltyTaskStatusService.selectByIdRecord(taskStatusId);
                if (taskStatus != null) {
                    taskStatus.setProcessStatus("2"); // 2-已完成
                    taskStatus.setBillCount(billCount);
                    taskStatus.setEndTime(new Date());
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                } else {
                    log.warn("未找到任务状态记录：taskStatusId={}", taskStatusId);
                }
            }
        } catch (Exception e) {
            log.error("更新任务状态为已完成失败：taskStatusId={}, contractNo={}", taskStatusId, contractNo, e);
        }
    }
    
    /**
     * 创建全局任务状态记录（用于标记特殊情况，如无合同等）
     */
    private void createGlobalTaskStatus(LocalDate taskDate, String processStatus, int billCount, String remark) {
        try {
            // 使用特殊的合同编号来标识全局任务状态
            String globalContractCode = "GLOBAL_TASK_" + taskDate.toString().replace("-", "");
            
            // 检查是否已存在全局任务状态记录
            List<BbpmPenaltyTaskStatusVo> taskStatusList = queryTaskStatusList(taskDate, globalContractCode, null);
                
            if (!taskStatusList.isEmpty()) {
                // 更新现有记录
                BbpmPenaltyTaskStatusVo taskStatus = taskStatusList.get(0);
                taskStatus.setProcessStatus(processStatus);
                taskStatus.setBillCount(billCount);
                taskStatus.setEndTime(new Date());
                taskStatus.setErrorMessage(remark);
                penaltyTaskStatusService.updateByIdRecord(taskStatus);
                log.info("已更新全局任务状态记录：{}", globalContractCode);
            } else {
                // 创建新的全局任务状态记录
                BbpmPenaltyTaskStatusVo globalTaskStatus = new BbpmPenaltyTaskStatusVo();
                globalTaskStatus.setTaskDate(taskDate);
                globalTaskStatus.setContractCode(globalContractCode);
                globalTaskStatus.setProcessStatus(processStatus);
                globalTaskStatus.setBillCount(billCount);
                globalTaskStatus.setStartTime(new Date());
                globalTaskStatus.setEndTime(new Date());
                globalTaskStatus.setErrorMessage(remark);
                globalTaskStatus.setDelFlag(1);
                
                penaltyTaskStatusService.insertRecord(globalTaskStatus);
                log.info("已创建全局任务状态记录：{}", globalContractCode);
            }
            
        } catch (Exception e) {
            log.error("创建全局任务状态记录失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 创建任务完成统计（记录到全局任务状态表）
     */
    private void createTaskCompletionSummary(LocalDate taskDate, int totalContracts, int processedContracts) {
        try {
            // 统计各状态的任务数量
            List<BbpmPenaltyTaskStatusVo> taskStatusList = queryTaskStatusList(taskDate, null, null);
                
            int completedCount = 0;
            int failedCount = 0;
            int processingCount = 0;
            
            for (BbpmPenaltyTaskStatusVo task : taskStatusList) {
                String status = task.getProcessStatus();
                if ("2".equals(status)) {
                    completedCount++;
                } else if ("3".equals(status)) {
                    failedCount++;
                } else if ("1".equals(status)) {
                    processingCount++;
                }
            }
            
            // 创建全局任务统计记录
            String summaryMessage = String.format(
                "任务执行统计 - 总合同数:%d, 处理合同数:%d, 已完成:%d, 失败:%d, 仍在处理:%d", 
                totalContracts, processedContracts, completedCount, failedCount, processingCount);
                
            createGlobalTaskStatus(taskDate, "2", totalContracts, summaryMessage);
            log.info(summaryMessage);
            
        } catch (Exception e) {
            log.error("创建任务完成统计失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新所有处理中的任务状态为失败（用于主任务异常处理）
     */
    private void updateAllProcessingTasksToFailed(LocalDate taskDate, String errorMessage) {
        try {
            log.info("开始更新所有处理中的任务状态为失败，任务日期：{}", taskDate);
            
            // 查询所有处理中的任务（状态为1）
            List<BbpmPenaltyTaskStatusVo> processingTasks = queryTaskStatusList(taskDate, null, "1");
                
            if (!processingTasks.isEmpty()) {
                int failedCount = 0;
                for (BbpmPenaltyTaskStatusVo taskStatus : processingTasks) {
                    try {
                        taskStatus.setProcessStatus("3"); // 3-失败
                        taskStatus.setEndTime(new Date());
                        taskStatus.setErrorMessage("主任务异常终止：" + errorMessage);
                        penaltyTaskStatusService.updateByIdRecord(taskStatus);
                        failedCount++;
                    } catch (Exception e) {
                        log.error("更新任务状态失败：taskStatusId={}", taskStatus.getTaskStatusId(), e);
                    }
                }
                log.info("已将 {} 个处理中的任务状态更新为失败", failedCount);
            } else {
                log.info("没有找到处理中的任务需要更新为失败状态");
            }
            
        } catch (Exception e) {
            log.error("批量更新任务状态为失败时发生异常：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新任务状态为失败
     */
    private void updateTaskStatusToFailed(String taskStatusId, String contractNo, String errorMessage, LocalDate taskDate) {
        try {
            if (StringUtils.isNotBlank(taskStatusId)) {
                BbpmPenaltyTaskStatusVo taskStatus = penaltyTaskStatusService.selectByIdRecord(taskStatusId);
                if (taskStatus != null) {
                    taskStatus.setProcessStatus("3"); // 3-失败
                    taskStatus.setEndTime(new Date());
                    taskStatus.setErrorMessage(errorMessage);
                    penaltyTaskStatusService.updateByIdRecord(taskStatus);
                } else {
                    log.warn("未找到任务状态记录：taskStatusId={}", taskStatusId);
                }
            }
        } catch (Exception e) {
            log.error("更新任务状态为失败失败：taskStatusId={}, contractNo={}", taskStatusId, contractNo, e);
        }
    }
    

 
}
