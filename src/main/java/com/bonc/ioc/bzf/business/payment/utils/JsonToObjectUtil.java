package com.bonc.ioc.bzf.business.payment.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bonc.ioc.bzf.business.penalty.vo.contractExtend.*;
import org.apache.commons.lang3.StringUtils;

/**
 *   json转 实体
 */
public class JsonToObjectUtil {

    /**
     * 合同扩展contract_extend转实体
     *
     * @param json
     * @return
     */
    public static ContractOtherInfo jsonToContractOtherInfo(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        ContractOtherInfo contractOtherInfo = JSON.parseObject(json, new TypeReference<ContractOtherInfo>() {
        });
        return contractOtherInfo;
    }

    /**
     * 获取租金优惠政策
     */
    public static FreeVo jsonToRentFreeVo(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        ContractOtherInfo contractOtherInfo = JSON.parseObject(json, new TypeReference<ContractOtherInfo>() {
        });
        if (contractOtherInfo == null) {
            return null;
        }
        return contractOtherInfo.getRentFreeVo();
    }

    /**
     * 获取物业费优惠政策
     */
    public static FreeVo jsonToPropFreeVo(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        ContractOtherInfo contractOtherInfo = JSON.parseObject(json, new TypeReference<ContractOtherInfo>() {
        });
        if (contractOtherInfo == null) {
            return null;
        }
        return contractOtherInfo.getPropFreeVo();
    }

    public static CustomerExtendVo jsonToCustomerExtendVo(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        CustomerExtendVo customerExtendVo = JSON.parseObject(json, new TypeReference<CustomerExtendVo>() {
        });
        return customerExtendVo;
    }





}
