package com.bonc.ioc.bzf.business.penalty.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 接口调用记录表 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@ApiModel(value="BbpmPenaltyApiCallLogPageVo对象", description="接口调用记录表")
public class BbpmPenaltyApiCallLogPageVo extends McpBasePageVo implements Serializable{


    /**
     * 接口调用日志ID
     */
    @ApiModelProperty(value = "接口调用日志ID")
    @NotBlank(message = "接口调用日志ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String apiCallLogId;

    /**
     * 任务状态ID（关联bbpm_penalty_task_status表的task_status_id）
     */
    @ApiModelProperty(value = "任务状态ID（关联bbpm_penalty_task_status表的task_status_id）")
                            private String taskStatusId;

    /**
     * 任务日期
     */
    @ApiModelProperty(value = "任务日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate taskDate;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 账单ID（收款单接口调用时有值）
     */
    @ApiModelProperty(value = "账单ID（收款单接口调用时有值）")
                            private String billId;

    /**
     * 接口类型：1-账单接口，2-收款单接口
     */
    @ApiModelProperty(value = "接口类型：1-账单接口，2-收款单接口")
                            private String apiType;

    /**
     * 接口URL
     */
    @ApiModelProperty(value = "接口URL")
                            private String apiUrl;

    /**
     * 请求参数（JSON格式）
     */
    @ApiModelProperty(value = "请求参数（JSON格式）")
                            private String requestParams;

    /**
     * 响应数据（JSON格式）
     */
    @ApiModelProperty(value = "响应数据（JSON格式）")
                            private String responseData;

    /**
     * 调用耗时（毫秒）
     */
    @ApiModelProperty(value = "调用耗时（毫秒）")
                            private Integer callDuration;

    /**
     * 调用状态：1-成功，2-失败
     */
    @ApiModelProperty(value = "调用状态：1-成功，2-失败")
                            private String callStatus;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
                            private String errorMessage;

    /**
     * 调用时间
     */
    @ApiModelProperty(value = "调用时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date callTime;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private Integer delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * @return 接口调用日志ID
     */
    public String getApiCallLogId() {
        return apiCallLogId;
    }

    public void setApiCallLogId(String apiCallLogId) {
        this.apiCallLogId = apiCallLogId;
    }

    /**
     * @return 任务状态ID（关联bbpm_penalty_task_status表的task_status_id）
     */
    public String getTaskStatusId() {
        return taskStatusId;
    }

    public void setTaskStatusId(String taskStatusId) {
        this.taskStatusId = taskStatusId;
    }

    /**
     * @return 任务日期
     */
    public LocalDate getTaskDate() {
        return taskDate;
    }

    public void setTaskDate(LocalDate taskDate) {
        this.taskDate = taskDate;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 账单ID（收款单接口调用时有值）
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 接口类型：1-账单接口，2-收款单接口
     */
    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    /**
     * @return 接口URL
     */
    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    /**
     * @return 请求参数（JSON格式）
     */
    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    /**
     * @return 响应数据（JSON格式）
     */
    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    /**
     * @return 调用耗时（毫秒）
     */
    public Integer getCallDuration() {
        return callDuration;
    }

    public void setCallDuration(Integer callDuration) {
        this.callDuration = callDuration;
    }

    /**
     * @return 调用状态：1-成功，2-失败
     */
    public String getCallStatus() {
        return callStatus;
    }

    public void setCallStatus(String callStatus) {
        this.callStatus = callStatus;
    }

    /**
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * @return 调用时间
     */
    public Date getCallTime(){
        if(callTime!=null){
            return (Date)callTime.clone();
        }else{
            return null;
        }
    }

    public void setCallTime(Date callTime) {
        if(callTime==null){
            this.callTime = null;
        }else{
            this.callTime = (Date)callTime.clone();
        }
    }

    /**
     * @return 有效标识
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyApiCallLogPageVo{" +
            "apiCallLogId=" + apiCallLogId +
            ", taskStatusId=" + taskStatusId +
            ", taskDate=" + taskDate +
            ", contractCode=" + contractCode +
            ", billId=" + billId +
            ", apiType=" + apiType +
            ", apiUrl=" + apiUrl +
            ", requestParams=" + requestParams +
            ", responseData=" + responseData +
            ", callDuration=" + callDuration +
            ", callStatus=" + callStatus +
            ", errorMessage=" + errorMessage +
            ", callTime=" + callTime +
            ", delFlag=" + delFlag +
            ", projectId=" + projectId +
        "}";
    }
}
