package com.bonc.ioc.bzf.business.penalty.dao;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeDailyDetailEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import java.util.List;

/**
 * 违约金每日计算明细 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Mapper
public interface BbpmPenaltyFeeDailyDetailMapper extends McpBaseMapper<BbpmPenaltyFeeDailyDetailEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change 2025-08-08 by tbh for init
     */
    List<BbpmPenaltyFeeDailyDetailPageResultVo> selectByPageCustom(@Param("vo") BbpmPenaltyFeeDailyDetailPageVo vo );

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    List<BbpmPenaltyFeeDailyDetailVo> selectListByCondition(@Param("vo") BbpmPenaltyFeeDailyDetailVo vo );
}
