package com.bonc.ioc.bzf.business.penalty.vo.contractExtend;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FreeVo {

    @ApiModelProperty(value = "免租期分类，0：无，1：区间，2：固定值")
    private String freePeriodType;

    @ApiModelProperty(value = "免租期固定日期,当免租期分类为2时有效")
    private String fpFixedDate;

    @ApiModelProperty(value = "免租期值（步长）,当免租期分类为2时有效")
    private Integer fpFixedValue;

//    @ApiModelProperty(value = "开始值,当免租期分类为1时有效")
//    private String start;
//
//    @ApiModelProperty(value = "结束值,当免租期分类为1时有效")
//    private String end;
    @ApiModelProperty(value = "免租区间")
    private List<FreeSectionVo> freeSections;

}
