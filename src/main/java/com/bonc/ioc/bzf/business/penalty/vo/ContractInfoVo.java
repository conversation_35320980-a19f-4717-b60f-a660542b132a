package com.bonc.ioc.bzf.business.penalty.vo;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 合同信息VO
 */
@Data
public class ContractInfoVo {
    
    /**
     * 合同号
     */
    private String contractNo;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 每日逾期率
     */
    private BigDecimal dailyOverdueRate;
    
    public ContractInfoVo() {
    }
    
    public ContractInfoVo(String contractNo, String projectId, BigDecimal dailyOverdueRate) {
        this.contractNo = contractNo;
        this.projectId = projectId;
        this.dailyOverdueRate = dailyOverdueRate;
    }
}
