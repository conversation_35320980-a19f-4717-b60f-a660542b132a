package com.bonc.ioc.bzf.business.payment.feign.fallback;


import com.bonc.ioc.bzf.business.payment.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * @ClassName BbctContractManagementFeignClientFallback
 * @Date 2022-12-12 8:52
 **/
public class BbctContractFeignClientFallback implements BbctContractFeignClient {
    @Override
    public AppReply<BbctContractManagementVo> selectByIdNo(String contractNo, String contractId) {
        return null;
    }

    @Override
    public AppReply<PageResult<List<BbctContractManagementPageResultVo>>> selectByPageCommon(BbctContractManagementPageVo vo) {
        return null;
    }

}
