package com.bonc.ioc.bzf.business.penalty.service.impl;

import com.bonc.ioc.bzf.business.payment.utils.ExportExcel;
import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeTrialBillEntity;
import com.bonc.ioc.bzf.business.penalty.dao.BbpmPenaltyFeeTrialBillMapper;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeTrialBillService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金试算账单 服务类实现
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Slf4j
@Service
public class BbpmPenaltyFeeTrialBillServiceImpl extends McpBaseServiceImpl<BbpmPenaltyFeeTrialBillEntity> implements IBbpmPenaltyFeeTrialBillService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmPenaltyFeeTrialBillMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmPenaltyFeeTrialBillService baseService;

    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmPenaltyFeeTrialBillVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmPenaltyFeeTrialBillEntity entity = new BbpmPenaltyFeeTrialBillEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setTrialBillId(null);
        if(!baseService.insert(entity)) {
            log.error("违约金试算账单新增失败:" + entity.toString());
            throw new McpException("违约金试算账单新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getTrialBillId(),1)) {
                log.error("违约金试算账单新增后保存历史失败:" + entity.toString());
                throw new McpException("违约金试算账单新增后保存历史失败");
            }

            log.debug("违约金试算账单新增成功:"+entity.getTrialBillId());
            return entity.getTrialBillId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmPenaltyFeeTrialBillVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmPenaltyFeeTrialBillEntity> entityList = new ArrayList<>();
        for (BbpmPenaltyFeeTrialBillVo item:voList) {
            BbpmPenaltyFeeTrialBillEntity entity = new BbpmPenaltyFeeTrialBillEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmPenaltyFeeTrialBillEntity item:entityList){
            item.setTrialBillId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("违约金试算账单新增失败");
            throw new McpException("违约金试算账单新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmPenaltyFeeTrialBillEntity::getTrialBillId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("违约金试算账单批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("违约金试算账单批量新增后保存历史失败");
            }

            log.debug("违约金试算账单新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param trialBillId 需要删除的试算账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String trialBillId) {
        if(!StringUtils.isEmpty(trialBillId)) {
            if(!baseService.saveOperationHisById(trialBillId,3)) {
                log.error("违约金试算账单删除后保存历史失败:" + trialBillId);
                throw new McpException("违约金试算账单删除后保存历史失败");
            }

            if(!baseService.removeById(trialBillId)) {
                log.error("违约金试算账单删除失败");
                throw new McpException("违约金试算账单删除失败"+trialBillId);
            }
        } else {
            throw new McpException("违约金试算账单删除失败试算账单ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param trialBillIdList 需要删除的试算账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> trialBillIdList) {
        if(!CollectionUtils.isEmpty(trialBillIdList)) {
            int oldSize = trialBillIdList.size();
            trialBillIdList = trialBillIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(trialBillIdList) || oldSize != trialBillIdList.size()) {
                throw new McpException("违约金试算账单批量删除失败 存在主键id为空的记录"+StringUtils.join(trialBillIdList));
            }

            if(!baseService.saveOperationHisByIds(trialBillIdList,3)) {
                log.error("违约金试算账单批量删除后保存历史失败:" + StringUtils.join(trialBillIdList));
                throw new McpException("违约金试算账单批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(trialBillIdList)) {
                log.error("违约金试算账单批量删除失败");
                throw new McpException("违约金试算账单批量删除失败"+StringUtils.join(trialBillIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmPenaltyFeeTrialBillVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeTrialBillEntity entity = new BbpmPenaltyFeeTrialBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getTrialBillId())) {
                throw new McpException("违约金试算账单更新失败传入试算账单ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("违约金试算账单更新失败");
                throw new McpException("违约金试算账单更新失败"+entity.getTrialBillId());
            } else {
                if(!baseService.saveOperationHisById(entity.getTrialBillId(),2)) {
                    log.error("违约金试算账单更新后保存历史失败:" + entity.getTrialBillId());
                    throw new McpException("违约金试算账单更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金试算账单更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmPenaltyFeeTrialBillVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeTrialBillEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeTrialBillVo item:voList){
                BbpmPenaltyFeeTrialBillEntity entity = new BbpmPenaltyFeeTrialBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getTrialBillId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("违约金试算账单批量更新失败 存在试算账单ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("违约金试算账单批量更新失败");
                throw new McpException("违约金试算账单批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getTrialBillId())).map(BbpmPenaltyFeeTrialBillEntity::getTrialBillId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("违约金试算账单批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金试算账单批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmPenaltyFeeTrialBillVo vo) {
        if(vo != null) {
            BbpmPenaltyFeeTrialBillEntity entity = new BbpmPenaltyFeeTrialBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("违约金试算账单保存失败");
                throw new McpException("违约金试算账单保存失败"+entity.getTrialBillId());
            } else {
                if(!baseService.saveOperationHisById(entity.getTrialBillId(),4)) {
                    log.error("违约金试算账单保存后保存历史失败:" + entity.getTrialBillId());
                    throw new McpException("违约金试算账单保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("违约金试算账单保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmPenaltyFeeTrialBillVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmPenaltyFeeTrialBillEntity> entityList = new ArrayList<>();

            for (BbpmPenaltyFeeTrialBillVo item:voList){
                BbpmPenaltyFeeTrialBillEntity entity = new BbpmPenaltyFeeTrialBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("违约金试算账单批量保存失败");
                throw new McpException("违约金试算账单批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getTrialBillId())).map(BbpmPenaltyFeeTrialBillEntity::getTrialBillId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("违约金试算账单批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("违约金试算账单批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param trialBillId 需要查询的试算账单ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmPenaltyFeeTrialBillVo selectByIdRecord(String trialBillId) {
        BbpmPenaltyFeeTrialBillVo vo = new BbpmPenaltyFeeTrialBillVo();

        if(!StringUtils.isEmpty(trialBillId)) {
            BbpmPenaltyFeeTrialBillEntity entity = baseService.selectById(trialBillId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPenaltyFeeTrialBillPageResultVo>> selectByPageRecord(BbpmPenaltyFeeTrialBillPageVo vo) {
        List<BbpmPenaltyFeeTrialBillPageResultVo> result = baseMapper.selectByPageCustom(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);

        return new PageResult(result);
    }

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmPenaltyFeeTrialBillVo> selectListByCondition(BbpmPenaltyFeeTrialBillVo vo) {
        return baseMapper.selectListByCondition(vo);
    }

    @Override
    public void ExportPenaltyFeeTrialBill(BbpmPenaltyFeeTrialBillPageVo vo, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        PageResult<List<BbpmPenaltyFeeTrialBillPageResultVo>> pageResult = baseService.selectByPageRecord(vo);
        if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0  ){
            new ExportExcel("违约金试算账单", BbpmPenaltyFeeTrialBilExportVo.class)
                    .setDataList(pageResult.getRows()).write(response, "违约金试算账单.xlsx").dispose();
        }else {
            throw new McpException("没有需要导出的数据！");
        }

    }

}
