package com.bonc.ioc.bzf.business.penalty.vo.contractExtend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="FreeSectionVo对象", description="免租期区间")
public class FreeSectionVo {

    @ApiModelProperty(value = "签约结果ID")
    private String signInfoId;

    /**
     * 标准类型（rent：租金，prop：物业费）
     */
    @ApiModelProperty(value = "标准类型（rent：租金，prop：物业费）")
    private String standardType;

    /**
     * 开始值
     */
    @ApiModelProperty(value = "开始值")
    private String start;

    /**
     * 结束值
     */
    @ApiModelProperty(value = "结束值")
    private String end;
}
