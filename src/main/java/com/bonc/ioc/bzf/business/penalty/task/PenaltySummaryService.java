package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeStageSummaryService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeTrialBillService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeDailyDetailVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeStageSummaryVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金汇总统计逻辑服务类
 * 负责试算账单汇总、分阶段汇总生成和验证等功能
 */
@Slf4j
@Service
public class PenaltySummaryService {

    @Resource
    private IBbpmPenaltyFeeTrialBillService penaltyFeeTrialBillService;
    
    @Resource
    private IBbpmPenaltyFeeStageSummaryService penaltyFeeStageSummaryService;
    
    @Resource
    private PenaltyDataService penaltyDataService;

    /**
     * 更新试算账单汇总信息
     */
    public void updateTrialBillSummary(String billId, LocalDate calculationDate) {
        try {
            // 查询该账单的所有每日明细记录
            List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = penaltyDataService.selectDailyDetailsByBillId(billId);
            
            if (dailyDetails != null && !dailyDetails.isEmpty()) {
                // 计算违约金总金额（只计算正常计算记录，排除红冲记录）
                BigDecimal totalPenaltyAmount = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType())) // 1-正常计算
                    .map(detail -> detail.getDailyPenaltyAmount() != null ? detail.getDailyPenaltyAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                // 计算逾期天数（从最早计算日期到最晚计算日期）
                LocalDate earliestDate = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType()))
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .min(LocalDate::compareTo)
                    .orElse(calculationDate);
                
                LocalDate latestDate = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType()))
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .max(LocalDate::compareTo)
                    .orElse(calculationDate);
                
                int overdueDays = (int) java.time.temporal.ChronoUnit.DAYS.between(earliestDate, latestDate) + 1;
                
                // 查询试算账单并更新汇总字段
                BbpmPenaltyFeeTrialBillVo trialBill = penaltyDataService.selectTrialBillByBillId(billId);
                if (trialBill != null) {
                    trialBill.setTotalPenaltyAmount(totalPenaltyAmount);
                    trialBill.setOverdueDays(overdueDays);
                    
                    // 更新试算账单
                    penaltyFeeTrialBillService.updateByIdRecord(trialBill);
                    
                    log.debug("更新试算账单汇总信息：{}，违约金总额：{}，逾期天数：{}", 
                             billId, totalPenaltyAmount, overdueDays);
                }
            }
            
        } catch (Exception e) {
            log.error("更新试算账单汇总信息失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 生成分阶段汇总数据（包含验证）
     */
    public void generateStageSummaryWithValidation(String trialBillId) {
        try {
            // 1. 生成分阶段汇总
            generateStageSummary(trialBillId);
            
            // 2. 查询生成的汇总数据
            List<BbpmPenaltyFeeStageSummaryVo> stageSummaries = penaltyDataService.selectStageSummaryByTrialBillId(trialBillId);
            
            // 3. 验证准确性
            validateStageSummary(trialBillId, stageSummaries);
            
            log.info("分阶段汇总生成并验证完成: trialBillId={}, 阶段数={}", trialBillId, stageSummaries.size());
            
        } catch (Exception e) {
            log.error("生成分阶段汇总失败: trialBillId={}", trialBillId, e);
            throw new RuntimeException("生成分阶段汇总失败", e);
        }
    }

    /**
     * 生成分阶段汇总
     */
    public void generateStageSummary(String trialBillId) {
        try {
            // 1. 查询试算账单信息获取billId
            BbpmPenaltyFeeTrialBillVo trialBill = penaltyFeeTrialBillService.selectByIdRecord(trialBillId);
            if (trialBill == null) {
                log.warn("试算账单 {} 不存在，跳过分阶段汇总", trialBillId);
                return;
            }
            
            // 2. 查询该账单的所有每日明细记录
            List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = penaltyDataService.selectDailyDetailsByBillId(trialBill.getBillId());
            
            if (dailyDetails == null || dailyDetails.isEmpty()) {
                log.debug("试算账单 {} 没有每日明细记录，跳过分阶段汇总", trialBillId);
                return;
            }
            
            // 3. 按收款时间（charge_time）分组生成阶段
            Map<LocalDate, List<BbpmPenaltyFeeDailyDetailVo>> stageGroups = dailyDetails.stream()
                .filter(detail -> "1".equals(detail.getEntryType())) // 只考虑正常计算记录
                .collect(Collectors.groupingBy(detail -> {
                    // 按charge_time分组，如果没有charge_time则按calculation_date分组
                    if (detail.getChargeTime() != null) {
                        return detail.getChargeTime(); // 已经是LocalDate类型
                    } else {
                        return detail.getCalculationDate();
                    }
                }));
            
            // 4. 删除现有的分阶段汇总记录
            deleteStageSummaryByTrialBillId(trialBillId);
            
            // 5. 为每个阶段生成汇总记录
            int stageNo = 1;
            for (Map.Entry<LocalDate, List<BbpmPenaltyFeeDailyDetailVo>> entry : stageGroups.entrySet()) {
                LocalDate chargeDate = entry.getKey();
                List<BbpmPenaltyFeeDailyDetailVo> stageDetails = entry.getValue();
                
                // 计算阶段汇总信息
                LocalDate penaltyStartDate = stageDetails.stream()
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .min(LocalDate::compareTo)
                    .orElse(chargeDate);
                
                LocalDate penaltyEndDate = stageDetails.stream()
                    .map(BbpmPenaltyFeeDailyDetailVo::getCalculationDate)
                    .max(LocalDate::compareTo)
                    .orElse(chargeDate);
                
                int overdueDays = (int) java.time.temporal.ChronoUnit.DAYS.between(penaltyStartDate, penaltyEndDate) + 1;
                
                BigDecimal stagePenaltyAmount = stageDetails.stream()
                    .map(detail -> detail.getDailyPenaltyAmount() != null ? detail.getDailyPenaltyAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                BigDecimal unpaidAmount = stageDetails.stream()
                    .map(detail -> detail.getReplacePayAmount() != null ? detail.getReplacePayAmount() : BigDecimal.ZERO)
                    .findFirst() // 取第一个作为欠缴金额
                    .orElse(BigDecimal.ZERO);
                
                BigDecimal dailyOverdueRate = stageDetails.stream()
                    .map(detail -> detail.getDailyOverdueRate() != null ? detail.getDailyOverdueRate() : BigDecimal.ZERO)
                    .findFirst() // 取第一个作为每日逾期率
                    .orElse(BigDecimal.ZERO);
                
                // 构建分阶段汇总记录
                BbpmPenaltyFeeStageSummaryVo stageSummary = new BbpmPenaltyFeeStageSummaryVo();
                stageSummary.setTrialBillId(trialBillId);
                stageSummary.setCurrentStage(String.valueOf(stageNo++));
                stageSummary.setPenaltyStartDate(penaltyStartDate);
                stageSummary.setPenaltyEndDate(penaltyEndDate);
                stageSummary.setOverdueDays(overdueDays);
                stageSummary.setDailyOverdueRate(dailyOverdueRate);
                stageSummary.setUnpaidAmount(unpaidAmount);
                stageSummary.setStagePenaltyAmount(stagePenaltyAmount);
                stageSummary.setDelFlag("1");
                
                // 保存分阶段汇总记录
                penaltyFeeStageSummaryService.insertRecord(stageSummary);
                
                log.debug("生成分阶段汇总：试算账单={}, 阶段={}, 违约金={}, 逾期天数={}", 
                         trialBillId, stageSummary.getCurrentStage(), stagePenaltyAmount, overdueDays);
            }
            
            log.info("试算账单 {} 分阶段汇总生成完成，共 {} 个阶段", trialBillId, stageGroups.size());
            
        } catch (Exception e) {
            log.error("生成分阶段汇总失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 验证分阶段汇总准确性
     */
    public void validateStageSummary(String trialBillId, List<BbpmPenaltyFeeStageSummaryVo> stageSummaries) {
        try {
            if (stageSummaries == null || stageSummaries.isEmpty()) {
                // 查询试算账单下的分阶段汇总记录
                stageSummaries = penaltyDataService.selectStageSummaryByTrialBillId(trialBillId);
                if (stageSummaries.isEmpty()) {
                    log.debug("没有分阶段汇总数据需要验证：{}", trialBillId);
                    return;
                }
            }
            
            // 1. 验证汇总金额是否正确
            BigDecimal totalStagePenalty = stageSummaries.stream()
                .map(stage -> stage.getStagePenaltyAmount() != null ? stage.getStagePenaltyAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 2. 查询试算账单获取billId，然后查询每日明细的汇总金额进行对比
            BbpmPenaltyFeeTrialBillVo trialBill = penaltyFeeTrialBillService.selectByIdRecord(trialBillId);
            if (trialBill != null) {
                List<BbpmPenaltyFeeDailyDetailVo> dailyDetails = penaltyDataService.selectDailyDetailsByBillId(trialBill.getBillId());
                BigDecimal totalDailyPenalty = dailyDetails.stream()
                    .filter(detail -> "1".equals(detail.getEntryType())) // 只计算正常计算记录
                    .map(detail -> detail.getDailyPenaltyAmount() != null ? detail.getDailyPenaltyAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                // 3. 比较金额是否一致
                if (totalStagePenalty.compareTo(totalDailyPenalty) != 0) {
                    log.warn("分阶段汇总金额不一致：试算账单={}, 汇总金额={}, 明细金额={}", 
                            trialBillId, totalStagePenalty, totalDailyPenalty);
                } else {
                    log.debug("分阶段汇总验证通过：试算账单={}, 金额={}", trialBillId, totalStagePenalty);
                }
            }
            
            // 4. 验证阶段连续性
            stageSummaries.sort((a, b) -> Integer.compare(
                Integer.parseInt(a.getCurrentStage()), 
                Integer.parseInt(b.getCurrentStage())
            ));
            
            for (int i = 0; i < stageSummaries.size(); i++) {
                BbpmPenaltyFeeStageSummaryVo stage = stageSummaries.get(i);
                
                // 验证阶段编号
                int expectedStageNo = i + 1;
                if (!String.valueOf(expectedStageNo).equals(stage.getCurrentStage())) {
                    log.warn("分阶段编号不连续：试算账单={}, 期望={}, 实际={}", 
                            trialBillId, expectedStageNo, stage.getCurrentStage());
                }
                
                // 验证日期合理性
                if (stage.getPenaltyStartDate() != null && stage.getPenaltyEndDate() != null) {
                    if (stage.getPenaltyStartDate().isAfter(stage.getPenaltyEndDate())) {
                        log.warn("分阶段日期错误：试算账单={}, 阶段={}, 开始日期={}, 结束日期={}", 
                                trialBillId, stage.getCurrentStage(), stage.getPenaltyStartDate(), stage.getPenaltyEndDate());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("验证分阶段汇总失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 根据trialBillId删除分阶段汇总记录
     */
    public void deleteStageSummaryByTrialBillId(String trialBillId) {
        try {
            // 先查询所有相关的记录
            List<BbpmPenaltyFeeStageSummaryVo> stageSummaries = penaltyDataService.selectStageSummaryByTrialBillId(trialBillId);
            
            // 删除所有记录
            for (BbpmPenaltyFeeStageSummaryVo stageSummary : stageSummaries) {
                penaltyFeeStageSummaryService.removeByIdRecord(stageSummary.getStageSummaryId());
            }
            
            log.debug("删除分阶段汇总记录：trialBillId={}, 删除数量={}", trialBillId, stageSummaries.size());
            
        } catch (Exception e) {
            log.error("删除分阶段汇总记录失败：trialBillId={}", trialBillId, e);
        }
    }
}
