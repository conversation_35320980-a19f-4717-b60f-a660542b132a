package com.bonc.ioc.bzf.business.penalty.service;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyFeeTrialBillEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.bonc.ioc.bzf.business.penalty.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 违约金试算账单 服务类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
public interface IBbpmPenaltyFeeTrialBillService extends IMcpBaseService<BbpmPenaltyFeeTrialBillEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    String insertRecord(BbpmPenaltyFeeTrialBillVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    List<String> insertBatchRecord(List<BbpmPenaltyFeeTrialBillVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param trialBillId 需要删除的试算账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdRecord(String trialBillId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param trialBillIdList 需要删除的试算账单ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void removeByIdsRecord(List<String> trialBillIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateByIdRecord(BbpmPenaltyFeeTrialBillVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void updateBatchByIdRecord(List<BbpmPenaltyFeeTrialBillVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveByIdRecord(BbpmPenaltyFeeTrialBillVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的违约金试算账单
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    void saveBatchByIdRecord(List<BbpmPenaltyFeeTrialBillVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param trialBillId 需要查询的试算账单ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    BbpmPenaltyFeeTrialBillVo selectByIdRecord(String trialBillId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change
     * 2025-08-08 by tbh for init
     */
    PageResult<List<BbpmPenaltyFeeTrialBillPageResultVo>> selectByPageRecord(BbpmPenaltyFeeTrialBillPageVo vo);

    /**
     * selectListByCondition 直接查询（不使用分页框架）
     * @param vo 需要查询的条件
     * @return  查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-01-21
     */
    List<BbpmPenaltyFeeTrialBillVo> selectListByCondition(BbpmPenaltyFeeTrialBillVo vo);

    void ExportPenaltyFeeTrialBill(BbpmPenaltyFeeTrialBillPageVo vo, HttpServletResponse response) throws IOException;
}
