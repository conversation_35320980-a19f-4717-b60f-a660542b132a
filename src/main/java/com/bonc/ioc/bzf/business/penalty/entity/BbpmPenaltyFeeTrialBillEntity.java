package com.bonc.ioc.bzf.business.penalty.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 违约金试算账单 实体类
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@TableName("bbpm_penalty_fee_trial_bill")
@ApiModel(value="BbpmPenaltyFeeTrialBillEntity对象", description="违约金试算账单")
public class BbpmPenaltyFeeTrialBillEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_TRIAL_BILL_ID = "trial_bill_id";
    public static final String FIELD_BILL_ID = "bill_id";
    public static final String FIELD_BILL_CYCLE = "bill_cycle";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_HOUSE_NAME = "house_name";
    public static final String FIELD_TENANT_CODE = "tenant_code";
    public static final String FIELD_TENANT_NAME = "tenant_name";
    public static final String FIELD_CONTRACT_CODE = "contract_code";
    public static final String FIELD_CHARGE_SUBJECT_BEGIN_DATE = "charge_subject_begin_date";
    public static final String FIELD_CHARGE_SUBJECT_END_DATE = "charge_subject_end_date";
    public static final String FIELD_CHARGE_SUBJECT_PERIOD = "charge_subject_period";
    public static final String FIELD_PAYABLE_DATE = "payable_date";
    public static final String FIELD_SHOULD_PAY_AMOUNT = "should_pay_amount";
    public static final String FIELD_PAYED_AMOUNT = "payed_amount";
    public static final String FIELD_REPLACE_PAY_AMOUNT = "replace_pay_amount";
    public static final String FIELD_OVERDUE_DAYS = "overdue_days";
    public static final String FIELD_DAILY_OVERDUE_RATE = "daily_overdue_rate";
    public static final String FIELD_TOTAL_PENALTY_AMOUNT = "total_penalty_amount";
    public static final String FIELD_BILL_CHARGE_SUBJECT = "bill_charge_subject";
    public static final String FIELD_BILL_STATUS = "bill_status";
    public static final String FIELD_ACCOUNT_STATUS = "account_status";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_PROJECT_ID = "project_id";

    /**
     * 试算账单ID
     */
    @ApiModelProperty(value = "试算账单ID")
                                @TableId(value = "trial_bill_id", type = IdType.ASSIGN_UUID)
                                  private String trialBillId;

    /**
     * 账单ID（工银账单ID）
     */
    @ApiModelProperty(value = "账单ID（工银账单ID）")
                            private String billId;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
                            private String billCycle;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 房源名称（房源地址）
     */
    @ApiModelProperty(value = "房源名称（房源地址）")
                            private String houseName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
                            private String tenantCode;

    /**
     * 租户名称（商户名称）
     */
    @ApiModelProperty(value = "租户名称（商户名称）")
                            private String tenantName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate chargeSubjectBeginDate;

    /**
     * 收费科目终止日期
     */
    @ApiModelProperty(value = "收费科目终止日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate chargeSubjectEndDate;

    /**
     * 账单周期数值（从1开始递增）
     */
    @ApiModelProperty(value = "账单周期数值（从1开始递增）")
                            private Integer chargeSubjectPeriod;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private LocalDate payableDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
                            private BigDecimal shouldPayAmount;

    /**
     * 实缴金额
     */
    @ApiModelProperty(value = "实缴金额")
                            private BigDecimal payedAmount;

    /**
     * 待缴金额（未缴金额）
     */
    @ApiModelProperty(value = "待缴金额（未缴金额）")
                            private BigDecimal replacePayAmount;

    /**
     * 逾期天数
     */
    @ApiModelProperty(value = "逾期天数")
                            private Integer overdueDays;

    /**
     * 每日逾期率
     */
    @ApiModelProperty(value = "每日逾期率")
                            private BigDecimal dailyOverdueRate;

    /**
     * 违约金总金额
     */
    @ApiModelProperty(value = "违约金总金额")
                            private BigDecimal totalPenaltyAmount;

    /**
     * 账单对应的收费科目
     */
    @ApiModelProperty(value = "账单对应的收费科目")
                            private String billChargeSubject;

    /**
     * 账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴
     */
    @ApiModelProperty(value = "账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴")
                            private String billStatus;

    /**
     * 对账状态：01-对齐，02-未对齐
     */
    @ApiModelProperty(value = "对账状态：01-对齐，02-未对齐")
                            private String accountStatus;

    /**
     * 状态：1-试算中，2-已完结
     */
    @ApiModelProperty(value = "状态：1-试算中，2-已完结")
                            private String status;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
                            private String delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * @return 试算账单ID
     */
    public String getTrialBillId() {
        return trialBillId;
    }

    public void setTrialBillId(String trialBillId) {
        this.trialBillId = trialBillId;
    }

    /**
     * @return 账单ID（工银账单ID）
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 账单周期
     */
    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 房源名称（房源地址）
     */
    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 租户ID
     */
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * @return 租户名称（商户名称）
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 收费科目起始日期
     */
    public LocalDate getChargeSubjectBeginDate() {
        return chargeSubjectBeginDate;
    }

    public void setChargeSubjectBeginDate(LocalDate chargeSubjectBeginDate) {
        this.chargeSubjectBeginDate = chargeSubjectBeginDate;
    }

    /**
     * @return 收费科目终止日期
     */
    public LocalDate getChargeSubjectEndDate() {
        return chargeSubjectEndDate;
    }

    public void setChargeSubjectEndDate(LocalDate chargeSubjectEndDate) {
        this.chargeSubjectEndDate = chargeSubjectEndDate;
    }

    /**
     * @return 账单周期数值（从1开始递增）
     */
    public Integer getChargeSubjectPeriod() {
        return chargeSubjectPeriod;
    }

    public void setChargeSubjectPeriod(Integer chargeSubjectPeriod) {
        this.chargeSubjectPeriod = chargeSubjectPeriod;
    }

    /**
     * @return 应缴费日期
     */
    public LocalDate getPayableDate() {
        return payableDate;
    }

    public void setPayableDate(LocalDate payableDate) {
        this.payableDate = payableDate;
    }

    /**
     * @return 应缴金额
     */
    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    /**
     * @return 实缴金额
     */
    public BigDecimal getPayedAmount() {
        return payedAmount;
    }

    public void setPayedAmount(BigDecimal payedAmount) {
        this.payedAmount = payedAmount;
    }

    /**
     * @return 待缴金额（未缴金额）
     */
    public BigDecimal getReplacePayAmount() {
        return replacePayAmount;
    }

    public void setReplacePayAmount(BigDecimal replacePayAmount) {
        this.replacePayAmount = replacePayAmount;
    }

    /**
     * @return 逾期天数
     */
    public Integer getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(Integer overdueDays) {
        this.overdueDays = overdueDays;
    }

    /**
     * @return 每日逾期率
     */
    public BigDecimal getDailyOverdueRate() {
        return dailyOverdueRate;
    }

    public void setDailyOverdueRate(BigDecimal dailyOverdueRate) {
        this.dailyOverdueRate = dailyOverdueRate;
    }

    /**
     * @return 违约金总金额
     */
    public BigDecimal getTotalPenaltyAmount() {
        return totalPenaltyAmount;
    }

    public void setTotalPenaltyAmount(BigDecimal totalPenaltyAmount) {
        this.totalPenaltyAmount = totalPenaltyAmount;
    }

    /**
     * @return 账单对应的收费科目
     */
    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    /**
     * @return 账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴
     */
    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     * @return 对账状态：01-对齐，02-未对齐
     */
    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    /**
     * @return 状态：1-试算中，2-已完结
     */
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return 有效标识
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "BbpmPenaltyFeeTrialBillEntity{" +
            "trialBillId=" + trialBillId +
            ", billId=" + billId +
            ", billCycle=" + billCycle +
            ", projectName=" + projectName +
            ", houseName=" + houseName +
            ", tenantCode=" + tenantCode +
            ", tenantName=" + tenantName +
            ", contractCode=" + contractCode +
            ", chargeSubjectBeginDate=" + chargeSubjectBeginDate +
            ", chargeSubjectEndDate=" + chargeSubjectEndDate +
            ", chargeSubjectPeriod=" + chargeSubjectPeriod +
            ", payableDate=" + payableDate +
            ", shouldPayAmount=" + shouldPayAmount +
            ", payedAmount=" + payedAmount +
            ", replacePayAmount=" + replacePayAmount +
            ", overdueDays=" + overdueDays +
            ", dailyOverdueRate=" + dailyOverdueRate +
            ", totalPenaltyAmount=" + totalPenaltyAmount +
            ", billChargeSubject=" + billChargeSubject +
            ", billStatus=" + billStatus +
            ", accountStatus=" + accountStatus +
            ", status=" + status +
            ", delFlag=" + delFlag +
            ", projectId=" + projectId +
        "}";
    }
}