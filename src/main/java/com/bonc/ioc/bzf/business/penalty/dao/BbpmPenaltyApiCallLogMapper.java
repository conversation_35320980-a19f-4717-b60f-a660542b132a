package com.bonc.ioc.bzf.business.penalty.dao;

import com.bonc.ioc.bzf.business.penalty.entity.BbpmPenaltyApiCallLogEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.penalty.vo.*;
import java.util.List;

/**
 * 接口调用记录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 * @change 2025-08-08 by tbh for init
 */
@Mapper
public interface BbpmPenaltyApiCallLogMapper extends McpBaseMapper<BbpmPenaltyApiCallLogEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-08-08
     * @change 2025-08-08 by tbh for init
     */
    List<BbpmPenaltyApiCallLogPageResultVo> selectByPageCustom(@Param("vo") BbpmPenaltyApiCallLogPageVo vo );
}
