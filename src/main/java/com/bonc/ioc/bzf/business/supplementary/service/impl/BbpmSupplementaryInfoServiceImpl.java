package com.bonc.ioc.bzf.business.supplementary.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.bonc.ioc.bzf.business.adjust.utils.RequestUtil;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbsiRuleInfoService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRuleSubParamsVo;
import com.bonc.ioc.bzf.business.payment.vo.TaxRateResultVo;
import com.bonc.ioc.bzf.business.payment.vo.TaxRateVo;
import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryInfoEntity;
import com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryInfoMapper;
import com.bonc.ioc.bzf.business.supplementary.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.AreaTypeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.BaseRentCalculateEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.CustomerTypeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.DelFlagEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.PayModeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.PayTypeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.PaymentTypeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.ProductTypeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.SignTypeEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.SupplementaryChargeStandardEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.SupplementaryStatusEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.WhetherEnum;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmApproveInfoService;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryInfoService;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryPaymentService;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryPreviewBillService;
import com.bonc.ioc.bzf.utils.common.other.DateUtils;
import com.bonc.ioc.bzf.utils.common.other.PercentUtil;
import com.bonc.ioc.bzf.utils.common.user.UserUtil;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加单表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Slf4j
@Service
public class BbpmSupplementaryInfoServiceImpl extends McpBaseServiceImpl<BbpmSupplementaryInfoEntity> implements IBbpmSupplementaryInfoService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmSupplementaryInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmSupplementaryInfoService baseService;

    /**
     * 追加账单相关 服务实例
     */
    @Resource
    private IBbpmSupplementaryPaymentService supplementaryPaymentService;

    /**
     * 审批相关 服务实例
     */
    @Resource
    private IBbpmApproveInfoService approveInfoService;

    @Resource
    private IBbsiRuleInfoService iBbsiRuleInfoService;

    /**
     * 追加单试算相关 服务实例
     */
    @Resource
    private IBbpmSupplementaryPreviewBillService supplementaryPreviewBillService;

    /**
     * 用户相关 服务实例
     */
    @Resource
    private UserUtil userUtil;

    /**
     * 字典相关 session实例
     */
    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private RestTemplateUtil restTemplateUtil;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;


    @Value("${yecai.url}")
    private String yecaiUrl;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbpmSupplementaryInfoVo vo) {
        if (vo == null) {
            return null;
        }

        BbpmSupplementaryInfoEntity entity = new BbpmSupplementaryInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSupplementaryId(null);
        if (!baseService.insert(entity)) {
            log.error("追加单表新增失败:" + entity.toString());
            throw new McpException("追加单表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getSupplementaryId(), 1)) {
                log.error("追加单表新增后保存历史失败:" + entity.toString());
                throw new McpException("追加单表新增后保存历史失败");
            }

            log.debug("追加单表新增成功:" + entity.getSupplementaryId());
            return entity.getSupplementaryId();
        }
    }

    /**
     * 新增追加单信息
     *
     * @param vo 追加单信息 vo实体
     * @return 主键id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertSupplementaryInfo(BbpmSupplementaryInfoVo vo) {
        // 是否有在途追加账单校验
        Integer doingSize = baseMapper.selectDoingSupplementaryInfoSize(vo.getContractNo());
        if (!Objects.isNull(doingSize) && doingSize > 0) {
            throw new McpException("当前合同存在进行中的追加账单申请，不支持继续发起追加账单");
        }
        // 赋值默认值
        vo.setSupplementaryStatus(SupplementaryStatusEnum.TEMPORARY.getCode());
        vo.setCreateUserName(userUtil.getUserName());
        vo.setSupplementaryCode(createSupplementaryCode(vo));
        vo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        // 新增追加单信息
        String supplementaryId = insertRecord(vo);
        // 新增追加账单信息
        List<BbpmSupplementaryPaymentVo> supplementaryPaymentList = vo.getSupplementaryPaymentList();
        if (CollectionUtils.isNotEmpty(supplementaryPaymentList)) {
            for (BbpmSupplementaryPaymentVo supplementaryPaymentVo : supplementaryPaymentList) {
                supplementaryPaymentVo.setParentId(supplementaryId);
                supplementaryPaymentVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
            }
            supplementaryPaymentService.insertBatchAndProduct(supplementaryPaymentList);
        }
        return supplementaryId;
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmSupplementaryInfoVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmSupplementaryInfoEntity> entityList = new ArrayList<>();
        for (BbpmSupplementaryInfoVo item : voList) {
            BbpmSupplementaryInfoEntity entity = new BbpmSupplementaryInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmSupplementaryInfoEntity item : entityList) {
            item.setSupplementaryId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("追加单表新增失败");
            throw new McpException("追加单表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmSupplementaryInfoEntity::getSupplementaryId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("追加单表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("追加单表批量新增后保存历史失败");
            }

            log.debug("追加单表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param supplementaryId 需要删除的追加单id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String supplementaryId) {
        if (!StringUtils.isEmpty(supplementaryId)) {
            if (!baseService.saveOperationHisById(supplementaryId, 3)) {
                log.error("追加单表删除后保存历史失败:" + supplementaryId);
                throw new McpException("追加单表删除后保存历史失败");
            }

            if (!baseService.removeById(supplementaryId)) {
                log.error("追加单表删除失败");
                throw new McpException("追加单表删除失败" + supplementaryId);
            }
        } else {
            throw new McpException("追加单表删除失败追加单id为空");
        }
    }

    /**
     * 删除追加单信息
     *
     * @param supplementaryId 追加单id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeSupplementaryInfo(String supplementaryId) {
        // 删除追加单信息
        removeByIdRecord(supplementaryId);
        // 删除追加账单信息
        supplementaryPaymentService.removeByParentId(supplementaryId);
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param supplementaryIdList 需要删除的追加单id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> supplementaryIdList) {
        if (!CollectionUtils.isEmpty(supplementaryIdList)) {
            int oldSize = supplementaryIdList.size();
            supplementaryIdList = supplementaryIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(supplementaryIdList) || oldSize != supplementaryIdList.size()) {
                throw new McpException("追加单表批量删除失败 存在主键id为空的记录" + StringUtils.join(supplementaryIdList));
            }

            if (!baseService.saveOperationHisByIds(supplementaryIdList, 3)) {
                log.error("追加单表批量删除后保存历史失败:" + StringUtils.join(supplementaryIdList));
                throw new McpException("追加单表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(supplementaryIdList)) {
                log.error("追加单表批量删除失败");
                throw new McpException("追加单表批量删除失败" + StringUtils.join(supplementaryIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmSupplementaryInfoVo vo) {
        if (vo != null) {
            BbpmSupplementaryInfoEntity entity = new BbpmSupplementaryInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getSupplementaryId())) {
                throw new McpException("追加单表更新失败传入追加单id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("追加单表更新失败");
                throw new McpException("追加单表更新失败" + entity.getSupplementaryId());
            } else {
                if (!baseService.saveOperationHisById(entity.getSupplementaryId(), 2)) {
                    log.error("追加单表更新后保存历史失败:" + entity.getSupplementaryId());
                    throw new McpException("追加单表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加单表更新失败传入为空");
        }
    }

    /**
     * 更新追加单信息
     *
     * @param vo 追加单信息 vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateSupplementaryInfo(BbpmSupplementaryInfoVo vo) {
        // 更新追加单信息
        updateByIdRecord(vo);
        // 清空追加账单信息
        supplementaryPaymentService.deleteByParentId(vo.getSupplementaryId());
        // 新增追加账单信息
        List<BbpmSupplementaryPaymentVo> supplementaryPaymentList = vo.getSupplementaryPaymentList();
        if (CollectionUtils.isNotEmpty(supplementaryPaymentList)) {
            for (BbpmSupplementaryPaymentVo supplementaryPaymentVo : supplementaryPaymentList) {
                supplementaryPaymentVo.setParentId(vo.getSupplementaryId());
                supplementaryPaymentVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
            }
            supplementaryPaymentService.insertBatchAndProduct(supplementaryPaymentList);
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmSupplementaryInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryInfoEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryInfoVo item : voList) {
                BbpmSupplementaryInfoEntity entity = new BbpmSupplementaryInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getSupplementaryId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("追加单表批量更新失败 存在追加单id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("追加单表批量更新失败");
                throw new McpException("追加单表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getSupplementaryId())).map(BbpmSupplementaryInfoEntity::getSupplementaryId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("追加单表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加单表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmSupplementaryInfoVo vo) {
        if (vo != null) {
            BbpmSupplementaryInfoEntity entity = new BbpmSupplementaryInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("追加单表保存失败");
                throw new McpException("追加单表保存失败" + entity.getSupplementaryId());
            } else {
                if (!baseService.saveOperationHisById(entity.getSupplementaryId(), 4)) {
                    log.error("追加单表保存后保存历史失败:" + entity.getSupplementaryId());
                    throw new McpException("追加单表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加单表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmSupplementaryInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryInfoEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryInfoVo item : voList) {
                BbpmSupplementaryInfoEntity entity = new BbpmSupplementaryInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("追加单表批量保存失败");
                throw new McpException("追加单表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getSupplementaryId())).map(BbpmSupplementaryInfoEntity::getSupplementaryId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("追加单表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加单表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param supplementaryId 需要查询的追加单id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmSupplementaryInfoVo selectByIdRecord(String supplementaryId) {
        BbpmSupplementaryInfoVo vo = new BbpmSupplementaryInfoVo();

        if (!StringUtils.isEmpty(supplementaryId)) {
            BbpmSupplementaryInfoEntity entity = baseService.selectById(supplementaryId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 查询追加单信息
     *
     * @param supplementaryId 追加单id
     * @return 追加单信息
     */
    @Override
    public BbpmSupplementaryInfoVo selectSupplementaryInfo(String supplementaryId) {
        BbpmSupplementaryInfoVo supplementaryInfoVo = selectByIdRecord(supplementaryId);
        if (!Objects.isNull(supplementaryInfoVo)) {
            supplementaryInfoVo.setSupplementaryPaymentList(supplementaryPaymentService.selectListByParentId(supplementaryId));
        }
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(supplementaryInfoVo);
        return supplementaryInfoVo;
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmSupplementaryInfoPageResultVo>> selectByPageRecord(BbpmSupplementaryInfoPageVo vo) {
        vo.setProjectIdStr(RequestUtil.getProjects());
        vo.setExistApprove(WhetherEnum.NO.getCode());
        List<BbpmSupplementaryInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * 分页查询追加单列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单列表
     */
    @Override
    public PageResult<List<BbpmSupplementaryInfoPageResultVo>> selectSupplementaryInfoPage(BbpmSupplementaryInfoPageVo vo) {
        vo.setProjectIdStr(RequestUtil.getProjects());
        vo.setExistApprove(WhetherEnum.NO.getCode());
        List<BbpmSupplementaryInfoPageResultVo> result = baseMapper.selectSupplementaryInfoPage(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * 提交
     *
     * @param supplementaryId 追加单id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void submit(String supplementaryId) {
        List<String> approveStatusList = new ArrayList<>();
        approveStatusList.add(SupplementaryStatusEnum.TEMPORARY.getCode());
        approveStatusList.add(SupplementaryStatusEnum.NO_PASS.getCode());
        boolean updateSuccess = new LambdaUpdateChainWrapper<>(baseMapper)
                .set(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.WAIT_AUDIT.getCode())
                .eq(BbpmSupplementaryInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .in(BbpmSupplementaryInfoEntity::getSupplementaryStatus, approveStatusList)
                .eq(BbpmSupplementaryInfoEntity::getSupplementaryId, supplementaryId)
                .update();
        if (!updateSuccess) {
            log.error(String.format("提交失败数据不存在或不是暂存状态[supplementaryId: %s]", supplementaryId));
            throw new McpException("提交失败数据不存在或不是暂存状态");
        }
        //approveInfoService.submit(supplementaryId, "1");
    }

    /**
     * 撤回
     *
     * @param supplementaryId 追加单id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void cancel(String supplementaryId) {
        boolean updateSuccess = new LambdaUpdateChainWrapper<>(baseMapper)
                .set(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.TEMPORARY.getCode())
                .eq(BbpmSupplementaryInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.WAIT_AUDIT.getCode())
                .eq(BbpmSupplementaryInfoEntity::getSupplementaryId, supplementaryId)
                .update();
        if (!updateSuccess) {
            log.error(String.format("撤回失败数据不存在或不是待审核状态[supplementaryId: %s]", supplementaryId));
            throw new McpException("撤回失败数据不存在或不是待审核状态");
        }
        approveInfoService.cancel(supplementaryId, "1");
    }

    /**
     * 处理审核
     *
     * @param vo 审批 vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void dealApprove(BbpmApproveVo vo) {
        if (ApproveStatusEnum.APPROVED.getCode().equals(vo.getStatus())) {
            BbpmSupplementaryInfoVo supplementaryInfoVo = selectSupplementaryInfo(vo.getId());
            if (Objects.isNull(supplementaryInfoVo)) {
                log.error(String.format("处理审核失败数据不存在[supplementaryId: %s]", vo.getId()));
                throw new McpException("处理审核失败数据不存在");
            }
            boolean updateSuccess = new LambdaUpdateChainWrapper<>(baseMapper)
                    .set(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.SUCCESS.getCode())
                    .eq(BbpmSupplementaryInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    //.eq(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.WAIT_AUDIT.getCode())
                    .eq(BbpmSupplementaryInfoEntity::getSupplementaryId, vo.getId())
                    .update();
            if (!updateSuccess) {
                log.error(String.format("处理审核失败数据不存在或不是待审核状态[supplementaryId: %s]", vo.getId()));
                throw new McpException("处理审核失败数据不存在或不是待审核状态");
            }
            // 存储试算结果
            BbpmAppendBillVo appendBillVo = createAppendBill(supplementaryInfoVo, vo.getContractManagementVo());
            List<BbpmSupplementaryPreviewBillVo> previewBillList = tryAppendBill(appendBillVo, vo.getId());
            supplementaryPreviewBillService.insertBatchRecord(previewBillList);
            // 推送账单
            appendBill(createAppendBill(supplementaryInfoVo, vo.getContractManagementVo()));
        } else if (ApproveStatusEnum.UNAPPROVED.getCode().equals(vo.getStatus())) {
            boolean updateSuccess = new LambdaUpdateChainWrapper<>(baseMapper)
                    .set(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.NO_PASS.getCode())
                    .eq(BbpmSupplementaryInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
//                    .eq(BbpmSupplementaryInfoEntity::getSupplementaryStatus, SupplementaryStatusEnum.WAIT_AUDIT.getCode())
                    .eq(BbpmSupplementaryInfoEntity::getSupplementaryId, vo.getId())
                    .update();
            if (!updateSuccess) {
                log.error(String.format("处理审核失败数据不存在或不是待审核状态[supplementaryId: %s]", vo.getId()));
                throw new McpException("处理审核失败数据不存在或不是待审核状态");
            }
        } else {
            throw new McpException(String.format("处理审核失败[supplementaryId: %s]", vo.getId()));
        }
        //BbpmRemarkVo remarkVo = new BbpmRemarkVo();
        //BeanUtils.copyProperties(vo, remarkVo);
        //remarkVo.setDate(new Date());
        //approveInfoService.dealApprove(remarkVo, "1", vo.getStatus());
    }

    /**
     * 分页查询追加单审批列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单列表
     */
    @Override
    public PageResult<List<BbpmSupplementaryInfoPageResultVo>> selectApproveInfoPage(BbpmSupplementaryInfoPageVo vo) {
        vo.setProjectIdStr(RequestUtil.getProjects());
        vo.setExistApprove(WhetherEnum.YES.getCode());
        List<BbpmSupplementaryInfoPageResultVo> result = baseMapper.selectSupplementaryInfoPage(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * 追加单信息统计
     *
     * @return 追加单信息统计
     */
    @Override
    public BbpmSupplementaryStatisticVo supplementaryInfoStatistic() {
        BbpmSupplementaryStatisticVo statisticVo = new BbpmSupplementaryStatisticVo();
        BbpmSupplementaryInfoPageVo vo = new BbpmSupplementaryInfoPageVo();
        vo.setProjectIdStr(RequestUtil.getProjects());
        vo.setSupplementaryStatusStr("1,2");
        statisticVo.setDbSize(baseMapper.statistic(vo));
        vo.setSupplementaryStatusStr("3,4");
        statisticVo.setYbSize(baseMapper.statistic(vo));
        return statisticVo;
    }

    /**
     * 追加单审批信息统计
     *
     * @return 追加单审批信息统计
     */
    @Override
    public BbpmSupplementaryStatisticVo approveInfoStatistic() {
        BbpmSupplementaryStatisticVo statisticVo = new BbpmSupplementaryStatisticVo();
        BbpmSupplementaryInfoPageVo vo = new BbpmSupplementaryInfoPageVo();
        vo.setExistApprove(WhetherEnum.YES.getCode());
        vo.setProjectIdStr(RequestUtil.getProjects());
        vo.setSupplementaryStatusStr("3");
        statisticVo.setDbSize(baseMapper.statistic(vo));
        vo.setSupplementaryStatusStr("1,2,4");
        statisticVo.setYbSize(baseMapper.statistic(vo));
        return statisticVo;
    }

    /**
     * 获取操作记录列表
     *
     * @param supplementaryId 追加单id
     * @return 操作记录列表
     */
    @Override
    public List<BbpmApproveDetailInfoVo> operateDetailInfo(String supplementaryId) {
        return approveInfoService.operateDetailInfo(supplementaryId, "1");
    }

    /**
     * 获取账单明细
     *
     * @param vo 请求参数 vo实体
     * @return 账单明细
     */
    @Override
    public Map<String, List<BbpmSupplementaryPreviewBillVo>> getPreviewBill(BbpmSupplementaryInfoVo vo) {
        if (StringUtils.isBlank(vo.getSupplementaryId())) {
            BbpmAppendBillVo appendBillVo = createAppendBill(vo, vo.getContractManagementVo());
            List<BbpmSupplementaryPreviewBillVo> previewBillList = tryAppendBill(appendBillVo, null);
            setChargeSubjectPeriodStr(previewBillList);
            return createPreviewBillMap(previewBillList);
        } else {
            BbpmSupplementaryInfoVo supplementaryInfoVo = selectSupplementaryInfo(vo.getSupplementaryId());
            if (SupplementaryStatusEnum.SUCCESS.getCode().equals(supplementaryInfoVo.getSupplementaryStatus())) {
                // 查本地库
                List<BbpmSupplementaryPreviewBillVo> previewBillList = supplementaryPreviewBillService.selectListByParentId(vo.getSupplementaryId());
                setChargeSubjectPeriodStr(previewBillList);
                return createPreviewBillMap(previewBillList);
            } else {
                // 试算
                BbpmAppendBillVo appendBillVo = createAppendBill(supplementaryInfoVo, vo.getContractManagementVo());
                List<BbpmSupplementaryPreviewBillVo> previewBillList = tryAppendBill(appendBillVo, null);
                setChargeSubjectPeriodStr(previewBillList);
                return createPreviewBillMap(previewBillList);
            }
        }

    }

    /**
     * 获取导出数据
     *
     * @param vo 请求参数 vo实体
     * @return 导出数据
     */
    @Override
    public List<BbpmSupplementaryExportVo> getExportData(BbpmSupplementaryInfoPageVo vo) {
        return supplementaryPreviewBillService.getExportData(vo);
    }

    /**
     * 生成追加单号
     *
     * @param vo 追加单信息 vo实体
     * @return 追加单号
     */
    private String createSupplementaryCode(BbpmSupplementaryInfoVo vo) {
        String supplementaryCode = "ZJZD";
        String formattedDate = DateUtils.getDate("yyyyMMdd");
        String serialStr = baseMapper.selectSupplementaryCodeSerial(formattedDate);
        if (StringUtils.isBlank(serialStr)) {
            serialStr = "0";
        }
        int serial = Integer.parseInt(serialStr);
        serial++;
        return supplementaryCode + formattedDate + String.format("%04d", serial);
    }

    /**
     * 构建追加账单推送报文
     *
     * @param supplementaryInfoVo  追加单信息 vo实体
     * @param contractManagementVo 合同信息 vo实体
     * @return 追加账单推送报文
     */
    private BbpmAppendBillVo createAppendBill(BbpmSupplementaryInfoVo supplementaryInfoVo,
                                              BbctContractManagementVo contractManagementVo) {
        BbpmAppendBillVo appendBillVo = new BbpmAppendBillVo();
        appendBillVo.setContractId(supplementaryInfoVo.getContractNo());
        appendBillVo.setProjectId(supplementaryInfoVo.getProjectId());
        if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(supplementaryInfoVo.getProductType())){
            appendBillVo.setProjectFormat(PaymentEnums.PROJECTFORMAT_SY.getCode());
        }
        appendBillVo.setRoomList(createRoomList(supplementaryInfoVo, contractManagementVo));
        return appendBillVo;
    }

    /**
     * 构建追加账单推送产品相关报文
     *
     * @param supplementaryInfoVo  追加单信息 vo实体
     * @param contractManagementVo 合同信息 vo实体
     * @return 追加账单推送产品相关报文
     */
    private List<BbpmAppendBillRoomVo> createRoomList(BbpmSupplementaryInfoVo supplementaryInfoVo,
                                                      BbctContractManagementVo contractManagementVo) {
        List<BbpmAppendBillRoomVo> roomList = new ArrayList<>();
        if (SignTypeEnum.WHOLESALE_RENTAL.getCode().equals(supplementaryInfoVo.getSignType())) {
            List<BbpmSupplementaryPaymentVo> paymentList = supplementaryInfoVo.getSupplementaryPaymentList();
            for (BbpmSupplementaryPaymentVo paymentVo : paymentList) {
                List<BbpmSupplementaryPaymentProductVo> productList = paymentVo.getSupplementaryPaymentProductList();
                for (BbpmSupplementaryPaymentProductVo productVo : productList) {
                    roomList.add(createMultipleRoomVo(supplementaryInfoVo, paymentVo, productVo, contractManagementVo));
                }
            }


        } else if (SignTypeEnum.PERSONAL_RENTAL.getCode().equals(supplementaryInfoVo.getSignType()) ||
                SignTypeEnum.WHOLESALE_AGREEMENT.getCode().equals(supplementaryInfoVo.getSignType())) {
            roomList.add(createOneRoomVo(supplementaryInfoVo, contractManagementVo));
        } else {
            throw new McpException(String.format("不支持的签约类型[signType: %s]", supplementaryInfoVo.getSignType()));
        }
        return roomList;
    }

    /**
     * 构建追加账单推送产品报文(趸租)
     *
     * @param supplementaryInfoVo  追加单信息 vo实体
     * @param paymentVo            追加账单信息 vo实体
     * @param productVo            产品信息 vo实体
     * @param contractManagementVo 合同信息 vo实体
     * @return 追加账单推送产品相关报文
     */
    private BbpmAppendBillRoomVo createMultipleRoomVo(BbpmSupplementaryInfoVo supplementaryInfoVo,
                                                      BbpmSupplementaryPaymentVo paymentVo,
                                                      BbpmSupplementaryPaymentProductVo productVo,
                                                      BbctContractManagementVo contractManagementVo) {
        BbpmAppendBillRoomVo roomVo = new BbpmAppendBillRoomVo();
        roomVo.setRoomNo(productVo.getProductNo());
        List<ChargeSubjectParamsRequest> chargeSubjectList = new ArrayList<>();
        Date chargeStartDate;
        Date chargeEndDate;
        String cyclicOrSingle;
        String chargeSubjectPeriod;
        String chargeRuleNo = null;
        String chargeRuleName = null;
        JSONObject paramList = null;
        JSONObject paramValueList = null;
        String rentPercent = null;
        String amountType = null;
        BigDecimal chargeSubjectAmount = null;
        if (PaymentTypeEnum.PERIODICITY_PAYMENT.getCode().equals(paymentVo.getPaymentType())) {
            // 周期性账单
            chargeStartDate = Date.from(paymentVo.getPaymentBeginTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            chargeEndDate = Date.from(paymentVo.getPaymentEndTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            cyclicOrSingle = "01";
            chargeSubjectPeriod = paymentVo.getPaymentCycleCode();
            if (SupplementaryChargeStandardEnum.UNIT_PRICE.getCode().equals(paymentVo.getChargeStandardCode())) {
                chargeRuleNo = BaseRentCalculateEnum.UNIT_PRICE_RIDE_AREA.getCode();
                chargeRuleName = BaseRentCalculateEnum.UNIT_PRICE_RIDE_AREA.getDesc();
                paramList = (JSONObject) JSONObject.toJSON(new ChargeRuleSubParamsVo());
                ChargeRuleSubParamsVo paramsVo = new ChargeRuleSubParamsVo();
                paramsVo.setPARAMPRICE(paymentVo.getChargeStandardMoney());
                paramsVo.setPARAMAREA(getArea(contractManagementVo.getAreaType(), contractManagementVo.getSubjectMatterList().get(0)));
                paramValueList = (JSONObject) JSONObject.toJSON(paramsVo);
                if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(supplementaryInfoVo.getProductType())){
                    amountType = paymentVo.getAmountType().length() == 1 ? "0" + paymentVo.getAmountType() : paymentVo.getAmountType();
                }
            } else if (SupplementaryChargeStandardEnum.RENT_STANDARD.getCode().equals(paymentVo.getChargeStandardCode())) {
                chargeRuleNo = BaseRentCalculateEnum.FIXED_PRICE.getCode();
                chargeRuleName = BaseRentCalculateEnum.FIXED_PRICE.getDesc();
                paramList = (JSONObject) JSONObject.toJSON(new ChargeRuleSubParamsVo());
                ChargeRuleSubParamsVo paramsVo = new ChargeRuleSubParamsVo();
                paramsVo.setPARAMPRICE(paymentVo.getChargeStandardMoney());
                paramValueList = (JSONObject) JSONObject.toJSON(paramsVo);
            } else if (SupplementaryChargeStandardEnum.PERCENT.getCode().equals(paymentVo.getChargeStandardCode())) {
                rentPercent = PercentUtil.parsePercentToDouble(paymentVo.getChargeStandardPercent());
            }
        } else {
            // 一次性账单
            chargeStartDate = Date.from(paymentVo.getSupplementaryTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            chargeEndDate = Date.from(paymentVo.getSupplementaryTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            cyclicOrSingle = "02";
            chargeSubjectPeriod = "01";
            //商业
            if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(supplementaryInfoVo.getProductType())){
                 chargeSubjectAmount = BigDecimal.valueOf(Double.parseDouble(paymentVo.getExpenseItemMoney()));
            }else{
                chargeRuleNo = BaseRentCalculateEnum.FIXED_PRICE.getCode();
                chargeRuleName = BaseRentCalculateEnum.FIXED_PRICE.getDesc();
                paramList = (JSONObject) JSONObject.toJSON(new ChargeRuleSubParamsVo());
                ChargeRuleSubParamsVo paramsVo = new ChargeRuleSubParamsVo();
                paramsVo.setPARAMPRICE(paymentVo.getExpenseItemMoney());
                paramValueList = (JSONObject) JSONObject.toJSON(paramsVo);
            }
        }
        BbpmTaxRateVo taxRateVo = createTaxRate(supplementaryInfoVo, paymentVo, contractManagementVo);
        ChargeSubjectParamsRequest chargeSubject = ChargeSubjectParamsRequest.builder()
                .chargeStartDate(chargeStartDate)
                .chargeEndDate(chargeEndDate)
                .noCompleteType("01")
                .chargeSubjectAmount(chargeSubjectAmount)
                .chargeSubjectNo(paymentVo.getExpenseItem())
                .amountType(amountType)
                .taxRate(taxRateVo.getTaxRate())
                .personalTaxRate(taxRateVo.getPersonalTaxRate())
                .cyclicOrSingle(cyclicOrSingle)
                .chargeSubjectPeriod(chargeSubjectPeriod)
                .depositProportion(1)
                .chargeRuleNo(chargeRuleNo)
                .chargeRuleName(chargeRuleName)
                .paramList(paramList)
                .paramValueList(paramValueList)
                .hasServiceFee(0)
                .monthlyRentServiceFeeRatio(rentPercent)
                .shareType("3")
                .build();
        chargeSubjectList.add(chargeSubject);
        roomVo.setChargeSubjectList(chargeSubjectList);
        return roomVo;
    }

    /**
     * 构建追加账单推送产品报文(散租、管理协议)
     *
     * @param supplementaryInfoVo  追加单信息 vo实体
     * @param contractManagementVo 合同信息 vo实体
     * @return 追加账单推送产品相关报文
     */
    private BbpmAppendBillRoomVo createOneRoomVo(BbpmSupplementaryInfoVo supplementaryInfoVo,
                                                 BbctContractManagementVo contractManagementVo) {
        BbpmAppendBillRoomVo roomVo = new BbpmAppendBillRoomVo();
        roomVo.setRoomNo(contractManagementVo.getSubjectMatterList().get(0).getProductNo());
        List<BbpmSupplementaryPaymentVo> paymentList = supplementaryInfoVo.getSupplementaryPaymentList();
        List<ChargeSubjectParamsRequest> chargeSubjectList = new ArrayList<>();
        for (BbpmSupplementaryPaymentVo paymentVo : paymentList) {
            Date chargeStartDate;
            Date chargeEndDate;
            String cyclicOrSingle;
            String chargeSubjectPeriod;
            String chargeRuleNo = null;
            String chargeRuleName = null;
            JSONObject paramList = null;
            JSONObject paramValueList = null;
            String rentPercent = null;
            String amountType = null;
            BigDecimal chargeSubjectAmount = null;
            if (PaymentTypeEnum.PERIODICITY_PAYMENT.getCode().equals(paymentVo.getPaymentType())) {
                // 周期性账单
                chargeStartDate = Date.from(paymentVo.getPaymentBeginTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
                chargeEndDate = Date.from(paymentVo.getPaymentEndTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
                cyclicOrSingle = "01";
                chargeSubjectPeriod = paymentVo.getPaymentCycleCode();
                if (SupplementaryChargeStandardEnum.UNIT_PRICE.getCode().equals(paymentVo.getChargeStandardCode())) {
                    chargeRuleNo = BaseRentCalculateEnum.UNIT_PRICE_RIDE_AREA.getCode();
                    chargeRuleName = BaseRentCalculateEnum.UNIT_PRICE_RIDE_AREA.getDesc();
                    paramList = (JSONObject) JSONObject.toJSON(new ChargeRuleSubParamsVo());
                    ChargeRuleSubParamsVo paramsVo = new ChargeRuleSubParamsVo();
                    paramsVo.setPARAMPRICE(paymentVo.getChargeStandardMoney());
                    paramsVo.setPARAMAREA(getArea(contractManagementVo.getAreaType(), contractManagementVo.getSubjectMatterList().get(0)));
                    paramValueList = (JSONObject) JSONObject.toJSON(paramsVo);
                    if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(supplementaryInfoVo.getProductType())){
                        amountType = paymentVo.getAmountType().length() == 1 ? "0" + paymentVo.getAmountType() : paymentVo.getAmountType();
                    }
                } else if (SupplementaryChargeStandardEnum.RENT_STANDARD.getCode().equals(paymentVo.getChargeStandardCode())) {
                    chargeRuleNo = BaseRentCalculateEnum.FIXED_PRICE.getCode();
                    chargeRuleName = BaseRentCalculateEnum.FIXED_PRICE.getDesc();
                    paramList = (JSONObject) JSONObject.toJSON(new ChargeRuleSubParamsVo());
                    ChargeRuleSubParamsVo paramsVo = new ChargeRuleSubParamsVo();
                    paramsVo.setPARAMPRICE(paymentVo.getChargeStandardMoney());
                    paramValueList = (JSONObject) JSONObject.toJSON(paramsVo);
                } else if (SupplementaryChargeStandardEnum.PERCENT.getCode().equals(paymentVo.getChargeStandardCode())) {
                    rentPercent = PercentUtil.parsePercentToDouble(paymentVo.getChargeStandardPercent());
                }
            } else {
                // 一次性账单
                chargeStartDate = Date.from(paymentVo.getSupplementaryTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
                chargeEndDate = Date.from(paymentVo.getSupplementaryTime().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
                cyclicOrSingle = "02";
                chargeSubjectPeriod = "01";
                //商业
                if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(supplementaryInfoVo.getProductType())){
                    chargeSubjectAmount = BigDecimal.valueOf(Double.parseDouble(paymentVo.getExpenseItemMoney()));
                }else{
                    chargeRuleNo = BaseRentCalculateEnum.FIXED_PRICE.getCode();
                    chargeRuleName = BaseRentCalculateEnum.FIXED_PRICE.getDesc();
                    paramList = (JSONObject) JSONObject.toJSON(new ChargeRuleSubParamsVo());
                    ChargeRuleSubParamsVo paramsVo = new ChargeRuleSubParamsVo();
                    paramsVo.setPARAMPRICE(paymentVo.getExpenseItemMoney());
                    paramValueList = (JSONObject) JSONObject.toJSON(paramsVo);
                }
            }
            String shareType;
            BigDecimal companyRate = null;
            BigDecimal companyAmount = null;
            if (SignTypeEnum.WHOLESALE_AGREEMENT.getCode().equals(supplementaryInfoVo.getSignType())) {
                // 管理协议
                if (PayModeEnum.COMPANY_PAY.getCode().equals(paymentVo.getPayMode())) {
                    shareType = "3";
                } else if (PayModeEnum.PERCENT_PAY.getCode().equals(paymentVo.getPayMode())) {
                    if (PayTypeEnum.PERCENT.getCode().equals(paymentVo.getPayType())) {
                        // 比例分摊
                        shareType = "1";
                        companyRate = new BigDecimal(PercentUtil.parsePercentToDouble(paymentVo.getPayPercent()));
                    } else {
                        // 金额分摊
                        shareType = "2";
                        companyAmount = new BigDecimal(paymentVo.getPayMoney());
                    }
                } else {
                    shareType = "4";
                }
            } else {
                // 散租
                shareType = "4";
            }
            BbpmTaxRateVo taxRateVo = createTaxRate(supplementaryInfoVo, paymentVo, contractManagementVo);
            ChargeSubjectParamsRequest chargeSubject = ChargeSubjectParamsRequest.builder()
                    .chargeStartDate(chargeStartDate)
                    .chargeEndDate(chargeEndDate)
                    .noCompleteType("01")
                    .chargeSubjectAmount(chargeSubjectAmount)
                    .amountType(amountType)
                    .chargeSubjectNo(paymentVo.getExpenseItem())
                    .taxRate(taxRateVo.getTaxRate())
                    .personalTaxRate(taxRateVo.getPersonalTaxRate())
                    .cyclicOrSingle(cyclicOrSingle)
                    .chargeSubjectPeriod(chargeSubjectPeriod)
                    .depositProportion(1)
                    .chargeRuleNo(chargeRuleNo)
                    .chargeRuleName(chargeRuleName)
                    .paramList(paramList)
                    .paramValueList(paramValueList)
                    .hasServiceFee(0)
                    .monthlyRentServiceFeeRatio(rentPercent)
                    .shareType(shareType)
                    .companyRate(companyRate)
                    .companyAmount(companyAmount)
                    .build();
            chargeSubjectList.add(chargeSubject);
        }
        roomVo.setChargeSubjectList(chargeSubjectList);
        return roomVo;
    }

    /**
     * 推送追加账单报文(试算)
     *
     * @param appendBillVo 追加账单推送报文 vo实体
     * @param parentId     上级id
     * @return 试算结果
     */
    private List<BbpmSupplementaryPreviewBillVo> tryAppendBill(BbpmAppendBillVo appendBillVo,
                                                               String parentId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<BbpmAppendBillVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(appendBillVo);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        String jsonRequest = JSON.toJSONString(parentRequest);
        log.info("3.62.试算追加账单接口-追加账单请求参数json:" + jsonRequest);
        ChargeRespondVo<List<Object>> result = null;
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("3.62.试算追加账单接口-追加账单, 请求参数(工银feign)：" + mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.tryAppendBill(parentRequest);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/appendBill/try";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("3.62.试算追加账单接口-追加账单,工银返回:" + JSONObject.toJSONString(result));
        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        BbpmSupplementaryTryBillVo tryBillVo = JSON.parseObject(JSON.toJSONString(result.getData().get(0)), BbpmSupplementaryTryBillVo.class);
        List<BbpmSupplementaryPreviewBillVo> previewBillList = new ArrayList<>();
        List<BbpmSupplementAppendBillVo> appendBillList = tryBillVo.getAppendBillList();
        for (BbpmSupplementAppendBillVo billVo : appendBillList) {
            BbpmSupplementaryPreviewBillVo previewBillVo = new BbpmSupplementaryPreviewBillVo();
            BeanUtils.copyProperties(tryBillVo, previewBillVo);
            BeanUtils.copyProperties(billVo, previewBillVo);
            previewBillVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
            previewBillVo.setParentId(parentId);
            // 类型转换
            previewBillVo.setPayableDate(DateUtils.parseDate(billVo.getPayableDate()));
            previewBillVo.setChargeSubjectBeginDate(DateUtils.parseDate(billVo.getChargeSubjectBeginDate()));
            previewBillVo.setChargeSubjectEndDate(DateUtils.parseDate(billVo.getChargeSubjectEndDate()));
            previewBillVo.setPayableMoney(Objects.toString(billVo.getPayableMoney(), null));
            previewBillVo.setNoTaxMoney(Objects.toString(billVo.getNoTaxMoney(), null));
            previewBillVo.setTaxRate(Objects.toString(billVo.getTaxRate(), null));
            previewBillVo.setRateMoney(Objects.toString(billVo.getRateMoney(), null));
            previewBillVo.setChargeSubjectPeriod(StringUtils.isBlank(billVo.getChargeSubjectPeriod()) ?
                    0 : Integer.parseInt(billVo.getChargeSubjectPeriod()));
            previewBillList.add(previewBillVo);
        }
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(previewBillList);
        return previewBillList;
    }

    /**
     * 推送追加账单报文
     *
     * @param appendBillVo 追加账单推送报文 vo实体
     */
    private void appendBill(BbpmAppendBillVo appendBillVo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<BbpmAppendBillVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(appendBillVo);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        String jsonRequest = JSON.toJSONString(parentRequest);
        log.info("3.63.追加账单接口-追加账单请求参数json:" + jsonRequest);
        ChargeRespondVo<Object> result = null;
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("3.63.追加账单接口-追加账单, 请求参数(工银feign)：" + mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.appendBill(parentRequest);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/appendBill";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("3.63.追加账单接口-追加账单,工银返回:" + JSONObject.toJSONString(result));
        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
    }

    /**
     * 从合同信息获取面积
     *
     * @param areaType        面积类型
     * @param subjectMatterVo 产品信息 vo实体
     * @return 面积
     */
    private String getArea(String areaType, BbctContractSubjectMatterVo subjectMatterVo) {
        String area;
        if (AreaTypeEnum.INNER_SLEEVE_AREA.getCode().equals(areaType)) {
            area = subjectMatterVo.getInnerSleeveArea();
        } else {
            area = subjectMatterVo.getHouseStructArea();
        }
        return area;
    }

    /**
     * 构建税率
     *
     * @param supplementaryInfoVo  追加单信息 vo实体
     * @param paymentVo            追加账单信息 vo实体
     * @param contractManagementVo 合同信息 vo实体
     * @return 税率 vo实体
     */
    private BbpmTaxRateVo createTaxRate(BbpmSupplementaryInfoVo supplementaryInfoVo,
                                        BbpmSupplementaryPaymentVo paymentVo,
                                        BbctContractManagementVo contractManagementVo) {
        TaxRateVo requestVo = new TaxRateVo();
        requestVo.setProjectId(supplementaryInfoVo.getProjectId());
        requestVo.setChargeItemId(paymentVo.getExpenseItem());
        if (ProductTypeEnum.PROTECT_TYPE.getCode().equals(supplementaryInfoVo.getProductType())) {
            requestVo.setYeTai(PaymentEnums.PROJECTFORMAT_BZ.getCode());
        } else {
            requestVo.setYeTai(PaymentEnums.PROJECTFORMAT_GZ.getCode());
        }
        List<BbctContractSignerVo> userList = contractManagementVo.getUserList();
        String companyId = null;
        for (BbctContractSignerVo userVo : userList) {
            if (CustomerTypeEnum.COMPANY.getCode().equals(userVo.getCustomerType())) {
                companyId = userVo.getCustomerNo();
                break;
            }
        }
        requestVo.setCompanyId(companyId);
        if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(supplementaryInfoVo.getSignType())) {
            requestVo.setTenantry(PaymentEnums.TENANTRY_THREE.getCode());
        } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(supplementaryInfoVo.getSignType())) {
            requestVo.setTenantry(PaymentEnums.TENANTRY_TWO.getCode());
        } else if (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(supplementaryInfoVo.getSignType())) {
            requestVo.setTenantry(PaymentEnums.TENANTRY_ONE.getCode());
        }
        log.info("合同contractId" + supplementaryInfoVo.getContractNo() + ",请求3.38查询税率配置列表接口传入参数:" + JSONObject.toJSONString(requestVo));
        TaxRateResultVo taxRateResultVo = iBbsiRuleInfoService.getTaxRateList(requestVo);
        BbpmTaxRateVo taxRateVo = new BbpmTaxRateVo();
        if (!Objects.isNull(taxRateResultVo)) {
            if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(supplementaryInfoVo.getSignType())) {
                if (StringUtils.isNotBlank(taxRateResultVo.getPersonTaxRate())) {
                    taxRateVo.setTaxRate(percentageStr(taxRateResultVo.getPersonTaxRate()));
                }
            } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(supplementaryInfoVo.getSignType())) {
                if (StringUtils.isNotBlank(taxRateResultVo.getCompanyTaxRate())) {
                    taxRateVo.setTaxRate(percentageStr(taxRateResultVo.getCompanyTaxRate()));
                }
            } else if (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(supplementaryInfoVo.getSignType())) {
                if (StringUtils.isNotBlank(taxRateResultVo.getCompanyTaxRate())) {
                    taxRateVo.setTaxRate(percentageStr(taxRateResultVo.getCompanyTaxRate()));
                }
                if (StringUtils.isNotBlank(taxRateResultVo.getPersonTaxRate())) {
                    taxRateVo.setPersonalTaxRate(percentageStr(taxRateResultVo.getPersonTaxRate()));
                }
            }
        } else {
            log.info("合同contractId" + supplementaryInfoVo.getContractNo() + ",请求3.38查询税率配置列表结果为空");
        }
        return taxRateVo;
    }

    private BigDecimal percentageStr(String percentageStr) {
        // 去除百分号并将字符串转换为数字
        double percentage = Double.parseDouble(percentageStr.replace("%", ""));
        // 将数字转换为BigDecimal并保留4位小数
        BigDecimal decimalValue = BigDecimal.valueOf(percentage / 100).setScale(4, BigDecimal.ROUND_HALF_UP);
        return decimalValue;
    }

    /**
     * 赋值账单周期字符串
     *
     * @param previewBillList 试算结果列表
     */
    private void setChargeSubjectPeriodStr(List<BbpmSupplementaryPreviewBillVo> previewBillList) {
        for (BbpmSupplementaryPreviewBillVo previewBillVo : previewBillList) {
            String chargeSubjectPeriodStr = String.format("第%s期（%s 至 %s）", previewBillVo.getChargeSubjectPeriod(),
                    DateUtils.formatDate(previewBillVo.getChargeSubjectBeginDate(), "yyyy/MM/dd"),
                    DateUtils.formatDate(previewBillVo.getChargeSubjectEndDate(), "yyyy/MM/dd"));
            previewBillVo.setChargeSubjectPeriodStr(chargeSubjectPeriodStr);
        }
    }

    /**
     * 构建试算结果map集
     *
     * @param previewBillList 试算结果列表
     * @return 试算结果map集
     */
    private Map<String, List<BbpmSupplementaryPreviewBillVo>> createPreviewBillMap(List<BbpmSupplementaryPreviewBillVo> previewBillList) {
        Map<String, List<BbpmSupplementaryPreviewBillVo>> resultMap = new HashMap<>();
        for (BbpmSupplementaryPreviewBillVo previewBillVo : previewBillList) {
            if (resultMap.containsKey(previewBillVo.getChargeSubject())) {
                resultMap.get(previewBillVo.getChargeSubject()).add(previewBillVo);
            } else {
                List<BbpmSupplementaryPreviewBillVo> tempList = new ArrayList<>();
                tempList.add(previewBillVo);
                resultMap.put(previewBillVo.getChargeSubject(), tempList);
            }
        }
        // 排序
        for (Map.Entry<String, List<BbpmSupplementaryPreviewBillVo>> entry : resultMap.entrySet()) {
            Collections.sort(entry.getValue(), new Comparator<BbpmSupplementaryPreviewBillVo>() {
                @Override
                public int compare(BbpmSupplementaryPreviewBillVo o1, BbpmSupplementaryPreviewBillVo o2) {
                    return Integer.compare(o1.getChargeSubjectPeriod(), o2.getChargeSubjectPeriod());
                }
            });
        }
        return resultMap;
    }
}
