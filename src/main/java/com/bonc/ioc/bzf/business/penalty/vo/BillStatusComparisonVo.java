package com.bonc.ioc.bzf.business.penalty.vo;

import java.util.ArrayList;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;


/**
 * 账单状态对比VO
 */
public class BillStatusComparisonVo {
    private final BbpmBillManagementPageResultVo bill;
    private final com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo existingTrialBill;
    private final String oldStatus;
    private final String newStatus;
    private final List<BbpmCollectionPageResultVo> currentPayments;
    
    public BillStatusComparisonVo(BbpmBillManagementPageResultVo bill,
                              com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo existingTrialBill,
                              String oldStatus,
                              String newStatus,
                              List<BbpmCollectionPageResultVo> currentPayments) {
        this.bill = bill;
        this.existingTrialBill = existingTrialBill;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.currentPayments = currentPayments != null ? currentPayments : new ArrayList<>();
    }
    
    public BbpmBillManagementPageResultVo getBill() { 
        return bill; 
    }
    
    public com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo getExistingTrialBill() { 
        return existingTrialBill; 
    }
    
    public String getOldStatus() { 
        return oldStatus; 
    }
    
    public String getNewStatus() { 
        return newStatus; 
    }
    
    public List<BbpmCollectionPageResultVo> getCurrentPayments() { 
        return currentPayments; 
    }
}
