package com.bonc.ioc.bzf.business.penalty.task;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.utils.JsonToObjectUtil;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageVo;
import com.bonc.ioc.bzf.business.penalty.enums.PenaltyApiType;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyApiCallLogService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeDailyDetailService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeStageSummaryService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyFeeTrialBillService;
import com.bonc.ioc.bzf.business.penalty.service.IBbpmPenaltyTaskStatusService;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyApiCallLogVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeDailyDetailVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeStageSummaryVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyFeeTrialBillVo;
import com.bonc.ioc.bzf.business.penalty.vo.BbpmPenaltyTaskStatusVo;
import com.bonc.ioc.bzf.business.penalty.vo.ContractInfoVo;
import com.bonc.ioc.bzf.business.penalty.vo.contractExtend.ContractOtherInfo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.feign.service.IBmsBaseServiceFeignService;

import lombok.extern.slf4j.Slf4j;

/**
 * 违约金数据查询和操作服务类
 * 负责所有数据库查询、API调用、数据转换和解析等操作
 */
@Slf4j
@Service
public class PenaltyDataService {

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;
    
    @Resource
    private IBmsBaseServiceFeignService bmsBaseServiceFeignService;

    @Resource 
    private BbctContractFeignClient contractFeignClient;
    
    @Resource
    private IBbpmPenaltyFeeTrialBillService penaltyFeeTrialBillService;
    
    @Resource
    private IBbpmPenaltyFeeDailyDetailService penaltyFeeDailyDetailService;
    
    @Resource
    private IBbpmPenaltyTaskStatusService penaltyTaskStatusService;
    
    @Resource
    private IBbpmPenaltyApiCallLogService penaltyApiCallLogService;
    
    @Resource
    private IBbpmPenaltyFeeStageSummaryService penaltyFeeStageSummaryService;

    @Resource
    private PenaltyCalculationService penaltyCalculationService;

    /**
     * 获取所有有效的商业合同（包含projectId和每日逾期率）
     */
    public List<ContractInfoVo> getAllActiveContracts() {
        List<ContractInfoVo> contractList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        String requestParamsJson = "";
        String responseBody = "";
        
        try {
            BbctContractManagementPageVo queryVo = new BbctContractManagementPageVo();
            queryVo.setIsProject("1"); // 不卡数据权限
            queryVo.setBusinessTypeCode1("02"); // 商业合同
            queryVo.setContractStatus("1"); // 已生效
            queryVo.setPageSize(1000);
            queryVo.setPageNumber(1);
            queryVo.setFindAll(true);
            
            requestParamsJson = JSONObject.toJSONString(queryVo);
            log.info("开始调用合同中心接口获取有效商业合同，请求参数：{}", requestParamsJson);
            
            AppReply<PageResult<List<BbctContractManagementPageResultVo>>> contractAppReply = 
                contractFeignClient.selectByPageCommon(queryVo);
            
            responseBody = JSONObject.toJSONString(contractAppReply);
            log.debug("合同中心接口响应：{}", responseBody);
            
            if (contractAppReply != null && contractAppReply.getData() != null 
                && contractAppReply.getData().getRows() != null) {
                
                List<BbctContractManagementPageResultVo> allContracts = contractAppReply.getData().getRows();
                log.info("合同中心接口返回合同总数：{}，开始筛选有每日逾期率的合同", allContracts.size());
                
                int validContractCount = 0;
                int invalidContractCount = 0;
                
                for (BbctContractManagementPageResultVo contract : allContracts) {
                    try {
                        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(contract.getContractExtend());
                        
                        // 只处理每日逾期率不为0的合同
                        if (contractOtherInfo != null && contractOtherInfo.getDailyOverdueRate() != null 
                            && contractOtherInfo.getDailyOverdueRate().compareTo(BigDecimal.ZERO) != 0) {
                            
                            // 获取projectId
                            String projectId = null;
                            if (contract.getSubjectMatterVos() != null && !contract.getSubjectMatterVos().isEmpty()) {
                                projectId = contract.getSubjectMatterVos().get(0).getProjectId();
                            }
                            
                            contractList.add(new ContractInfoVo(
                                contract.getContractNo(), 
                                projectId, 
                                contractOtherInfo.getDailyOverdueRate()
                            ));
                            validContractCount++;
                            
                            log.debug("添加有效合同：合同号={}，项目ID={}，每日逾期率={}", 
                                    contract.getContractNo(), projectId, contractOtherInfo.getDailyOverdueRate());
                        } else {
                            invalidContractCount++;
                            log.debug("跳过无效合同：合同号={}，原因：每日逾期率为空或为0", contract.getContractNo());
                        }
                    } catch (Exception e) {
                        log.error("处理合同 {} 时发生异常：{}", contract.getContractNo(), e.getMessage(), e);
                        invalidContractCount++;
                    }
                }
                
                log.info("合同筛选完成，有效合同数：{}，无效合同数：{}，耗时：{}ms", 
                        validContractCount, invalidContractCount, System.currentTimeMillis() - startTime);
                
            } else {
                log.warn("合同中心接口返回数据为空：contractAppReply={}", contractAppReply);
            }
            
            // 记录成功的API调用日志
            logApiCall(null, LocalDate.now(), null, null, PenaltyApiType.CONTRACT_QUERY.getCode(), 
                "/contract/selectByPageCommon", requestParamsJson, 
                responseBody.length() > 2000 ? responseBody.substring(0, 2000) + "..." : responseBody, 
                System.currentTimeMillis() - startTime, null);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            
            log.error("获取有效合同列表失败，耗时：{}ms，错误：{}", duration, errorMessage, e);
            
            // 记录失败的API调用日志
            logApiCall(null, LocalDate.now(), null, null, PenaltyApiType.CONTRACT_QUERY.getCode(),
                "/contract/selectByPageCommon", requestParamsJson, responseBody,
                duration, errorMessage);
        }
        
        return contractList;
    }

    /**
     * 查询合同指定类型的逾期账单（支持projectId和chargeOwner）
     */
    public PageResult<List<BbpmBillManagementPageResultVo>> queryBillsByContractAndType(String taskStatusId, 
            String contractNo, String projectId, String chargeOwner, LocalDate calculationDate) {
        long startTime = System.currentTimeMillis();
        String requestParamsJson = "";
        String responseBody = "";
        
        try {
            // 构建查询参数
            BbpmBillManagementPageVo queryVo = new BbpmBillManagementPageVo();
            queryVo.setContractCode(contractNo);
            // 设置projectId（新增）
            if (StringUtils.isNotBlank(projectId)) {
                queryVo.setProjectId(projectId);
            }
            // 设置账单类型（新增）
            queryVo.setChargeOwner(chargeOwner);
            // 设置payableEndDate为昨天日期（新增）
            LocalDate yesterday = calculationDate.minusDays(1);
            queryVo.setPayableEndDate(yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 不设置账单状态过滤，查询所有状态的账单
            queryVo.setFullPage("true");
            queryVo.setPageSize(100);
            queryVo.setPageNo(1);
            queryVo.setPrimaryChargeCodeFlag("1");
            
            // 构建请求对象
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<BbpmBillManagementPageVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(queryVo);
            
            requestParamsJson = JSONObject.toJSONString(parentRequest);
            log.info("调用工银账单接口请求参数（合同：{}，项目：{}，类型：{}，截止：{}）：{}", 
                    contractNo, projectId, chargeOwner, yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd")), requestParamsJson);
            
            // 调用工银账单查询接口
            responseBody = bfipChargeFeignClient.listByContract(parentRequest);
            log.debug("调用工银账单接口返回：{}", responseBody);
            
            // 解析响应结果
            PageResult<List<BbpmBillManagementPageResultVo>> pageResult = resetBillData(responseBody);
            
            // 记录成功调用日志
            logApiCall(taskStatusId, calculationDate, contractNo, null, PenaltyApiType.BILL_QUERY.getCode(), 
                "/charge/v1/bill/listByContract", requestParamsJson, 
                responseBody, System.currentTimeMillis() - startTime, null);
            
            int resultCount = (pageResult != null && pageResult.getRows() != null) ? pageResult.getRows().size() : 0;
            log.info("调用工银账单接口成功，合同：{}，项目：{}，类型：{}，返回逾期账单数：{}，耗时：{}ms", 
                    contractNo, projectId, chargeOwner, resultCount, System.currentTimeMillis() - startTime);
            
            return pageResult;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            
            // 记录失败调用日志
            logApiCall(taskStatusId, calculationDate, contractNo, null, PenaltyApiType.BILL_QUERY.getCode(),
                "/charge/v1/bill/listByContract", requestParamsJson, responseBody,
                duration, errorMessage);
            
            log.error("调用工银账单接口失败，合同：{}，项目：{}，类型：{}，错误：{}，耗时：{}ms", 
                    contractNo, projectId, chargeOwner, errorMessage, duration);
            
            return new PageResult<>(new ArrayList<>());
        }
    }

    /**
     * 查询账单的收款单信息（参考BbpmCollectionServiceImpl）
     */
    public PageResult<List<BbpmCollectionPageResultVo>> queryPaymentsByBillId(String taskStatusId, String billId, 
                                                                              LocalDate taskDate, String contractNo, String projectId) {
        long startTime = System.currentTimeMillis();
        String requestParamsJson = "";
        String responseBody = "";
        
        try {
            // 构建查询参数
            BbpmCollectionPageVo queryVo = new BbpmCollectionPageVo();
            queryVo.setBillId(billId);
            queryVo.setOptType("02"); // 01:详情 02:列表，默认详情
            queryVo.setFullPage("true");
            queryVo.setSize(1000);
            queryVo.setCurrent(1);
            // 设置projectId（新增）
            if (StringUtils.isNotBlank(projectId)) {
                queryVo.setProjectId(projectId);
            }
            
            // 构建请求对象
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<BbpmCollectionPageVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(queryVo);
            
            requestParamsJson = JSONObject.toJSONString(parentRequest);
            log.debug("调用工银收款单接口请求参数：{}", requestParamsJson);
            
            // 调用工银收款单查询接口
            responseBody = bfipChargeFeignClient.listByBill(parentRequest);
            log.debug("调用工银收款单接口返回：{}", responseBody);
            
            // 解析响应结果
            PageResult<List<BbpmCollectionPageResultVo>> pageResult = resetCollectionData(responseBody);
            
            // 记录成功调用日志
            logApiCall(taskStatusId, taskDate, contractNo, billId, PenaltyApiType.COLLECTION_QUERY.getCode(), 
                "/charge/v2/receipt/listByBill", requestParamsJson, 
                responseBody, System.currentTimeMillis() - startTime, null);
                
            int resultCount = (pageResult != null && pageResult.getRows() != null) ? pageResult.getRows().size() : 0;
            log.debug("调用工银收款单接口成功，账单：{}，返回收款单数：{}，耗时：{}ms",
                     billId, resultCount, System.currentTimeMillis() - startTime);
                
            return pageResult;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            
            // 记录失败调用日志
            logApiCall(taskStatusId, taskDate, contractNo, billId, PenaltyApiType.COLLECTION_QUERY.getCode(),
                "/charge/v2/receipt/listByBill", requestParamsJson, responseBody,
                duration, errorMessage);
                
            log.error("调用工银收款单接口失败，账单：{}，错误：{}，耗时：{}ms",
                     billId, errorMessage, duration);
            return new PageResult<>(new ArrayList<>());
        }
    }

    /**
     * 批量查询账单的收款单信息（优化：每个账单只查询一次）
     */
    public Map<String, List<BbpmCollectionPageResultVo>> batchQueryPaymentsForBills(
            String taskStatusId, 
            List<BbpmBillManagementPageResultVo> bills, 
            LocalDate calculationDate, 
            String contractNo, 
            String projectId) {
        
        Map<String, List<BbpmCollectionPageResultVo>> paymentsMap = new HashMap<>();
        
        for (BbpmBillManagementPageResultVo bill : bills) {
            try {
                PageResult<List<BbpmCollectionPageResultVo>> paymentsResult = 
                    queryPaymentsByBillId(taskStatusId, bill.getBillId(), calculationDate, bill.getContractNo(), projectId);
                
                List<BbpmCollectionPageResultVo> payments = 
                    (paymentsResult != null && paymentsResult.getRows() != null) ? 
                    paymentsResult.getRows() : new ArrayList<>();
                
                paymentsMap.put(bill.getBillId(), payments);
                
                log.debug("账单 {} 查询到 {} 个收款单", bill.getBillId(), payments.size());
                
            } catch (Exception e) {
                log.error("查询账单 {} 收款单信息失败：{}", bill.getBillId(), e.getMessage(), e);
                // 失败时存储空列表，避免后续处理时出现null
                paymentsMap.put(bill.getBillId(), new ArrayList<>());
            }
        }
        
        log.info("批量查询完成：合同 {}，共 {} 个账单，成功 {} 个", 
                contractNo, bills.size(), paymentsMap.size());
        
        return paymentsMap;
    }

    /**
     * 解析工银账单接口响应（参考BbpmBillManagementServiceImpl.resetBill）
     */
    public PageResult<List<BbpmBillManagementPageResultVo>> resetBillData(String responseBody) {
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmBillManagementPageResultVo> billListResultFaceHttpResultTwo = 
            JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            log.warn("工银账单接口返回错误：code={}, message={}", 
                    billListResultFaceHttpResultTwo.getCode(), billListResultFaceHttpResultTwo.getMessage());
            return new PageResult<>(new ArrayList<>());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(new ArrayList<>());
        }
        
        List<BbpmBillManagementPageResultVo> billListResultList = 
            JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(), BbpmBillManagementPageResultVo.class);
        List<BbpmBillManagementPageResultVo> result = new ArrayList<>();
        for(int i=0;i<billListResultList.size();i++){
            BbpmBillManagementPageResultVo bill = billListResultList.get(i);
             bill.setBillNo(bill.getBillId());
            bill.setContractNo(bill.getContractCode());
            //账单周期相关  第X期（YYYY/MM/DD - YYYY/MM/DD）
            bill.setBillCycle("第"+bill.getChargeSubjectPeriod()+"期("+bill.getChargeSubjectBeginDate()+"至"+bill.getChargeSubjectEndDate()+")");
            result.add(bill);
        }
        
        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(), result);
    }

    /**
     * 解析工银收款单接口响应（参考BbpmCollectionServiceImpl.resetCollection）
     */
    public PageResult<List<BbpmCollectionPageResultVo>> resetCollectionData(String responseBody) {
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmCollectionPageResultVo> billListResultFaceHttpResultTwo = 
            JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            log.warn("工银收款单接口返回错误：code={}, message={}", 
                    billListResultFaceHttpResultTwo.getCode(), billListResultFaceHttpResultTwo.getMessage());
            return new PageResult<>(new ArrayList<>());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(new ArrayList<>());
        }
        
        List<BbpmCollectionPageResultVo> collectionListResultList = 
            JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(), BbpmCollectionPageResultVo.class);

        // 注意：这里简化处理，不做复杂的数据转换，因为违约金计算只需要基本信息
        // 如果需要完整的数据转换，可以参考BbpmCollectionServiceImpl.resetCollection的完整逻辑
        
        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(), collectionListResultList);
    }

    /**
     * 分析对账状态
     */
    public String analyzeReconciliationStatus(List<BbpmCollectionPageResultVo> payments) {
        if (payments.isEmpty()) {
            return "02"; // 没有收款单，返回未对平
        }
        
        // 直接使用reconciliationResult字段判断对账状态
        // 优先级：01对平 > 03待对账 > 02未对平
        boolean hasReconciled = payments.stream()
            .anyMatch(payment -> "01".equals(payment.getReconciliationResult())); // 01-对平
        
        if (hasReconciled) {
            return "01"; // 对平
        }
        
        boolean hasPending = payments.stream()
            .anyMatch(payment -> "03".equals(payment.getReconciliationResult())); // 03-待对账
            
        if (hasPending) {
            return "03"; // 待对账
        }
        
        return "02"; // 未对平
    }

    /**
     * 根据billId查询每日明细记录
     */
    public List<BbpmPenaltyFeeDailyDetailVo> selectDailyDetailsByBillId(String billId) {
        try {
            // 直接查询每日明细记录
            BbpmPenaltyFeeDailyDetailVo queryVo = new BbpmPenaltyFeeDailyDetailVo();
            queryVo.setBillId(billId);
            
            return penaltyFeeDailyDetailService.selectListByCondition(queryVo);
            
        } catch (Exception e) {
            log.error("查询每日明细记录失败：billId={}", billId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据billId查询试算账单
     */
    public BbpmPenaltyFeeTrialBillVo selectTrialBillByBillId(String billId) {
        try {
            // 直接查询试算账单
            List<BbpmPenaltyFeeTrialBillVo> trialBills = queryTrialBillList(null, billId);
            return trialBills.isEmpty() ? null : trialBills.get(0);
        } catch (Exception e) {
            log.error("查询试算账单失败：billId={}", billId, e);
            return null;
        }
    }

    /**
     * 根据trialBillId查询分阶段汇总记录
     */
    public List<BbpmPenaltyFeeStageSummaryVo> selectStageSummaryByTrialBillId(String trialBillId) {
        try {
            // 直接查询阶段汇总记录
            BbpmPenaltyFeeStageSummaryVo queryVo = new BbpmPenaltyFeeStageSummaryVo();
            queryVo.setTrialBillId(trialBillId);
            
            return penaltyFeeStageSummaryService.selectListByCondition(queryVo);
        } catch (Exception e) {
            log.error("查询分阶段汇总记录失败：trialBillId={}", trialBillId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询从指定日期开始的每日明细记录
     */
    public List<BbpmPenaltyFeeDailyDetailVo> selectDailyDetailsFromDate(String billId, LocalDate fromDate) {
        try {
            // 先查询该账单的所有每日明细记录
            List<BbpmPenaltyFeeDailyDetailVo> allDetails = selectDailyDetailsByBillId(billId);
            
            // 筛选出从指定日期开始的正常计算记录
            return allDetails.stream()
                .filter(detail -> "1".equals(detail.getEntryType())) // 只处理正常计算记录
                .filter(detail -> detail.getCalculationDate() != null)
                .filter(detail -> !detail.getCalculationDate().isBefore(fromDate)) // 从指定日期开始
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("查询从日期 {} 开始的每日明细记录失败：billId={}", fromDate, billId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询任务状态记录（不使用分页框架）
     */
    public List<BbpmPenaltyTaskStatusVo> queryTaskStatusList(LocalDate taskDate, String contractCode, String processStatus) {
        try {
            BbpmPenaltyTaskStatusVo queryVo = new BbpmPenaltyTaskStatusVo();
            if (taskDate != null) {
                queryVo.setTaskDate(taskDate);
            }
            if (StringUtils.isNotBlank(contractCode)) {
                queryVo.setContractCode(contractCode);
            }
            if (StringUtils.isNotBlank(processStatus)) {
                queryVo.setProcessStatus(processStatus);
            }
            
            return penaltyTaskStatusService.selectListByCondition(queryVo);
        } catch (Exception e) {
            log.error("查询任务状态记录失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询试算账单记录（不使用分页框架）
     */
    public List<BbpmPenaltyFeeTrialBillVo> queryTrialBillList(String contractCode, String billId) {
        try {
            BbpmPenaltyFeeTrialBillVo queryVo = new BbpmPenaltyFeeTrialBillVo();
            if (StringUtils.isNotBlank(contractCode)) {
                queryVo.setContractCode(contractCode);
            }
            if (StringUtils.isNotBlank(billId)) {
                queryVo.setBillId(billId);
            }
            
            return penaltyFeeTrialBillService.selectListByCondition(queryVo);
        } catch (Exception e) {
            log.error("查询试算账单记录失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 记录API调用日志
     */
    public void logApiCall(String taskStatusId, LocalDate taskDate, String contractCode, 
                           String billId, String apiType, String apiUrl, 
                           String requestParams, String responseData, long duration, String errorMessage) {
        try {
            BbpmPenaltyApiCallLogVo logVo = new BbpmPenaltyApiCallLogVo();
            logVo.setTaskStatusId(taskStatusId);
            logVo.setTaskDate(taskDate);
            logVo.setContractCode(contractCode);
            logVo.setBillId(billId);
            logVo.setApiType(apiType);
            logVo.setApiUrl(apiUrl);
            logVo.setRequestParams(requestParams);
            logVo.setResponseData(responseData);
            logVo.setCallDuration((int) duration);
            logVo.setCallStatus(StringUtils.isBlank(errorMessage) ? "1" : "2");
            logVo.setErrorMessage(errorMessage);
            logVo.setCallTime(new Date());
            logVo.setDelFlag(1);
            
            penaltyApiCallLogService.insertRecord(logVo);
        } catch (Exception e) {
            log.error("记录API调用日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 获取固定用户token
     */
    public String getTokenByFixedUser(){
        String token = bmsBaseServiceFeignService.getToken();
        return token;
    }

    /**
     * 记录对账状态变化到API调用日志
     */
    public void recordReconciliationStatusChange(String billId, String oldStatus, String newStatus) {
        try {
            BbpmPenaltyApiCallLogVo logVo = new BbpmPenaltyApiCallLogVo();
            logVo.setTaskDate(LocalDate.now());
            logVo.setBillId(billId);
            logVo.setApiType(PenaltyApiType.STATUS_CHANGE.getCode()); // 状态变化记录
            logVo.setApiUrl("RECONCILIATION_STATUS_CHANGE");
            logVo.setRequestParams(String.format("{\"oldStatus\":\"%s\"}", oldStatus));
            logVo.setResponseData(String.format("{\"newStatus\":\"%s\"}", newStatus));
            logVo.setCallDuration(0);
            logVo.setCallStatus("1");
            logVo.setCallTime(new Date());
            logVo.setDelFlag(1);
            
            penaltyApiCallLogService.insertRecord(logVo);
            
        } catch (Exception e) {
            log.error("记录对账状态变化日志失败：{}", e.getMessage(), e);
        }
    }
}
